"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/keys/route";
exports.ids = ["app/api/admin/keys/route"];
exports.modules = {

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("better-sqlite3");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fkeys%2Froute&page=%2Fapi%2Fadmin%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fkeys%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fkeys%2Froute&page=%2Fapi%2Fadmin%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fkeys%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var D_reader_auto_main_app_api_admin_keys_route_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./app/api/admin/keys/route.js */ \"(rsc)/./app/api/admin/keys/route.js\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/keys/route\",\n        pathname: \"/api/admin/keys\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/keys/route\"\n    },\n    resolvedPagePath: \"D:\\\\reader-auto-main\\\\app\\\\api\\\\admin\\\\keys\\\\route.js\",\n    nextConfigOutput,\n    userland: D_reader_auto_main_app_api_admin_keys_route_js__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/admin/keys/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fkeys%2Froute&page=%2Fapi%2Fadmin%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fkeys%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/admin/keys/route.js":
/*!*************************************!*\
  !*** ./app/api/admin/keys/route.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../lib/database */ \"(rsc)/./lib/database.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_lib_database__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../lib/auth */ \"(rsc)/./lib/auth.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_lib_auth__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nasync function GET(request) {\n    try {\n        const auth = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getAuthManager)();\n        // Check authentication and admin role\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"No token provided\"\n            }, {\n                status: 401\n            });\n        }\n        const token = authHeader.substring(7);\n        const session = auth.validateSession(token);\n        if (session.role !== \"admin\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const db = (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n        // Get query parameters\n        const url = new URL(request.url);\n        const limit = parseInt(url.searchParams.get(\"limit\")) || 50;\n        const offset = parseInt(url.searchParams.get(\"offset\")) || 0;\n        const keys = db.getLicenseKeys(limit, offset);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            keys,\n            pagination: {\n                limit,\n                offset,\n                hasMore: keys.length === limit\n            }\n        });\n    } catch (error) {\n        console.error(\"Get keys error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: error.message\n        }, {\n            status: error.message === \"Invalid session\" ? 401 : 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const auth = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getAuthManager)();\n        // Check authentication and admin role\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"No token provided\"\n            }, {\n                status: 401\n            });\n        }\n        const token = authHeader.substring(7);\n        const session = auth.validateSession(token);\n        if (session.role !== \"admin\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const { duration, maxUses, priority_level, max_accounts_per_batch, scheduling_access } = await request.json();\n        // Validate input\n        if (!duration || duration < 1) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Duration must be at least 1 day\"\n            }, {\n                status: 400\n            });\n        }\n        if (!maxUses || maxUses < 1) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Max uses must be at least 1\"\n            }, {\n                status: 400\n            });\n        }\n        if (priority_level < 0 || priority_level > 10) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Priority level must be between 0 and 10\"\n            }, {\n                status: 400\n            });\n        }\n        if (max_accounts_per_batch < 0 || max_accounts_per_batch > 1000) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Max accounts per batch must be between 0 and 1000\"\n            }, {\n                status: 400\n            });\n        }\n        const db = (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n        // Create license key\n        const keyData = db.createLicenseKey(parseInt(duration), parseInt(maxUses), [], session.userId);\n        // Set license features\n        db.setLicenseFeatures(keyData.id, {\n            max_accounts_per_batch: parseInt(max_accounts_per_batch),\n            priority_level: parseInt(priority_level),\n            scheduling_access: scheduling_access\n        });\n        // Log activity\n        db.logActivity(session.userId, \"CREATE_LICENSE_KEY\", `Created license key: ${keyData.keyCode} (${duration} days, ${maxUses} uses)`, auth.getClientIP(request));\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            key: keyData,\n            message: \"License key created successfully\"\n        });\n    } catch (error) {\n        console.error(\"Create key error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: error.message\n        }, {\n            status: error.message === \"Invalid session\" ? 401 : 500\n        });\n    }\n}\nasync function PATCH(request) {\n    try {\n        const auth = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.getAuthManager)();\n        // Check authentication and admin role\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"No token provided\"\n            }, {\n                status: 401\n            });\n        }\n        const token = authHeader.substring(7);\n        const session = auth.validateSession(token);\n        if (session.role !== \"admin\") {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const { keyId, action } = await request.json();\n        if (!keyId || !action) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Key ID and action are required\"\n            }, {\n                status: 400\n            });\n        }\n        if (action === \"deactivate\") {\n            const db = (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getDatabase)();\n            // Deactivate license key\n            const result = db.deactivateLicenseKey(keyId);\n            if (result.changes === 0) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    success: false,\n                    error: \"License key not found\"\n                }, {\n                    status: 404\n                });\n            }\n            // Log activity\n            db.logActivity(session.userId, \"DEACTIVATE_LICENSE_KEY\", `Deactivated license key ID: ${keyId}`, auth.getClientIP(request));\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                message: \"License key deactivated successfully\"\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: \"Invalid action\"\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error(\"Deactivate key error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: error.message\n        }, {\n            status: error.message === \"Invalid session\" ? 401 : 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2FkbWluL2tleXMvcm91dGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBMkM7QUFDWTtBQUNEO0FBRS9DLGVBQWVHLElBQUlDLE9BQU87SUFDL0IsSUFBSTtRQUNGLE1BQU1DLE9BQU9ILHlEQUFjQTtRQUUzQixzQ0FBc0M7UUFDdEMsTUFBTUksYUFBYUYsUUFBUUcsT0FBTyxDQUFDQyxHQUFHLENBQUM7UUFDdkMsSUFBSSxDQUFDRixjQUFjLENBQUNBLFdBQVdHLFVBQVUsQ0FBQyxZQUFZO1lBQ3BELE9BQU9ULGtGQUFZQSxDQUFDVSxJQUFJLENBQ3RCO2dCQUFFQyxTQUFTO2dCQUFPQyxPQUFPO1lBQW9CLEdBQzdDO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxNQUFNQyxRQUFRUixXQUFXUyxTQUFTLENBQUM7UUFDbkMsTUFBTUMsVUFBVVgsS0FBS1ksZUFBZSxDQUFDSDtRQUVyQyxJQUFJRSxRQUFRRSxJQUFJLEtBQUssU0FBUztZQUM1QixPQUFPbEIsa0ZBQVlBLENBQUNVLElBQUksQ0FDdEI7Z0JBQUVDLFNBQVM7Z0JBQU9DLE9BQU87WUFBd0IsR0FDakQ7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLE1BQU1NLEtBQUtsQiwwREFBV0E7UUFFdEIsdUJBQXVCO1FBQ3ZCLE1BQU1tQixNQUFNLElBQUlDLElBQUlqQixRQUFRZ0IsR0FBRztRQUMvQixNQUFNRSxRQUFRQyxTQUFTSCxJQUFJSSxZQUFZLENBQUNoQixHQUFHLENBQUMsYUFBYTtRQUN6RCxNQUFNaUIsU0FBU0YsU0FBU0gsSUFBSUksWUFBWSxDQUFDaEIsR0FBRyxDQUFDLGNBQWM7UUFFM0QsTUFBTWtCLE9BQU9QLEdBQUdRLGNBQWMsQ0FBQ0wsT0FBT0c7UUFFdEMsT0FBT3pCLGtGQUFZQSxDQUFDVSxJQUFJLENBQUM7WUFDdkJDLFNBQVM7WUFDVGU7WUFDQUUsWUFBWTtnQkFDVk47Z0JBQ0FHO2dCQUNBSSxTQUFTSCxLQUFLSSxNQUFNLEtBQUtSO1lBQzNCO1FBQ0Y7SUFFRixFQUFFLE9BQU9WLE9BQU87UUFDZG1CLFFBQVFuQixLQUFLLENBQUMsbUJBQW1CQTtRQUNqQyxPQUFPWixrRkFBWUEsQ0FBQ1UsSUFBSSxDQUN0QjtZQUFFQyxTQUFTO1lBQU9DLE9BQU9BLE1BQU1vQixPQUFPO1FBQUMsR0FDdkM7WUFBRW5CLFFBQVFELE1BQU1vQixPQUFPLEtBQUssb0JBQW9CLE1BQU07UUFBSTtJQUU5RDtBQUNGO0FBRU8sZUFBZUMsS0FBSzdCLE9BQU87SUFDaEMsSUFBSTtRQUNGLE1BQU1DLE9BQU9ILHlEQUFjQTtRQUUzQixzQ0FBc0M7UUFDdEMsTUFBTUksYUFBYUYsUUFBUUcsT0FBTyxDQUFDQyxHQUFHLENBQUM7UUFDdkMsSUFBSSxDQUFDRixjQUFjLENBQUNBLFdBQVdHLFVBQVUsQ0FBQyxZQUFZO1lBQ3BELE9BQU9ULGtGQUFZQSxDQUFDVSxJQUFJLENBQ3RCO2dCQUFFQyxTQUFTO2dCQUFPQyxPQUFPO1lBQW9CLEdBQzdDO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxNQUFNQyxRQUFRUixXQUFXUyxTQUFTLENBQUM7UUFDbkMsTUFBTUMsVUFBVVgsS0FBS1ksZUFBZSxDQUFDSDtRQUVyQyxJQUFJRSxRQUFRRSxJQUFJLEtBQUssU0FBUztZQUM1QixPQUFPbEIsa0ZBQVlBLENBQUNVLElBQUksQ0FDdEI7Z0JBQUVDLFNBQVM7Z0JBQU9DLE9BQU87WUFBd0IsR0FDakQ7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLE1BQU0sRUFBRXFCLFFBQVEsRUFBRUMsT0FBTyxFQUFFQyxjQUFjLEVBQUVDLHNCQUFzQixFQUFFQyxpQkFBaUIsRUFBRSxHQUFHLE1BQU1sQyxRQUFRTSxJQUFJO1FBRTNHLGlCQUFpQjtRQUNqQixJQUFJLENBQUN3QixZQUFZQSxXQUFXLEdBQUc7WUFDN0IsT0FBT2xDLGtGQUFZQSxDQUFDVSxJQUFJLENBQ3RCO2dCQUFFQyxTQUFTO2dCQUFPQyxPQUFPO1lBQWtDLEdBQzNEO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxJQUFJLENBQUNzQixXQUFXQSxVQUFVLEdBQUc7WUFDM0IsT0FBT25DLGtGQUFZQSxDQUFDVSxJQUFJLENBQ3RCO2dCQUFFQyxTQUFTO2dCQUFPQyxPQUFPO1lBQThCLEdBQ3ZEO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxJQUFJdUIsaUJBQWlCLEtBQUtBLGlCQUFpQixJQUFJO1lBQzdDLE9BQU9wQyxrRkFBWUEsQ0FBQ1UsSUFBSSxDQUN0QjtnQkFBRUMsU0FBUztnQkFBT0MsT0FBTztZQUEwQyxHQUNuRTtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsSUFBSXdCLHlCQUF5QixLQUFLQSx5QkFBeUIsTUFBTTtZQUMvRCxPQUFPckMsa0ZBQVlBLENBQUNVLElBQUksQ0FDdEI7Z0JBQUVDLFNBQVM7Z0JBQU9DLE9BQU87WUFBb0QsR0FDN0U7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLE1BQU1NLEtBQUtsQiwwREFBV0E7UUFFdEIscUJBQXFCO1FBQ3JCLE1BQU1zQyxVQUFVcEIsR0FBR3FCLGdCQUFnQixDQUNqQ2pCLFNBQVNXLFdBQ1RYLFNBQVNZLFVBQ1QsRUFBRSxFQUNGbkIsUUFBUXlCLE1BQU07UUFHaEIsdUJBQXVCO1FBQ3ZCdEIsR0FBR3VCLGtCQUFrQixDQUFDSCxRQUFRSSxFQUFFLEVBQUU7WUFDaENOLHdCQUF3QmQsU0FBU2M7WUFDakNELGdCQUFnQmIsU0FBU2E7WUFDekJFLG1CQUFtQkE7UUFDckI7UUFFQSxlQUFlO1FBQ2ZuQixHQUFHeUIsV0FBVyxDQUNaNUIsUUFBUXlCLE1BQU0sRUFDZCxzQkFDQSxDQUFDLHFCQUFxQixFQUFFRixRQUFRTSxPQUFPLENBQUMsRUFBRSxFQUFFWCxTQUFTLE9BQU8sRUFBRUMsUUFBUSxNQUFNLENBQUMsRUFDN0U5QixLQUFLeUMsV0FBVyxDQUFDMUM7UUFHbkIsT0FBT0osa0ZBQVlBLENBQUNVLElBQUksQ0FBQztZQUN2QkMsU0FBUztZQUNUb0MsS0FBS1I7WUFDTFAsU0FBUztRQUNYO0lBRUYsRUFBRSxPQUFPcEIsT0FBTztRQUNkbUIsUUFBUW5CLEtBQUssQ0FBQyxxQkFBcUJBO1FBQ25DLE9BQU9aLGtGQUFZQSxDQUFDVSxJQUFJLENBQ3RCO1lBQUVDLFNBQVM7WUFBT0MsT0FBT0EsTUFBTW9CLE9BQU87UUFBQyxHQUN2QztZQUFFbkIsUUFBUUQsTUFBTW9CLE9BQU8sS0FBSyxvQkFBb0IsTUFBTTtRQUFJO0lBRTlEO0FBQ0Y7QUFFTyxlQUFlZ0IsTUFBTTVDLE9BQU87SUFDakMsSUFBSTtRQUNGLE1BQU1DLE9BQU9ILHlEQUFjQTtRQUUzQixzQ0FBc0M7UUFDdEMsTUFBTUksYUFBYUYsUUFBUUcsT0FBTyxDQUFDQyxHQUFHLENBQUM7UUFDdkMsSUFBSSxDQUFDRixjQUFjLENBQUNBLFdBQVdHLFVBQVUsQ0FBQyxZQUFZO1lBQ3BELE9BQU9ULGtGQUFZQSxDQUFDVSxJQUFJLENBQ3RCO2dCQUFFQyxTQUFTO2dCQUFPQyxPQUFPO1lBQW9CLEdBQzdDO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxNQUFNQyxRQUFRUixXQUFXUyxTQUFTLENBQUM7UUFDbkMsTUFBTUMsVUFBVVgsS0FBS1ksZUFBZSxDQUFDSDtRQUVyQyxJQUFJRSxRQUFRRSxJQUFJLEtBQUssU0FBUztZQUM1QixPQUFPbEIsa0ZBQVlBLENBQUNVLElBQUksQ0FDdEI7Z0JBQUVDLFNBQVM7Z0JBQU9DLE9BQU87WUFBd0IsR0FDakQ7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLE1BQU0sRUFBRW9DLEtBQUssRUFBRUMsTUFBTSxFQUFFLEdBQUcsTUFBTTlDLFFBQVFNLElBQUk7UUFFNUMsSUFBSSxDQUFDdUMsU0FBUyxDQUFDQyxRQUFRO1lBQ3JCLE9BQU9sRCxrRkFBWUEsQ0FBQ1UsSUFBSSxDQUN0QjtnQkFBRUMsU0FBUztnQkFBT0MsT0FBTztZQUFpQyxHQUMxRDtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsSUFBSXFDLFdBQVcsY0FBYztZQUMzQixNQUFNL0IsS0FBS2xCLDBEQUFXQTtZQUV0Qix5QkFBeUI7WUFDekIsTUFBTWtELFNBQVNoQyxHQUFHaUMsb0JBQW9CLENBQUNIO1lBRXZDLElBQUlFLE9BQU9FLE9BQU8sS0FBSyxHQUFHO2dCQUN4QixPQUFPckQsa0ZBQVlBLENBQUNVLElBQUksQ0FDdEI7b0JBQUVDLFNBQVM7b0JBQU9DLE9BQU87Z0JBQXdCLEdBQ2pEO29CQUFFQyxRQUFRO2dCQUFJO1lBRWxCO1lBRUEsZUFBZTtZQUNmTSxHQUFHeUIsV0FBVyxDQUNaNUIsUUFBUXlCLE1BQU0sRUFDZCwwQkFDQSxDQUFDLDRCQUE0QixFQUFFUSxNQUFNLENBQUMsRUFDdEM1QyxLQUFLeUMsV0FBVyxDQUFDMUM7WUFHbkIsT0FBT0osa0ZBQVlBLENBQUNVLElBQUksQ0FBQztnQkFDdkJDLFNBQVM7Z0JBQ1RxQixTQUFTO1lBQ1g7UUFDRjtRQUVBLE9BQU9oQyxrRkFBWUEsQ0FBQ1UsSUFBSSxDQUN0QjtZQUFFQyxTQUFTO1lBQU9DLE9BQU87UUFBaUIsR0FDMUM7WUFBRUMsUUFBUTtRQUFJO0lBR2xCLEVBQUUsT0FBT0QsT0FBTztRQUNkbUIsUUFBUW5CLEtBQUssQ0FBQyx5QkFBeUJBO1FBQ3ZDLE9BQU9aLGtGQUFZQSxDQUFDVSxJQUFJLENBQ3RCO1lBQUVDLFNBQVM7WUFBT0MsT0FBT0EsTUFBTW9CLE9BQU87UUFBQyxHQUN2QztZQUFFbkIsUUFBUUQsTUFBTW9CLE9BQU8sS0FBSyxvQkFBb0IsTUFBTTtRQUFJO0lBRTlEO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFkZXItYXV0by8uL2FwcC9hcGkvYWRtaW4va2V5cy9yb3V0ZS5qcz8wNWE3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJztcbmltcG9ydCB7IGdldERhdGFiYXNlIH0gZnJvbSAnLi4vLi4vLi4vLi4vbGliL2RhdGFiYXNlJztcbmltcG9ydCB7IGdldEF1dGhNYW5hZ2VyIH0gZnJvbSAnLi4vLi4vLi4vLi4vbGliL2F1dGgnO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKHJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBhdXRoID0gZ2V0QXV0aE1hbmFnZXIoKTtcbiAgICBcbiAgICAvLyBDaGVjayBhdXRoZW50aWNhdGlvbiBhbmQgYWRtaW4gcm9sZVxuICAgIGNvbnN0IGF1dGhIZWFkZXIgPSByZXF1ZXN0LmhlYWRlcnMuZ2V0KCdhdXRob3JpemF0aW9uJyk7XG4gICAgaWYgKCFhdXRoSGVhZGVyIHx8ICFhdXRoSGVhZGVyLnN0YXJ0c1dpdGgoJ0JlYXJlciAnKSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ05vIHRva2VuIHByb3ZpZGVkJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAxIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgY29uc3QgdG9rZW4gPSBhdXRoSGVhZGVyLnN1YnN0cmluZyg3KTtcbiAgICBjb25zdCBzZXNzaW9uID0gYXV0aC52YWxpZGF0ZVNlc3Npb24odG9rZW4pO1xuXG4gICAgaWYgKHNlc3Npb24ucm9sZSAhPT0gJ2FkbWluJykge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0FkbWluIGFjY2VzcyByZXF1aXJlZCcgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMyB9XG4gICAgICApO1xuICAgIH1cblxuICAgIGNvbnN0IGRiID0gZ2V0RGF0YWJhc2UoKTtcbiAgICBcbiAgICAvLyBHZXQgcXVlcnkgcGFyYW1ldGVyc1xuICAgIGNvbnN0IHVybCA9IG5ldyBVUkwocmVxdWVzdC51cmwpO1xuICAgIGNvbnN0IGxpbWl0ID0gcGFyc2VJbnQodXJsLnNlYXJjaFBhcmFtcy5nZXQoJ2xpbWl0JykpIHx8IDUwO1xuICAgIGNvbnN0IG9mZnNldCA9IHBhcnNlSW50KHVybC5zZWFyY2hQYXJhbXMuZ2V0KCdvZmZzZXQnKSkgfHwgMDtcblxuICAgIGNvbnN0IGtleXMgPSBkYi5nZXRMaWNlbnNlS2V5cyhsaW1pdCwgb2Zmc2V0KTtcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAga2V5cyxcbiAgICAgIHBhZ2luYXRpb246IHtcbiAgICAgICAgbGltaXQsXG4gICAgICAgIG9mZnNldCxcbiAgICAgICAgaGFzTW9yZToga2V5cy5sZW5ndGggPT09IGxpbWl0XG4gICAgICB9XG4gICAgfSk7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdHZXQga2V5cyBlcnJvcjonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfSxcbiAgICAgIHsgc3RhdHVzOiBlcnJvci5tZXNzYWdlID09PSAnSW52YWxpZCBzZXNzaW9uJyA/IDQwMSA6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgYXV0aCA9IGdldEF1dGhNYW5hZ2VyKCk7XG4gICAgXG4gICAgLy8gQ2hlY2sgYXV0aGVudGljYXRpb24gYW5kIGFkbWluIHJvbGVcbiAgICBjb25zdCBhdXRoSGVhZGVyID0gcmVxdWVzdC5oZWFkZXJzLmdldCgnYXV0aG9yaXphdGlvbicpO1xuICAgIGlmICghYXV0aEhlYWRlciB8fCAhYXV0aEhlYWRlci5zdGFydHNXaXRoKCdCZWFyZXIgJykpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdObyB0b2tlbiBwcm92aWRlZCcgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMSB9XG4gICAgICApO1xuICAgIH1cblxuICAgIGNvbnN0IHRva2VuID0gYXV0aEhlYWRlci5zdWJzdHJpbmcoNyk7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IGF1dGgudmFsaWRhdGVTZXNzaW9uKHRva2VuKTtcblxuICAgIGlmIChzZXNzaW9uLnJvbGUgIT09ICdhZG1pbicpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdBZG1pbiBhY2Nlc3MgcmVxdWlyZWQnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDMgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICBjb25zdCB7IGR1cmF0aW9uLCBtYXhVc2VzLCBwcmlvcml0eV9sZXZlbCwgbWF4X2FjY291bnRzX3Blcl9iYXRjaCwgc2NoZWR1bGluZ19hY2Nlc3MgfSA9IGF3YWl0IHJlcXVlc3QuanNvbigpO1xuXG4gICAgLy8gVmFsaWRhdGUgaW5wdXRcbiAgICBpZiAoIWR1cmF0aW9uIHx8IGR1cmF0aW9uIDwgMSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0R1cmF0aW9uIG11c3QgYmUgYXQgbGVhc3QgMSBkYXknIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICBpZiAoIW1heFVzZXMgfHwgbWF4VXNlcyA8IDEpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdNYXggdXNlcyBtdXN0IGJlIGF0IGxlYXN0IDEnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICBpZiAocHJpb3JpdHlfbGV2ZWwgPCAwIHx8IHByaW9yaXR5X2xldmVsID4gMTApIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdQcmlvcml0eSBsZXZlbCBtdXN0IGJlIGJldHdlZW4gMCBhbmQgMTAnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICBpZiAobWF4X2FjY291bnRzX3Blcl9iYXRjaCA8IDAgfHwgbWF4X2FjY291bnRzX3Blcl9iYXRjaCA+IDEwMDApIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdNYXggYWNjb3VudHMgcGVyIGJhdGNoIG11c3QgYmUgYmV0d2VlbiAwIGFuZCAxMDAwJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgY29uc3QgZGIgPSBnZXREYXRhYmFzZSgpO1xuICAgIFxuICAgIC8vIENyZWF0ZSBsaWNlbnNlIGtleVxuICAgIGNvbnN0IGtleURhdGEgPSBkYi5jcmVhdGVMaWNlbnNlS2V5KFxuICAgICAgcGFyc2VJbnQoZHVyYXRpb24pLFxuICAgICAgcGFyc2VJbnQobWF4VXNlcyksXG4gICAgICBbXSxcbiAgICAgIHNlc3Npb24udXNlcklkXG4gICAgKTtcblxuICAgIC8vIFNldCBsaWNlbnNlIGZlYXR1cmVzXG4gICAgZGIuc2V0TGljZW5zZUZlYXR1cmVzKGtleURhdGEuaWQsIHtcbiAgICAgIG1heF9hY2NvdW50c19wZXJfYmF0Y2g6IHBhcnNlSW50KG1heF9hY2NvdW50c19wZXJfYmF0Y2gpLFxuICAgICAgcHJpb3JpdHlfbGV2ZWw6IHBhcnNlSW50KHByaW9yaXR5X2xldmVsKSxcbiAgICAgIHNjaGVkdWxpbmdfYWNjZXNzOiBzY2hlZHVsaW5nX2FjY2Vzc1xuICAgIH0pO1xuXG4gICAgLy8gTG9nIGFjdGl2aXR5XG4gICAgZGIubG9nQWN0aXZpdHkoXG4gICAgICBzZXNzaW9uLnVzZXJJZCxcbiAgICAgICdDUkVBVEVfTElDRU5TRV9LRVknLFxuICAgICAgYENyZWF0ZWQgbGljZW5zZSBrZXk6ICR7a2V5RGF0YS5rZXlDb2RlfSAoJHtkdXJhdGlvbn0gZGF5cywgJHttYXhVc2VzfSB1c2VzKWAsXG4gICAgICBhdXRoLmdldENsaWVudElQKHJlcXVlc3QpXG4gICAgKTtcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAga2V5OiBrZXlEYXRhLFxuICAgICAgbWVzc2FnZTogJ0xpY2Vuc2Uga2V5IGNyZWF0ZWQgc3VjY2Vzc2Z1bGx5J1xuICAgIH0pO1xuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignQ3JlYXRlIGtleSBlcnJvcjonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfSxcbiAgICAgIHsgc3RhdHVzOiBlcnJvci5tZXNzYWdlID09PSAnSW52YWxpZCBzZXNzaW9uJyA/IDQwMSA6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUEFUQ0gocmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IGF1dGggPSBnZXRBdXRoTWFuYWdlcigpO1xuICAgIFxuICAgIC8vIENoZWNrIGF1dGhlbnRpY2F0aW9uIGFuZCBhZG1pbiByb2xlXG4gICAgY29uc3QgYXV0aEhlYWRlciA9IHJlcXVlc3QuaGVhZGVycy5nZXQoJ2F1dGhvcml6YXRpb24nKTtcbiAgICBpZiAoIWF1dGhIZWFkZXIgfHwgIWF1dGhIZWFkZXIuc3RhcnRzV2l0aCgnQmVhcmVyICcpKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnTm8gdG9rZW4gcHJvdmlkZWQnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDEgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICBjb25zdCB0b2tlbiA9IGF1dGhIZWFkZXIuc3Vic3RyaW5nKDcpO1xuICAgIGNvbnN0IHNlc3Npb24gPSBhdXRoLnZhbGlkYXRlU2Vzc2lvbih0b2tlbik7XG5cbiAgICBpZiAoc2Vzc2lvbi5yb2xlICE9PSAnYWRtaW4nKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnQWRtaW4gYWNjZXNzIHJlcXVpcmVkJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAzIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgY29uc3QgeyBrZXlJZCwgYWN0aW9uIH0gPSBhd2FpdCByZXF1ZXN0Lmpzb24oKTtcblxuICAgIGlmICgha2V5SWQgfHwgIWFjdGlvbikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0tleSBJRCBhbmQgYWN0aW9uIGFyZSByZXF1aXJlZCcgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIGlmIChhY3Rpb24gPT09ICdkZWFjdGl2YXRlJykge1xuICAgICAgY29uc3QgZGIgPSBnZXREYXRhYmFzZSgpO1xuICAgICAgXG4gICAgICAvLyBEZWFjdGl2YXRlIGxpY2Vuc2Uga2V5XG4gICAgICBjb25zdCByZXN1bHQgPSBkYi5kZWFjdGl2YXRlTGljZW5zZUtleShrZXlJZCk7XG5cbiAgICAgIGlmIChyZXN1bHQuY2hhbmdlcyA9PT0gMCkge1xuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdMaWNlbnNlIGtleSBub3QgZm91bmQnIH0sXG4gICAgICAgICAgeyBzdGF0dXM6IDQwNCB9XG4gICAgICAgICk7XG4gICAgICB9XG5cbiAgICAgIC8vIExvZyBhY3Rpdml0eVxuICAgICAgZGIubG9nQWN0aXZpdHkoXG4gICAgICAgIHNlc3Npb24udXNlcklkLFxuICAgICAgICAnREVBQ1RJVkFURV9MSUNFTlNFX0tFWScsXG4gICAgICAgIGBEZWFjdGl2YXRlZCBsaWNlbnNlIGtleSBJRDogJHtrZXlJZH1gLFxuICAgICAgICBhdXRoLmdldENsaWVudElQKHJlcXVlc3QpXG4gICAgICApO1xuXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICBtZXNzYWdlOiAnTGljZW5zZSBrZXkgZGVhY3RpdmF0ZWQgc3VjY2Vzc2Z1bGx5J1xuICAgICAgfSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdJbnZhbGlkIGFjdGlvbicgfSxcbiAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICk7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdEZWFjdGl2YXRlIGtleSBlcnJvcjonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfSxcbiAgICAgIHsgc3RhdHVzOiBlcnJvci5tZXNzYWdlID09PSAnSW52YWxpZCBzZXNzaW9uJyA/IDQwMSA6IDUwMCB9XG4gICAgKTtcbiAgfVxufSJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJnZXREYXRhYmFzZSIsImdldEF1dGhNYW5hZ2VyIiwiR0VUIiwicmVxdWVzdCIsImF1dGgiLCJhdXRoSGVhZGVyIiwiaGVhZGVycyIsImdldCIsInN0YXJ0c1dpdGgiLCJqc29uIiwic3VjY2VzcyIsImVycm9yIiwic3RhdHVzIiwidG9rZW4iLCJzdWJzdHJpbmciLCJzZXNzaW9uIiwidmFsaWRhdGVTZXNzaW9uIiwicm9sZSIsImRiIiwidXJsIiwiVVJMIiwibGltaXQiLCJwYXJzZUludCIsInNlYXJjaFBhcmFtcyIsIm9mZnNldCIsImtleXMiLCJnZXRMaWNlbnNlS2V5cyIsInBhZ2luYXRpb24iLCJoYXNNb3JlIiwibGVuZ3RoIiwiY29uc29sZSIsIm1lc3NhZ2UiLCJQT1NUIiwiZHVyYXRpb24iLCJtYXhVc2VzIiwicHJpb3JpdHlfbGV2ZWwiLCJtYXhfYWNjb3VudHNfcGVyX2JhdGNoIiwic2NoZWR1bGluZ19hY2Nlc3MiLCJrZXlEYXRhIiwiY3JlYXRlTGljZW5zZUtleSIsInVzZXJJZCIsInNldExpY2Vuc2VGZWF0dXJlcyIsImlkIiwibG9nQWN0aXZpdHkiLCJrZXlDb2RlIiwiZ2V0Q2xpZW50SVAiLCJrZXkiLCJQQVRDSCIsImtleUlkIiwiYWN0aW9uIiwicmVzdWx0IiwiZGVhY3RpdmF0ZUxpY2Vuc2VLZXkiLCJjaGFuZ2VzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/api/admin/keys/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/auth.js":
/*!*********************!*\
  !*** ./lib/auth.js ***!
  \*********************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst jwt = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst { getDatabase } = __webpack_require__(/*! ./database */ \"(rsc)/./lib/database.js\");\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key-change-this-in-production\";\nconst TOKEN_EXPIRY = \"7d\"; // 7 days\nclass AuthManager {\n    constructor(){\n        this.db = getDatabase();\n    }\n    // Generate JWT token\n    generateToken(user) {\n        const payload = {\n            userId: user.id,\n            username: user.username,\n            role: user.role,\n            iat: Math.floor(Date.now() / 1000)\n        };\n        return jwt.sign(payload, JWT_SECRET, {\n            expiresIn: TOKEN_EXPIRY\n        });\n    }\n    // Verify JWT token\n    verifyToken(token) {\n        try {\n            return jwt.verify(token, JWT_SECRET);\n        } catch (error) {\n            throw new Error(\"Invalid token\");\n        }\n    }\n    // Hash token for database storage\n    hashToken(token) {\n        return crypto.createHash(\"sha256\").update(token).digest(\"hex\");\n    }\n    // Create session with token\n    createSession(user, ipAddress = null, userAgent = null) {\n        const token = this.generateToken(user);\n        const tokenHash = this.hashToken(token);\n        // Calculate expiry date\n        const expiresAt = new Date();\n        expiresAt.setDate(expiresAt.getDate() + 7); // 7 days\n        // Store session in database\n        this.db.createSession(user.id, tokenHash, expiresAt.toISOString(), ipAddress, userAgent);\n        // Log activity\n        this.db.logActivity(user.id, \"LOGIN\", `User logged in from ${ipAddress}`, ipAddress);\n        return {\n            token,\n            user: {\n                id: user.id,\n                username: user.username,\n                role: user.role,\n                lastLogin: user.last_login\n            },\n            expiresAt: expiresAt.toISOString()\n        };\n    }\n    // Validate session\n    validateSession(token) {\n        try {\n            // First verify JWT\n            const decoded = this.verifyToken(token);\n            // Then check database session\n            const tokenHash = this.hashToken(token);\n            const session = this.db.validateSession(tokenHash);\n            if (!session || !session.user_active) {\n                throw new Error(\"Session invalid or user inactive\");\n            }\n            return {\n                userId: session.user_id,\n                username: session.username,\n                role: session.role,\n                sessionId: session.id\n            };\n        } catch (error) {\n            throw new Error(\"Invalid session\");\n        }\n    }\n    // Logout user\n    logout(token, userId = null) {\n        const tokenHash = this.hashToken(token);\n        this.db.invalidateSession(tokenHash);\n        if (userId) {\n            this.db.logActivity(userId, \"LOGOUT\", \"User logged out\");\n        }\n    }\n    // Logout all sessions for user\n    logoutAllSessions(userId) {\n        this.db.invalidateAllUserSessions(userId);\n        this.db.logActivity(userId, \"LOGOUT_ALL\", \"All sessions invalidated\");\n    }\n    // Middleware for protecting routes\n    requireAuth(requiredRole = null) {\n        return async (req, res, next)=>{\n            try {\n                const authHeader = req.headers.authorization;\n                if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n                    return res.status(401).json({\n                        error: \"No token provided\"\n                    });\n                }\n                const token = authHeader.substring(7);\n                const session = await this.validateSession(token);\n                // Check role if required\n                if (requiredRole && session.role !== requiredRole) {\n                    return res.status(403).json({\n                        error: \"Insufficient permissions\"\n                    });\n                }\n                // Add user info to request\n                req.user = session;\n                req.token = token;\n                next();\n            } catch (error) {\n                return res.status(401).json({\n                    error: error.message\n                });\n            }\n        };\n    }\n    // Admin only middleware\n    requireAdmin() {\n        return this.requireAuth(\"admin\");\n    }\n    // Extract IP address from request (Next.js compatible)\n    getClientIP(req) {\n        // For Next.js App Router requests\n        if (req.headers && typeof req.headers.get === \"function\") {\n            return req.headers.get(\"x-forwarded-for\") || req.headers.get(\"x-real-ip\") || req.ip || \"127.0.0.1\";\n        }\n        // For traditional Node.js requests\n        return req.headers[\"x-forwarded-for\"] || req.connection?.remoteAddress || req.socket?.remoteAddress || (req.connection?.socket ? req.connection.socket.remoteAddress : null) || \"127.0.0.1\";\n    }\n    // Extract user agent (Next.js compatible)\n    getUserAgent(req) {\n        // For Next.js App Router requests\n        if (req.headers && typeof req.headers.get === \"function\") {\n            return req.headers.get(\"user-agent\") || \"Unknown\";\n        }\n        // For traditional Node.js requests\n        return req.headers[\"user-agent\"] || \"Unknown\";\n    }\n    // Rate limiting helper\n    checkRateLimit(identifier, maxAttempts = 5, windowMinutes = 15) {\n        // This is a simple in-memory rate limiter\n        // In production, you might want to use Redis or database\n        if (!this.rateLimitStore) {\n            this.rateLimitStore = new Map();\n        }\n        const now = Date.now();\n        const windowMs = windowMinutes * 60 * 1000;\n        const key = `rate_limit_${identifier}`;\n        if (!this.rateLimitStore.has(key)) {\n            this.rateLimitStore.set(key, {\n                count: 1,\n                resetTime: now + windowMs\n            });\n            return {\n                allowed: true,\n                remaining: maxAttempts - 1\n            };\n        }\n        const record = this.rateLimitStore.get(key);\n        if (now > record.resetTime) {\n            // Reset the window\n            this.rateLimitStore.set(key, {\n                count: 1,\n                resetTime: now + windowMs\n            });\n            return {\n                allowed: true,\n                remaining: maxAttempts - 1\n            };\n        }\n        if (record.count >= maxAttempts) {\n            return {\n                allowed: false,\n                remaining: 0,\n                resetTime: record.resetTime\n            };\n        }\n        record.count++;\n        return {\n            allowed: true,\n            remaining: maxAttempts - record.count\n        };\n    }\n    // Clean up expired rate limit entries\n    cleanupRateLimit() {\n        if (!this.rateLimitStore) return;\n        const now = Date.now();\n        for (const [key, record] of this.rateLimitStore.entries()){\n            if (now > record.resetTime) {\n                this.rateLimitStore.delete(key);\n            }\n        }\n    }\n    // Password strength validation\n    validatePasswordStrength(password) {\n        const minLength = 8;\n        const hasUpperCase = /[A-Z]/.test(password);\n        const hasLowerCase = /[a-z]/.test(password);\n        const hasNumbers = /\\d/.test(password);\n        const hasSpecialChar = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n        const errors = [];\n        if (password.length < minLength) {\n            errors.push(`Password must be at least ${minLength} characters long`);\n        }\n        if (!hasUpperCase) {\n            errors.push(\"Password must contain at least one uppercase letter\");\n        }\n        if (!hasLowerCase) {\n            errors.push(\"Password must contain at least one lowercase letter\");\n        }\n        if (!hasNumbers) {\n            errors.push(\"Password must contain at least one number\");\n        }\n        if (!hasSpecialChar) {\n            errors.push(\"Password must contain at least one special character\");\n        }\n        return {\n            isValid: errors.length === 0,\n            errors,\n            strength: this.calculatePasswordStrength(password)\n        };\n    }\n    calculatePasswordStrength(password) {\n        let score = 0;\n        // Length bonus\n        score += Math.min(password.length * 2, 20);\n        // Character variety bonus\n        if (/[a-z]/.test(password)) score += 5;\n        if (/[A-Z]/.test(password)) score += 5;\n        if (/[0-9]/.test(password)) score += 5;\n        if (/[^A-Za-z0-9]/.test(password)) score += 10;\n        // Penalty for common patterns\n        if (/(.)\\1{2,}/.test(password)) score -= 10; // Repeated characters\n        if (/123|abc|qwe/i.test(password)) score -= 10; // Sequential patterns\n        if (score < 30) return \"weak\";\n        if (score < 60) return \"medium\";\n        return \"strong\";\n    }\n}\n// Export singleton instance\nlet authInstance = null;\nfunction getAuthManager() {\n    if (!authInstance) {\n        authInstance = new AuthManager();\n    }\n    return authInstance;\n}\nmodule.exports = {\n    getAuthManager,\n    AuthManager\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.js\n");

/***/ }),

/***/ "(rsc)/./lib/database.js":
/*!*************************!*\
  !*** ./lib/database.js ***!
  \*************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst Database = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\nconst bcrypt = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/umd/index.js\");\nconst { v4: uuidv4 } = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/cjs/index.js\");\nconst path = __webpack_require__(/*! path */ \"path\");\nclass DatabaseManager {\n    constructor(){\n        const dbPath = path.join(process.cwd(), \"data\", \"app.db\");\n        this.db = new Database(dbPath);\n        this.initializeTables();\n        this.createDefaultAdmin();\n    }\n    initializeTables() {\n        // Users table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS users (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        username TEXT UNIQUE NOT NULL,\n        password_hash TEXT NOT NULL,\n        role TEXT DEFAULT 'user' CHECK(role IN ('user', 'admin')),\n        license_key_id INTEGER,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        is_active BOOLEAN DEFAULT 1,\n        last_login DATETIME,\n        FOREIGN KEY (license_key_id) REFERENCES license_keys (id)\n      )\n    `);\n        // License keys table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS license_keys (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        key_code TEXT UNIQUE NOT NULL,\n        duration_days INTEGER NOT NULL,\n        max_uses INTEGER NOT NULL DEFAULT 1,\n        current_uses INTEGER DEFAULT 0,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        expires_at DATETIME NOT NULL,\n        is_active BOOLEAN DEFAULT 1,\n        created_by INTEGER,\n        features TEXT DEFAULT '[]',\n        FOREIGN KEY (created_by) REFERENCES users (id)\n      )\n    `);\n        // User sessions table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS user_sessions (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        token_hash TEXT NOT NULL,\n        expires_at DATETIME NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        ip_address TEXT,\n        user_agent TEXT,\n        is_active BOOLEAN DEFAULT 1,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Activity logs table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS activity_logs (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER,\n        action TEXT NOT NULL,\n        details TEXT,\n        ip_address TEXT,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // System settings table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS system_settings (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        setting_key TEXT UNIQUE NOT NULL,\n        setting_value TEXT,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_by INTEGER,\n        FOREIGN KEY (updated_by) REFERENCES users (id)\n      )\n    `);\n        // Encrypted login credentials table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS encrypted_credentials (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        login_key TEXT UNIQUE NOT NULL,\n        login_method TEXT NOT NULL CHECK(login_method IN ('normal', 'microsoft', 'google')),\n        encrypted_school TEXT NOT NULL,\n        encrypted_email TEXT NOT NULL,\n        encrypted_password TEXT NOT NULL,\n        encryption_iv TEXT NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        is_active BOOLEAN DEFAULT 1,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // License feature settings table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS license_feature_settings (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        license_key_id INTEGER NOT NULL,\n        max_accounts_per_batch INTEGER DEFAULT 0,\n        priority_level INTEGER DEFAULT 0 CHECK(priority_level >= 0 AND priority_level <= 10),\n        scheduling_access BOOLEAN DEFAULT 0,\n        multi_user_access BOOLEAN DEFAULT 0,\n        max_batches_per_day INTEGER DEFAULT 1,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (license_key_id) REFERENCES license_keys (id),\n        UNIQUE(license_key_id)\n      )\n    `);\n        // Queue batches table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_batches (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        batch_name TEXT,\n        login_type TEXT DEFAULT 'normal' CHECK(login_type IN ('normal', 'google', 'microsoft')),\n        total_accounts INTEGER NOT NULL,\n        processed_accounts INTEGER DEFAULT 0,\n        failed_accounts INTEGER DEFAULT 0,\n        status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),\n        priority_level INTEGER DEFAULT 0,\n        srp_target INTEGER DEFAULT 100,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        started_at DATETIME,\n        completed_at DATETIME,\n        scheduled_time DATETIME,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Add srp_target column to queue_batches if it doesn't exist\n        try {\n            this.db.exec(`ALTER TABLE queue_batches ADD COLUMN srp_target INTEGER DEFAULT 100`);\n        } catch (error) {\n        // Column already exists, ignore error\n        }\n        // Queue jobs table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_jobs (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        batch_id INTEGER,\n        user_id INTEGER NOT NULL,\n        job_type TEXT NOT NULL DEFAULT 'sparx_reader',\n        job_data TEXT NOT NULL,\n        status TEXT DEFAULT 'queued' CHECK(status IN ('queued', 'processing', 'completed', 'failed', 'cancelled')),\n        priority_level INTEGER DEFAULT 0,\n        effective_priority INTEGER DEFAULT 0,\n        srp_target INTEGER DEFAULT 100,\n        scheduled_time DATETIME,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        started_at DATETIME,\n        completed_at DATETIME,\n        error_message TEXT,\n        retry_count INTEGER DEFAULT 0,\n        max_retries INTEGER DEFAULT 3,\n        FOREIGN KEY (batch_id) REFERENCES queue_batches (id),\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Add srp_target column to queue_jobs if it doesn't exist\n        try {\n            this.db.exec(`ALTER TABLE queue_jobs ADD COLUMN srp_target INTEGER DEFAULT 100`);\n        } catch (error) {\n        // Column already exists, ignore error\n        }\n        // Queue schedules table for conflict detection\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_schedules (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        scheduled_time DATETIME NOT NULL,\n        duration_minutes INTEGER DEFAULT 30,\n        srp_target INTEGER DEFAULT 100,\n        job_id INTEGER,\n        batch_id INTEGER,\n        status TEXT DEFAULT 'scheduled' CHECK(status IN ('scheduled', 'active', 'completed', 'cancelled')),\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users (id),\n        FOREIGN KEY (job_id) REFERENCES queue_jobs (id),\n        FOREIGN KEY (batch_id) REFERENCES queue_batches (id)\n      )\n    `);\n        // Add srp_target column if it doesn't exist (for existing databases)\n        try {\n            this.db.exec(`ALTER TABLE queue_schedules ADD COLUMN srp_target INTEGER DEFAULT 100`);\n        } catch (error) {\n        // Column already exists, ignore error\n        }\n        // Create indexes for better performance\n        this.db.exec(`\n      CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);\n      CREATE INDEX IF NOT EXISTS idx_users_license_key ON users(license_key_id);\n      CREATE INDEX IF NOT EXISTS idx_license_keys_code ON license_keys(key_code);\n      CREATE INDEX IF NOT EXISTS idx_license_keys_expires ON license_keys(expires_at);\n      CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(token_hash);\n      CREATE INDEX IF NOT EXISTS idx_sessions_user ON user_sessions(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_user ON activity_logs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_created ON activity_logs(created_at);\n      CREATE INDEX IF NOT EXISTS idx_encrypted_credentials_user ON encrypted_credentials(user_id);\n      CREATE INDEX IF NOT EXISTS idx_encrypted_credentials_key ON encrypted_credentials(login_key);\n      CREATE INDEX IF NOT EXISTS idx_license_features ON license_feature_settings(license_key_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_user ON queue_batches(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_status ON queue_batches(status);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_scheduled ON queue_batches(scheduled_time);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_batch ON queue_jobs(batch_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_user ON queue_jobs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_status ON queue_jobs(status);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_priority ON queue_jobs(effective_priority);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_scheduled ON queue_jobs(scheduled_time);\n      CREATE INDEX IF NOT EXISTS idx_queue_schedules_user ON queue_schedules(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_schedules_time ON queue_schedules(scheduled_time);\n    `);\n        // Migration: Add max_batches_per_day column if it doesn't exist\n        try {\n            const columns = this.db.prepare(\"PRAGMA table_info(license_feature_settings)\").all();\n            const hasMaxBatchesPerDay = columns.some((col)=>col.name === \"max_batches_per_day\");\n            if (!hasMaxBatchesPerDay) {\n                console.log(\"Adding max_batches_per_day column to license_feature_settings...\");\n                this.db.exec(`ALTER TABLE license_feature_settings ADD COLUMN max_batches_per_day INTEGER DEFAULT 1`);\n            }\n        } catch (error) {\n            console.error(\"Migration error for max_batches_per_day:\", error);\n        }\n        // Migration: Add login_type column to queue_batches if it doesn't exist\n        try {\n            const batchColumns = this.db.prepare(\"PRAGMA table_info(queue_batches)\").all();\n            const hasLoginType = batchColumns.some((col)=>col.name === \"login_type\");\n            if (!hasLoginType) {\n                console.log(\"Adding login_type column to queue_batches...\");\n                this.db.exec(`ALTER TABLE queue_batches ADD COLUMN login_type TEXT DEFAULT 'normal' CHECK(login_type IN ('normal', 'google', 'microsoft'))`);\n            }\n        } catch (error) {\n            console.error(\"Migration error for login_type:\", error);\n        }\n    }\n    createDefaultAdmin() {\n        const adminExists = this.db.prepare(\"SELECT id FROM users WHERE role = ? LIMIT 1\").get(\"admin\");\n        if (!adminExists) {\n            const hashedPassword = bcrypt.hashSync(process.env.DEFAULT_ADMIN_PASSWORD, 12);\n            const stmt = this.db.prepare(`\n        INSERT INTO users (username, password_hash, role, is_active)\n        VALUES (?, ?, ?, ?)\n      `);\n            stmt.run(process.env.DEFAULT_ADMIN_USERNAME, hashedPassword, \"admin\", 1);\n            console.log(`Default admin user created: ${process.env.DEFAULT_ADMIN_USERNAME}/[hidden]`);\n        }\n    }\n    // User management methods\n    createUser(username, password, licenseKey) {\n        const transaction = this.db.transaction(()=>{\n            // Validate license key\n            const licenseStmt = this.db.prepare(`\n        SELECT id, max_uses, current_uses, expires_at, is_active \n        FROM license_keys \n        WHERE key_code = ? AND is_active = 1\n      `);\n            const license = licenseStmt.get(licenseKey);\n            if (!license) {\n                throw new Error(\"Invalid license key\");\n            }\n            if (new Date(license.expires_at) < new Date()) {\n                throw new Error(\"License key has expired\");\n            }\n            if (license.current_uses >= license.max_uses) {\n                throw new Error(\"License key has reached maximum uses\");\n            }\n            // Check if username already exists\n            const userExists = this.db.prepare(\"SELECT id FROM users WHERE username = ?\").get(username);\n            if (userExists) {\n                throw new Error(\"Username already exists\");\n            }\n            // Create user\n            const hashedPassword = bcrypt.hashSync(password, 12);\n            const userStmt = this.db.prepare(`\n        INSERT INTO users (username, password_hash, license_key_id, role)\n        VALUES (?, ?, ?, ?)\n      `);\n            const result = userStmt.run(username, hashedPassword, license.id, \"user\");\n            // Update license key usage\n            const updateLicenseStmt = this.db.prepare(`\n        UPDATE license_keys \n        SET current_uses = current_uses + 1 \n        WHERE id = ?\n      `);\n            updateLicenseStmt.run(license.id);\n            return result.lastInsertRowid;\n        });\n        return transaction();\n    }\n    authenticateUser(username, password) {\n        const stmt = this.db.prepare(`\n      SELECT u.*, lk.expires_at as license_expires, lk.is_active as license_active\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      WHERE u.username = ? AND u.is_active = 1\n    `);\n        const user = stmt.get(username);\n        if (!user) {\n            throw new Error(\"Invalid credentials\");\n        }\n        const isValidPassword = bcrypt.compareSync(password, user.password_hash);\n        if (!isValidPassword) {\n            throw new Error(\"Invalid credentials\");\n        }\n        // Check license validity for non-admin users\n        if (user.role !== \"admin\") {\n            if (!user.license_active || new Date(user.license_expires) < new Date()) {\n                throw new Error(\"License has expired\");\n            }\n        }\n        // Update last login\n        const updateStmt = this.db.prepare(\"UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?\");\n        updateStmt.run(user.id);\n        // Remove sensitive data\n        delete user.password_hash;\n        return user;\n    }\n    // License key management methods\n    createLicenseKey(durationDays, maxUses = 1, features = [], createdBy = null) {\n        const keyCode = this.generateLicenseKey();\n        const expiresAt = new Date();\n        expiresAt.setDate(expiresAt.getDate() + durationDays);\n        const stmt = this.db.prepare(`\n      INSERT INTO license_keys (key_code, duration_days, max_uses, expires_at, created_by, features)\n      VALUES (?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(keyCode, durationDays, maxUses, expiresAt.toISOString(), createdBy, JSON.stringify(features));\n        return {\n            id: result.lastInsertRowid,\n            keyCode,\n            durationDays,\n            maxUses,\n            expiresAt: expiresAt.toISOString(),\n            features\n        };\n    }\n    generateLicenseKey() {\n        const segments = [];\n        for(let i = 0; i < 4; i++){\n            segments.push(uuidv4().replace(/-/g, \"\").substring(0, 8).toUpperCase());\n        }\n        return `SRX-${segments.join(\"-\")}`;\n    }\n    getLicenseKeys(limit = 50, offset = 0) {\n        const stmt = this.db.prepare(`\n      SELECT \n        lk.*,\n        u.username as created_by_username,\n        COUNT(users.id) as users_count\n      FROM license_keys lk\n      LEFT JOIN users u ON lk.created_by = u.id\n      LEFT JOIN users ON users.license_key_id = lk.id\n      GROUP BY lk.id\n      ORDER BY lk.created_at DESC\n      LIMIT ? OFFSET ?\n    `);\n        return stmt.all(limit, offset);\n    }\n    deactivateLicenseKey(keyId) {\n        const stmt = this.db.prepare(\"UPDATE license_keys SET is_active = 0 WHERE id = ?\");\n        return stmt.run(keyId);\n    }\n    // Get detailed license information for a user\n    getUserLicenseStatus(userId) {\n        const stmt = this.db.prepare(`\n      SELECT \n        u.id as user_id,\n        u.username,\n        lk.id as license_id,\n        lk.key_code,\n        lk.max_uses,\n        lk.current_uses,\n        lk.expires_at,\n        lk.is_active,\n        CASE \n          WHEN lk.expires_at <= datetime('now') THEN 'expired'\n          WHEN lk.current_uses >= lk.max_uses THEN 'maxed_out'\n          WHEN lk.is_active = 0 THEN 'inactive'\n          ELSE 'valid'\n        END as license_status\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      WHERE u.id = ?\n    `);\n        return stmt.get(userId);\n    }\n    // Validate a license key for renewal (check if it's valid and has available uses)\n    validateLicenseForRenewal(licenseKey) {\n        const stmt = this.db.prepare(`\n      SELECT \n        id,\n        key_code,\n        max_uses,\n        current_uses,\n        expires_at,\n        is_active,\n        CASE \n          WHEN expires_at <= datetime('now') THEN 'expired'\n          WHEN current_uses >= max_uses THEN 'maxed_out'\n          WHEN is_active = 0 THEN 'inactive'\n          ELSE 'valid'\n        END as status\n      FROM license_keys \n      WHERE key_code = ?\n    `);\n        const license = stmt.get(licenseKey);\n        if (!license) {\n            return {\n                valid: false,\n                error: \"License key not found\"\n            };\n        }\n        if (license.status !== \"valid\") {\n            let errorMessage = \"License key is not valid\";\n            switch(license.status){\n                case \"expired\":\n                    errorMessage = \"License key has expired\";\n                    break;\n                case \"maxed_out\":\n                    errorMessage = \"License key has reached maximum uses\";\n                    break;\n                case \"inactive\":\n                    errorMessage = \"License key is inactive\";\n                    break;\n            }\n            return {\n                valid: false,\n                error: errorMessage\n            };\n        }\n        return {\n            valid: true,\n            license\n        };\n    }\n    // Renew user's license with a new license key\n    renewUserLicense(userId, newLicenseKey) {\n        const transaction = this.db.transaction(()=>{\n            // First validate the new license key\n            const validation = this.validateLicenseForRenewal(newLicenseKey);\n            if (!validation.valid) {\n                throw new Error(validation.error);\n            }\n            const newLicense = validation.license;\n            // Update user's license_key_id to the new license\n            const updateUserStmt = this.db.prepare(`\n        UPDATE users \n        SET license_key_id = ?, updated_at = CURRENT_TIMESTAMP \n        WHERE id = ?\n      `);\n            updateUserStmt.run(newLicense.id, userId);\n            // Increment the new license's current_uses\n            const updateLicenseStmt = this.db.prepare(`\n        UPDATE license_keys \n        SET current_uses = current_uses + 1 \n        WHERE id = ?\n      `);\n            updateLicenseStmt.run(newLicense.id);\n            // Log the renewal activity\n            this.logActivity(userId, \"LICENSE_RENEWED\", `License renewed with key: ${newLicenseKey}`);\n            return {\n                success: true,\n                newLicenseId: newLicense.id,\n                newLicenseKey: newLicenseKey,\n                expiresAt: newLicense.expires_at,\n                maxUses: newLicense.max_uses,\n                currentUses: newLicense.current_uses + 1\n            };\n        });\n        return transaction();\n    }\n    // Session management\n    createSession(userId, tokenHash, expiresAt, ipAddress = null, userAgent = null) {\n        const stmt = this.db.prepare(`\n      INSERT INTO user_sessions (user_id, token_hash, expires_at, ip_address, user_agent)\n      VALUES (?, ?, ?, ?, ?)\n    `);\n        return stmt.run(userId, tokenHash, expiresAt, ipAddress, userAgent);\n    }\n    validateSession(tokenHash) {\n        const stmt = this.db.prepare(`\n      SELECT s.*, u.username, u.role, u.is_active as user_active\n      FROM user_sessions s\n      JOIN users u ON s.user_id = u.id\n      WHERE s.token_hash = ? AND s.is_active = 1 AND s.expires_at > datetime('now')\n    `);\n        return stmt.get(tokenHash);\n    }\n    invalidateSession(tokenHash) {\n        const stmt = this.db.prepare(\"UPDATE user_sessions SET is_active = 0 WHERE token_hash = ?\");\n        return stmt.run(tokenHash);\n    }\n    invalidateAllUserSessions(userId) {\n        const stmt = this.db.prepare(\"UPDATE user_sessions SET is_active = 0 WHERE user_id = ?\");\n        return stmt.run(userId);\n    }\n    // Activity logging\n    logActivity(userId, action, details = null, ipAddress = null) {\n        const stmt = this.db.prepare(`\n      INSERT INTO activity_logs (user_id, action, details, ip_address)\n      VALUES (?, ?, ?, ?)\n    `);\n        return stmt.run(userId, action, details, ipAddress);\n    }\n    // Cleanup expired keys older than 30 days\n    cleanupExpiredKeys() {\n        try {\n            const stmt = this.db.prepare(`\n        DELETE FROM license_keys\n        WHERE status = 'expired'\n        AND datetime(expires_at) < datetime('now', '-30 days')\n      `);\n            const result = stmt.run();\n            if (result.changes > 0) {\n                console.log(`🧹 Cleaned up ${result.changes} expired license keys older than 30 days`);\n                this.logActivity(null, \"SYSTEM_CLEANUP\", `Removed ${result.changes} expired keys older than 30 days`);\n            }\n            return result.changes;\n        } catch (error) {\n            console.error(\"Error cleaning up expired keys:\", error);\n            return 0;\n        }\n    }\n    getActivityLogs(userId = null, limit = 100, offset = 0) {\n        let query = `\n      SELECT \n        al.*,\n        u.username\n      FROM activity_logs al\n      LEFT JOIN users u ON al.user_id = u.id\n    `;\n        const params = [];\n        if (userId) {\n            query += \" WHERE al.user_id = ?\";\n            params.push(userId);\n        }\n        query += \" ORDER BY al.created_at DESC LIMIT ? OFFSET ?\";\n        params.push(limit, offset);\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    // Analytics methods\n    getSystemStats() {\n        const stats = {};\n        // Total users\n        stats.totalUsers = this.db.prepare(\"SELECT COUNT(*) as count FROM users WHERE role = 'user'\").get().count;\n        // Active users (logged in within last 30 days)\n        stats.activeUsers = this.db.prepare(`\n      SELECT COUNT(*) as count FROM users \n      WHERE role = 'user' AND last_login > datetime('now', '-30 days')\n    `).get().count;\n        // Total license keys\n        stats.totalLicenseKeys = this.db.prepare(\"SELECT COUNT(*) as count FROM license_keys\").get().count;\n        // Active license keys\n        stats.activeLicenseKeys = this.db.prepare(`\n      SELECT COUNT(*) as count FROM license_keys \n      WHERE is_active = 1 AND expires_at > datetime('now')\n    `).get().count;\n        // Expired license keys\n        stats.expiredLicenseKeys = this.db.prepare(`\n      SELECT COUNT(*) as count FROM license_keys \n      WHERE expires_at <= datetime('now')\n    `).get().count;\n        // Recent activity (last 24 hours)\n        stats.recentActivity = this.db.prepare(`\n      SELECT COUNT(*) as count FROM activity_logs \n      WHERE created_at > datetime('now', '-1 day')\n    `).get().count;\n        return stats;\n    }\n    // User management for admin\n    getUsers(limit = 50, offset = 0) {\n        const stmt = this.db.prepare(`\n      SELECT \n        u.id,\n        u.username,\n        u.role,\n        u.created_at,\n        u.last_login,\n        u.is_active,\n        lk.key_code,\n        lk.expires_at as license_expires\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      ORDER BY u.created_at DESC\n      LIMIT ? OFFSET ?\n    `);\n        return stmt.all(limit, offset);\n    }\n    toggleUserStatus(userId) {\n        const stmt = this.db.prepare(\"UPDATE users SET is_active = NOT is_active WHERE id = ?\");\n        return stmt.run(userId);\n    }\n    // Cleanup methods\n    cleanupExpiredSessions() {\n        const stmt = this.db.prepare('DELETE FROM user_sessions WHERE expires_at <= datetime(\"now\")');\n        return stmt.run();\n    }\n    cleanupOldLogs(daysToKeep = 90) {\n        const stmt = this.db.prepare(`\n      DELETE FROM activity_logs \n      WHERE created_at <= datetime('now', '-${daysToKeep} days')\n    `);\n        return stmt.run();\n    }\n    // Encrypted credentials methods\n    saveEncryptedCredentials(userId, loginMethod, school, email, password, encryptionKey) {\n        const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n        // Generate a unique login key\n        const loginKey = \"SLK-\" + crypto.randomBytes(8).toString(\"hex\").toUpperCase();\n        // Create encryption IV\n        const iv = crypto.randomBytes(16);\n        const key = crypto.scryptSync(encryptionKey, \"salt\", 32);\n        // Encrypt school, email and password\n        const cipher1 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedSchool = cipher1.update(school, \"utf8\", \"hex\") + cipher1.final(\"hex\");\n        const cipher2 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedEmail = cipher2.update(email, \"utf8\", \"hex\") + cipher2.final(\"hex\");\n        const cipher3 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedPassword = cipher3.update(password, \"utf8\", \"hex\") + cipher3.final(\"hex\");\n        const stmt = this.db.prepare(`\n      INSERT INTO encrypted_credentials (user_id, login_key, login_method, encrypted_school, encrypted_email, encrypted_password, encryption_iv)\n      VALUES (?, ?, ?, ?, ?, ?, ?)\n    `);\n        stmt.run(userId, loginKey, loginMethod, encryptedSchool, encryptedEmail, encryptedPassword, iv.toString(\"hex\"));\n        return loginKey;\n    }\n    getEncryptedCredentials(loginKey, encryptionKey) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM encrypted_credentials \n      WHERE login_key = ? AND is_active = 1\n    `);\n        const result = stmt.get(loginKey);\n        if (!result) return null;\n        const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n        try {\n            const key = crypto.scryptSync(encryptionKey, \"salt\", 32);\n            const iv = Buffer.from(result.encryption_iv, \"hex\");\n            // Decrypt school, email and password\n            const decipher1 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const school = result.encrypted_school ? decipher1.update(result.encrypted_school, \"hex\", \"utf8\") + decipher1.final(\"utf8\") : null;\n            const decipher2 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const email = result.encrypted_email ? decipher2.update(result.encrypted_email, \"hex\", \"utf8\") + decipher2.final(\"utf8\") : null;\n            const decipher3 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const password = result.encrypted_password ? decipher3.update(result.encrypted_password, \"hex\", \"utf8\") + decipher3.final(\"utf8\") : null;\n            return {\n                loginMethod: result.login_method,\n                school,\n                email,\n                password,\n                userId: result.user_id\n            };\n        } catch (error) {\n            console.error(\"Failed to decrypt credentials:\", error);\n            return null;\n        }\n    }\n    getUserCredentials(userId) {\n        const stmt = this.db.prepare(`\n      SELECT login_key, login_method, created_at FROM encrypted_credentials \n      WHERE user_id = ? AND is_active = 1\n      ORDER BY created_at DESC\n    `);\n        return stmt.all(userId);\n    }\n    deactivateCredentials(loginKey) {\n        const stmt = this.db.prepare(\"UPDATE encrypted_credentials SET is_active = 0 WHERE login_key = ?\");\n        return stmt.run(loginKey);\n    }\n    // License Feature Settings Methods\n    setLicenseFeatures(licenseKeyId, features) {\n        const stmt = this.db.prepare(`\n      INSERT OR REPLACE INTO license_feature_settings\n      (license_key_id, max_accounts_per_batch, priority_level, scheduling_access, max_batches_per_day, updated_at)\n      VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)\n    `);\n        return stmt.run(licenseKeyId, features.max_accounts_per_batch || 0, features.priority_level || 0, features.scheduling_access ? 1 : 0, features.max_batches_per_day || 1);\n    }\n    getLicenseFeatures(licenseKeyId) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM license_feature_settings WHERE license_key_id = ?\n    `);\n        const result = stmt.get(licenseKeyId);\n        if (!result) {\n            // Return default features if none set\n            return {\n                max_accounts_per_batch: 0,\n                priority_level: 0,\n                scheduling_access: false,\n                max_batches_per_day: 1\n            };\n        }\n        return {\n            max_accounts_per_batch: result.max_accounts_per_batch,\n            priority_level: result.priority_level,\n            scheduling_access: Boolean(result.scheduling_access),\n            max_batches_per_day: result.max_batches_per_day || 1\n        };\n    }\n    getUserLicenseFeatures(userId) {\n        const stmt = this.db.prepare(`\n      SELECT lfs.* FROM license_feature_settings lfs\n      JOIN users u ON u.license_key_id = lfs.license_key_id\n      WHERE u.id = ?\n    `);\n        const result = stmt.get(userId);\n        if (!result) {\n            return {\n                max_accounts_per_batch: 0,\n                priority_level: 0,\n                scheduling_access: false,\n                max_batches_per_day: 1\n            };\n        }\n        return {\n            max_accounts_per_batch: result.max_accounts_per_batch,\n            priority_level: result.priority_level,\n            scheduling_access: Boolean(result.scheduling_access),\n            max_batches_per_day: result.max_batches_per_day || 1\n        };\n    }\n    // Daily batch count check\n    getUserDailyBatchCount(userId, date = null) {\n        const targetDate = date || new Date().toISOString().split(\"T\")[0]; // YYYY-MM-DD format\n        const stmt = this.db.prepare(`\n      SELECT COUNT(*) as count\n      FROM queue_batches\n      WHERE user_id = ?\n      AND DATE(created_at) = ?\n    `);\n        const result = stmt.get(userId, targetDate);\n        return result.count;\n    }\n    // Weekly schedule count check\n    getUserWeeklyScheduleCount(userId) {\n        // Get the start of the current week (Monday)\n        const now = new Date();\n        const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.\n        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // If Sunday, go back 6 days to Monday\n        const startOfWeek = new Date(now);\n        startOfWeek.setDate(now.getDate() - daysToMonday);\n        startOfWeek.setHours(0, 0, 0, 0);\n        const stmt = this.db.prepare(`\n      SELECT COUNT(*) as count\n      FROM queue_schedules\n      WHERE user_id = ?\n      AND created_at >= ?\n      AND status != 'cancelled'\n    `);\n        const result = stmt.get(userId, startOfWeek.toISOString());\n        return result.count;\n    }\n    // Queue Batch Methods\n    createQueueBatch(userId, batchName, accounts, scheduledTime = null, loginType = \"normal\", srpTarget = 100) {\n        const transaction = this.db.transaction(()=>{\n            // Get user's license features\n            const features = this.getUserLicenseFeatures(userId);\n            // Check daily batch limit\n            const dailyBatchCount = this.getUserDailyBatchCount(userId);\n            if (dailyBatchCount >= features.max_batches_per_day) {\n                throw new Error(`Daily batch limit reached (${features.max_batches_per_day} batches per day). Please try again tomorrow.`);\n            }\n            // Validate batch size against license limits\n            if (features.max_accounts_per_batch > 0 && accounts.length > features.max_accounts_per_batch) {\n                throw new Error(`Batch size (${accounts.length}) exceeds license limit (${features.max_accounts_per_batch})`);\n            }\n            // Validate scheduling access\n            if (scheduledTime && !features.scheduling_access) {\n                throw new Error(\"Scheduling access not available for this license\");\n            }\n            // Validate login type\n            if (![\n                \"normal\",\n                \"google\",\n                \"microsoft\"\n            ].includes(loginType)) {\n                throw new Error(\"Invalid login type specified\");\n            }\n            // Validate SRP target\n            if (srpTarget < 1 || srpTarget > 400) {\n                throw new Error(\"SRP target must be between 1 and 400\");\n            }\n            // Create batch\n            const batchStmt = this.db.prepare(`\n        INSERT INTO queue_batches (user_id, batch_name, login_type, total_accounts, priority_level, srp_target, scheduled_time)\n        VALUES (?, ?, ?, ?, ?, ?, ?)\n      `);\n            const batchResult = batchStmt.run(userId, batchName, loginType, accounts.length, features.priority_level, srpTarget, scheduledTime);\n            const batchId = batchResult.lastInsertRowid;\n            // Create individual jobs for each account\n            const jobStmt = this.db.prepare(`\n        INSERT INTO queue_jobs (batch_id, user_id, job_data, priority_level, effective_priority, srp_target, scheduled_time)\n        VALUES (?, ?, ?, ?, ?, ?, ?)\n      `);\n            accounts.forEach((account)=>{\n                const effectivePriority = this.calculateEffectivePriority(features.priority_level, scheduledTime);\n                jobStmt.run(batchId, userId, JSON.stringify(account), features.priority_level, effectivePriority, srpTarget, scheduledTime);\n            });\n            // Create schedule entry if scheduled\n            if (scheduledTime) {\n                this.createScheduleEntry(userId, scheduledTime, null, batchId, 30, srpTarget);\n            }\n            // Log activity\n            this.logActivity(userId, \"BATCH_CREATED\", `Created batch: ${batchName} with ${accounts.length} accounts`);\n            return batchId;\n        });\n        return transaction();\n    }\n    calculateEffectivePriority(basePriority, scheduledTime) {\n        let effectivePriority = basePriority;\n        // Boost priority for scheduled jobs approaching their time\n        if (scheduledTime) {\n            const now = new Date();\n            const scheduled = new Date(scheduledTime);\n            const timeDiff = scheduled.getTime() - now.getTime();\n            const hoursUntil = timeDiff / (1000 * 60 * 60);\n            if (hoursUntil <= 1) {\n                effectivePriority += 5; // High boost for jobs due within an hour\n            } else if (hoursUntil <= 6) {\n                effectivePriority += 2; // Medium boost for jobs due within 6 hours\n            }\n        }\n        // Apply starvation prevention (boost priority for old jobs)\n        // This would be implemented in a background process\n        return Math.min(effectivePriority, 10); // Cap at maximum priority\n    }\n    getQueueBatches(userId = null, status = null, limit = 50, offset = 0) {\n        let query = `\n      SELECT qb.*, u.username\n      FROM queue_batches qb\n      JOIN users u ON qb.user_id = u.id\n      WHERE 1=1\n    `;\n        const params = [];\n        if (userId) {\n            query += \" AND qb.user_id = ?\";\n            params.push(userId);\n        }\n        if (status) {\n            query += \" AND qb.status = ?\";\n            params.push(status);\n        }\n        query += \" ORDER BY qb.created_at DESC LIMIT ? OFFSET ?\";\n        params.push(limit, offset);\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    getBatchJobs(batchId) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM queue_jobs \n      WHERE batch_id = ? \n      ORDER BY effective_priority DESC, created_at ASC\n    `);\n        return stmt.all(batchId);\n    }\n    updateBatchStatus(batchId, status, completedAt = null) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_batches\n      SET status = ?, completed_at = ?\n      WHERE id = ?\n    `);\n        return stmt.run(status, completedAt, batchId);\n    }\n    // Queue Job Methods\n    createQueueJob({ username, job_type = \"sparx_reader\", job_data, srp_target = 100, priority = 0, status = \"queued\" }) {\n        // Get user ID\n        const userStmt = this.db.prepare(\"SELECT id FROM users WHERE username = ?\");\n        const user = userStmt.get(username);\n        if (!user) {\n            throw new Error(\"User not found\");\n        }\n        // Create the job\n        const stmt = this.db.prepare(`\n      INSERT INTO queue_jobs (user_id, job_type, job_data, priority_level, effective_priority, srp_target, status)\n      VALUES (?, ?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(user.id, job_type, job_data, priority, priority, srp_target, status);\n        return result.lastInsertRowid;\n    }\n    getQueueJobs(userId = null) {\n        let query = `\n      SELECT qj.*, qb.batch_name, u.username\n      FROM queue_jobs qj\n      LEFT JOIN queue_batches qb ON qj.batch_id = qb.id\n      JOIN users u ON qj.user_id = u.id\n    `;\n        const params = [];\n        if (userId) {\n            query += \" WHERE qj.user_id = ?\";\n            params.push(userId);\n        }\n        query += \" ORDER BY qj.created_at DESC\";\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    getNextQueueJob() {\n        const stmt = this.db.prepare(`\n      SELECT qj.*, qb.batch_name, u.username\n      FROM queue_jobs qj\n      LEFT JOIN queue_batches qb ON qj.batch_id = qb.id\n      JOIN users u ON qj.user_id = u.id\n      WHERE qj.status = 'queued'\n      AND (qj.scheduled_time IS NULL OR qj.scheduled_time <= datetime('now'))\n      ORDER BY qj.effective_priority DESC, qj.created_at ASC\n      LIMIT 1\n    `);\n        return stmt.get();\n    }\n    updateJobStatus(jobId, status, errorMessage = null, startedAt = null, completedAt = null) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET status = ?, error_message = ?, started_at = ?, completed_at = ?\n      WHERE id = ?\n    `);\n        return stmt.run(status, errorMessage, startedAt, completedAt, jobId);\n    }\n    incrementJobRetry(jobId) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET retry_count = retry_count + 1, status = 'queued'\n      WHERE id = ? AND retry_count < max_retries\n    `);\n        return stmt.run(jobId);\n    }\n    // Scheduling Methods\n    createScheduleEntry(userId, scheduledTime, jobId = null, batchId = null, durationMinutes = 30, srpTarget = 100) {\n        // Check for conflicts\n        const conflicts = this.checkScheduleConflicts(userId, scheduledTime, durationMinutes);\n        if (conflicts.length > 0) {\n            throw new Error(`Schedule conflict detected at ${scheduledTime}`);\n        }\n        const stmt = this.db.prepare(`\n      INSERT INTO queue_schedules (user_id, scheduled_time, duration_minutes, srp_target, job_id, batch_id)\n      VALUES (?, ?, ?, ?, ?, ?)\n    `);\n        return stmt.run(userId, scheduledTime, durationMinutes, srpTarget, jobId, batchId);\n    }\n    checkScheduleConflicts(userId, scheduledTime, durationMinutes) {\n        const startTime = new Date(scheduledTime);\n        const endTime = new Date(startTime.getTime() + durationMinutes * 60 * 1000);\n        const stmt = this.db.prepare(`\n      SELECT * FROM queue_schedules\n      WHERE user_id = ? \n      AND status IN ('scheduled', 'active')\n      AND (\n        (scheduled_time <= ? AND datetime(scheduled_time, '+' || duration_minutes || ' minutes') > ?) OR\n        (scheduled_time < ? AND datetime(scheduled_time, '+' || duration_minutes || ' minutes') >= ?)\n      )\n    `);\n        return stmt.all(userId, startTime.toISOString(), startTime.toISOString(), endTime.toISOString(), endTime.toISOString());\n    }\n    getUserSchedules(userId, startDate = null, endDate = null) {\n        let query = `\n      SELECT qs.*, qj.job_type, qb.batch_name\n      FROM queue_schedules qs\n      LEFT JOIN queue_jobs qj ON qs.job_id = qj.id\n      LEFT JOIN queue_batches qb ON qs.batch_id = qb.id\n      WHERE qs.user_id = ?\n    `;\n        const params = [\n            userId\n        ];\n        if (startDate) {\n            query += \" AND qs.scheduled_time >= ?\";\n            params.push(startDate);\n        }\n        if (endDate) {\n            query += \" AND qs.scheduled_time <= ?\";\n            params.push(endDate);\n        }\n        query += \" ORDER BY qs.scheduled_time ASC\";\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    // Priority Management Methods\n    updateJobPriority(jobId, newPriority, adminOverride = false) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET effective_priority = ?, priority_level = ?\n      WHERE id = ?\n    `);\n        const result = stmt.run(newPriority, adminOverride ? newPriority : null, jobId);\n        if (adminOverride) {\n            this.logActivity(null, \"ADMIN_PRIORITY_OVERRIDE\", `Job ${jobId} priority set to ${newPriority}`);\n        }\n        return result;\n    }\n    applyStarvationPrevention() {\n        // Boost priority for jobs that have been waiting too long\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET effective_priority = CASE \n        WHEN datetime('now') > datetime(created_at, '+2 hours') THEN MIN(effective_priority + 2, 10)\n        WHEN datetime('now') > datetime(created_at, '+1 hour') THEN MIN(effective_priority + 1, 10)\n        ELSE effective_priority\n      END\n      WHERE status = 'queued'\n    `);\n        return stmt.run();\n    }\n    getQueueStats() {\n        const stats = {};\n        // Total jobs by status\n        const statusStmt = this.db.prepare(`\n      SELECT status, COUNT(*) as count \n      FROM queue_jobs \n      GROUP BY status\n    `);\n        stats.jobsByStatus = statusStmt.all();\n        // Jobs by priority level\n        const priorityStmt = this.db.prepare(`\n      SELECT effective_priority, COUNT(*) as count \n      FROM queue_jobs \n      WHERE status = 'queued'\n      GROUP BY effective_priority\n      ORDER BY effective_priority DESC\n    `);\n        stats.jobsByPriority = priorityStmt.all();\n        // Average wait time\n        const waitTimeStmt = this.db.prepare(`\n      SELECT AVG(julianday(started_at) - julianday(created_at)) * 24 * 60 as avg_wait_minutes\n      FROM queue_jobs \n      WHERE started_at IS NOT NULL\n    `);\n        stats.averageWaitTime = waitTimeStmt.get()?.avg_wait_minutes || 0;\n        return stats;\n    }\n    close() {\n        this.db.close();\n    }\n}\n// Create data directory if it doesn't exist\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst dataDir = path.join(process.cwd(), \"data\");\nif (!fs.existsSync(dataDir)) {\n    fs.mkdirSync(dataDir, {\n        recursive: true\n    });\n}\n// Export singleton instance\nlet dbInstance = null;\nfunction getDatabase() {\n    if (!dbInstance) {\n        dbInstance = new DatabaseManager();\n    }\n    return dbInstance;\n}\nmodule.exports = {\n    getDatabase,\n    DatabaseManager\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/uuid","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fkeys%2Froute&page=%2Fapi%2Fadmin%2Fkeys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fkeys%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();