import { NextResponse } from 'next/server';
const { getDatabase } = require('../../../../lib/database');
const { getWebhookManager } = require('../../../../lib/webhook');
const { getAuthManager } = require('../../../../lib/auth');
const QueueMiddleware = require('../../../../lib/queueMiddleware');

// Middleware wrapper for Next.js API routes
function withMiddleware(handler, middlewares) {
  return async (request, context) => {
    const req = {
      ...request,
      body: await request.json().catch(() => ({})),
      user: null,
      licenseFeatures: null,
      url: request.url
    };

    // Authenticate user first
    try {
      const auth = getAuthManager();
      const authHeader = request.headers.get('authorization');

      console.log('Auth header:', authHeader ? `Present: ${authHeader.substring(0, 20)}...` : 'Missing');
      console.log('All headers:', Object.fromEntries(request.headers.entries()));

      if (!authHeader?.startsWith('Bearer ')) {
        console.log('❌ No Bearer token found');
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const token = authHeader.substring(7);
      const session = auth.validateSession(token);

      req.user = {
        id: session.userId,
        username: session.username,
        role: session.role
      };
    } catch (error) {
      console.error('Authentication error:', error.message);
      return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
    }

    // Apply middlewares
    for (const middleware of middlewares) {
      try {
        let nextCalled = false;
        const next = () => {
          nextCalled = true;
        };

        // Create a mock res object for Express-style middleware
        const res = {
          status: (code) => ({
            json: (data) => NextResponse.json(data, { status: code })
          })
        };

        const result = await middleware(req, res, next);

        if (result instanceof NextResponse) {
          return result;
        }

        if (!nextCalled) {
          return NextResponse.json({ error: 'Middleware blocked request' }, { status: 403 });
        }
      } catch (error) {
        console.error('Middleware error:', error);
        return NextResponse.json({ error: 'Request processing failed' }, { status: 500 });
      }
    }

    return handler(req, context);
  };
}

// GET - Get queue status and statistics
export async function GET(request) {
  const handler = withMiddleware(async (req) => {
    try {
      const db = getDatabase();
      const url = new URL(request.url);
      const detailed = url.searchParams.get('detailed') === 'true';
      const includeJobs = url.searchParams.get('include_jobs') === 'true';

      // Get user's queue statistics
      const userBatches = db.getQueueBatches(req.user.id, null, 10, 0);
      const userJobs = db.db.prepare(`
        SELECT * FROM queue_jobs 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 20
      `).all(req.user.id);

      // Get overall queue statistics (if admin or detailed view)
      let globalStats = null;
      if (req.user.role === 'admin' || detailed) {
        globalStats = db.getQueueStats();
      }

      // Get basic global queue overview for all users
      const globalOverview = db.db.prepare(`
        SELECT
          COUNT(DISTINCT u.id) as total_users,
          COUNT(DISTINCT qb.id) as total_batches,
          COUNT(CASE WHEN qj.status = 'queued' THEN 1 END) as queued_jobs,
          COUNT(CASE WHEN qj.status = 'processing' THEN 1 END) as processing_jobs,
          COUNT(CASE WHEN qj.status = 'completed' THEN 1 END) as completed_jobs_today,
          COUNT(CASE WHEN qj.status = 'failed' THEN 1 END) as failed_jobs_today
        FROM users u
        LEFT JOIN queue_batches qb ON u.id = qb.user_id
          AND DATE(qb.created_at) = DATE('now')
        LEFT JOIN queue_jobs qj ON qb.id = qj.batch_id
          AND DATE(qj.created_at) = DATE('now')
        WHERE u.role != 'admin'
      `).get();

      // Check for direct homework processing (not in queue)
      const directProcessingCount = global.realtimeData?.isRunning ? 1 : 0;

      // Get user's position in queue for pending jobs
      const userQueuePositions = db.db.prepare(`
        SELECT 
          qj.id,
          qj.status,
          qj.effective_priority,
          qj.created_at,
          (
            SELECT COUNT(*) 
            FROM queue_jobs qj2 
            WHERE qj2.status = 'queued' 
            AND (
              qj2.effective_priority > qj.effective_priority 
              OR (qj2.effective_priority = qj.effective_priority AND qj2.created_at < qj.created_at)
            )
          ) + 1 as queue_position
        FROM queue_jobs qj
        WHERE qj.user_id = ? AND qj.status = 'queued'
        ORDER BY qj.effective_priority DESC, qj.created_at ASC
      `).all(req.user.id);

      // Calculate estimated wait times
      const avgProcessingTime = db.db.prepare(`
        SELECT AVG(julianday(completed_at) - julianday(started_at)) * 24 * 60 as avg_minutes
        FROM queue_jobs 
        WHERE status = 'completed' AND started_at IS NOT NULL AND completed_at IS NOT NULL
      `).get()?.avg_minutes || 5; // Default 5 minutes if no data

      const queuedJobsAhead = db.db.prepare(`
        SELECT COUNT(*) as count 
        FROM queue_jobs 
        WHERE status = 'queued'
      `).get()?.count || 0;

      const processingJobs = db.db.prepare(`
        SELECT COUNT(*) as count 
        FROM queue_jobs 
        WHERE status = 'processing'
      `).get()?.count || 0;

      // Estimate wait time based on queue position and processing rate
      const estimatedWaitMinutes = Math.max(0, (queuedJobsAhead - processingJobs) * avgProcessingTime);

      // Get recent activity
      const recentActivity = db.getActivityLogs(req.user.id, 10, 0);

      const response = {
        user_queue_status: {
          total_batches: userBatches.length,
          active_batches: userBatches.filter(b => ['pending', 'processing'].includes(b.status)).length,
          completed_batches: userBatches.filter(b => b.status === 'completed').length,
          failed_batches: userBatches.filter(b => b.status === 'failed').length,
          total_jobs: userJobs.length,
          queued_jobs: userJobs.filter(j => j.status === 'queued').length,
          processing_jobs: userJobs.filter(j => j.status === 'processing').length,
          completed_jobs: userJobs.filter(j => j.status === 'completed').length,
          failed_jobs: userJobs.filter(j => j.status === 'failed').length
        },
        queue_positions: userQueuePositions,
        estimated_wait_time: {
          minutes: Math.round(estimatedWaitMinutes),
          formatted: formatWaitTime(estimatedWaitMinutes)
        },
        license_features: req.licenseFeatures,
        recent_activity: recentActivity.slice(0, 5), // Last 5 activities
        global_overview: {
          total_users: globalOverview.total_users || 0,
          total_batches_today: globalOverview.total_batches || 0,
          queue_status: {
            queued_jobs: globalOverview.queued_jobs || 0,
            processing_jobs: (globalOverview.processing_jobs || 0) + directProcessingCount,
            completed_jobs_today: globalOverview.completed_jobs_today || 0,
            failed_jobs_today: globalOverview.failed_jobs_today || 0
          },
          direct_homework_processing: directProcessingCount > 0
        }
      };

      if (detailed) {
        response.detailed_batches = userBatches.map(batch => ({
          ...batch,
          jobs: includeJobs ? db.getBatchJobs(batch.id) : []
        }));
      }

      if (globalStats && req.user.role === 'admin') {
        response.global_statistics = {
          ...globalStats,
          system_health: {
            queue_load: queuedJobsAhead > 100 ? 'high' : queuedJobsAhead > 50 ? 'medium' : 'low',
            processing_capacity: processingJobs,
            average_processing_time_minutes: Math.round(avgProcessingTime),
            estimated_system_wait_minutes: Math.round(estimatedWaitMinutes)
          }
        };
      }

      return NextResponse.json(response);

    } catch (error) {
      console.error('Get queue status error:', error);
      return NextResponse.json({
        error: 'Failed to retrieve queue status',
        details: error.message
      }, { status: 500 });
    }
  }, [
    QueueMiddleware.validateLicenseFeatures
  ]);

  return handler(request);
}

// POST - Queue management actions (admin only)
export async function POST(request) {
  const handler = withMiddleware(async (req) => {
    try {
      if (req.user.role !== 'admin') {
        return NextResponse.json({
          error: 'Admin access required'
        }, { status: 403 });
      }

      const db = getDatabase();
      const webhook = getWebhookManager();
      const { action, job_ids, batch_ids, priority_adjustment } = req.body;

      if (!action) {
        return NextResponse.json({
          error: 'action is required'
        }, { status: 400 });
      }

      let result = { success: true, affected_items: 0 };

      switch (action) {
        case 'apply_starvation_prevention':
          const starvationResult = db.applyStarvationPrevention();
          result.affected_items = starvationResult.changes;
          result.message = `Applied starvation prevention to ${result.affected_items} jobs`;
          
          await webhook.sendQueueAlert(
            'info',
            'Starvation prevention applied',
            {
              admin_user: req.user.username,
              affected_jobs: result.affected_items
            }
          );
          break;

        case 'cancel_jobs':
          if (!job_ids || !Array.isArray(job_ids)) {
            return NextResponse.json({
              error: 'job_ids array is required for cancel_jobs action'
            }, { status: 400 });
          }

          const cancelStmt = db.db.prepare(`
            UPDATE queue_jobs 
            SET status = 'cancelled' 
            WHERE id = ? AND status IN ('queued', 'processing')
          `);

          job_ids.forEach(jobId => {
            const cancelResult = cancelStmt.run(jobId);
            if (cancelResult.changes > 0) {
              result.affected_items++;
            }
          });

          result.message = `Cancelled ${result.affected_items} jobs`;
          
          await webhook.sendQueueAlert(
            'info',
            'Jobs cancelled by admin',
            {
              admin_user: req.user.username,
              cancelled_jobs: result.affected_items,
              total_requested: job_ids.length
            }
          );
          break;

        case 'cancel_batches':
          if (!batch_ids || !Array.isArray(batch_ids)) {
            return NextResponse.json({
              error: 'batch_ids array is required for cancel_batches action'
            }, { status: 400 });
          }

          batch_ids.forEach(batchId => {
            const updateResult = db.updateBatchStatus(batchId, 'cancelled');
            if (updateResult.changes > 0) {
              result.affected_items++;
              
              // Cancel all jobs in the batch
              const cancelJobsStmt = db.db.prepare(`
                UPDATE queue_jobs 
                SET status = 'cancelled' 
                WHERE batch_id = ? AND status IN ('queued', 'processing')
              `);
              cancelJobsStmt.run(batchId);
            }
          });

          result.message = `Cancelled ${result.affected_items} batches`;
          
          await webhook.sendQueueAlert(
            'info',
            'Batches cancelled by admin',
            {
              admin_user: req.user.username,
              cancelled_batches: result.affected_items,
              total_requested: batch_ids.length
            }
          );
          break;

        case 'adjust_priority':
          if (!job_ids || !Array.isArray(job_ids) || priority_adjustment === undefined) {
            return NextResponse.json({
              error: 'job_ids array and priority_adjustment are required'
            }, { status: 400 });
          }

          if (priority_adjustment < 0 || priority_adjustment > 10) {
            return NextResponse.json({
              error: 'priority_adjustment must be between 0 and 10'
            }, { status: 400 });
          }

          job_ids.forEach(jobId => {
            const updateResult = db.updateJobPriority(jobId, priority_adjustment, true);
            if (updateResult.changes > 0) {
              result.affected_items++;
            }
          });

          result.message = `Adjusted priority for ${result.affected_items} jobs to level ${priority_adjustment}`;
          
          await webhook.sendPriorityAdjustment(
            `bulk_${result.affected_items}_jobs`,
            'various',
            priority_adjustment,
            'Admin bulk priority adjustment',
            req.user.username
          );
          break;

        case 'clear_completed':
          const clearStmt = db.db.prepare(`
            DELETE FROM queue_jobs 
            WHERE status = 'completed' AND completed_at < datetime('now', '-7 days')
          `);
          const clearResult = clearStmt.run();
          result.affected_items = clearResult.changes;
          result.message = `Cleared ${result.affected_items} completed jobs older than 7 days`;
          break;

        case 'system_maintenance':
          // Perform various maintenance tasks
          const maintenanceTasks = [
            { name: 'cleanup_expired_sessions', result: db.cleanupExpiredSessions() },
            { name: 'cleanup_old_logs', result: db.cleanupOldLogs(30) },
            { name: 'apply_starvation_prevention', result: db.applyStarvationPrevention() }
          ];

          result.maintenance_results = maintenanceTasks.map(task => ({
            task: task.name,
            affected_items: task.result.changes || 0
          }));

          result.affected_items = maintenanceTasks.reduce((sum, task) => sum + (task.result.changes || 0), 0);
          result.message = `System maintenance completed, ${result.affected_items} items processed`;

          await webhook.sendQueueAlert(
            'maintenance',
            'System maintenance completed',
            {
              admin_user: req.user.username,
              tasks_completed: maintenanceTasks.length,
              total_items_processed: result.affected_items
            }
          );
          break;

        default:
          return NextResponse.json({
            error: 'Invalid action',
            valid_actions: [
              'apply_starvation_prevention',
              'cancel_jobs',
              'cancel_batches', 
              'adjust_priority',
              'clear_completed',
              'system_maintenance'
            ]
          }, { status: 400 });
      }

      // Log the admin action
      db.logActivity(
        req.user.id,
        'ADMIN_QUEUE_ACTION',
        `Performed ${action}: ${result.message}`
      );

      return NextResponse.json(result);

    } catch (error) {
      console.error('Queue management action error:', error);
      
      const webhook = getWebhookManager();
      await webhook.sendQueueAlert(
        'system_error',
        'Queue management action failed',
        {
          admin_user: req.user?.username || 'unknown',
          action: req.body?.action || 'unknown',
          error: error.message
        }
      );

      return NextResponse.json({
        error: 'Queue management action failed',
        details: error.message
      }, { status: 500 });
    }
  }, [
    QueueMiddleware.validateLicenseFeatures
  ]);

  return handler(request);
}

function formatWaitTime(minutes) {
  if (minutes < 1) {
    return 'Less than 1 minute';
  } else if (minutes < 60) {
    return `${Math.round(minutes)} minutes`;
  } else if (minutes < 1440) {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = Math.round(minutes % 60);
    return `${hours} hour${hours > 1 ? 's' : ''}${remainingMinutes > 0 ? ` ${remainingMinutes} minutes` : ''}`;
  } else {
    const days = Math.floor(minutes / 1440);
    const remainingHours = Math.floor((minutes % 1440) / 60);
    return `${days} day${days > 1 ? 's' : ''}${remainingHours > 0 ? ` ${remainingHours} hours` : ''}`;
  }
}