import { NextResponse } from 'next/server';
const { getDatabase } = require('../../../../lib/database');
const { getAuthManager } = require('../../../../lib/auth');

export async function GET(request) {
  try {
    const auth = getAuthManager();
    
    // Check authentication and admin role
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const session = auth.validateSession(token);

    if (session.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const db = getDatabase();
    
    // Get query parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit')) || 50;
    const offset = parseInt(url.searchParams.get('offset')) || 0;

    const users = db.getUsers(limit, offset);

    return NextResponse.json({
      success: true,
      users,
      pagination: {
        limit,
        offset,
        hasMore: users.length === limit
      }
    });

  } catch (error) {
    console.error('Get users error:', error);
    return NextResponse.json(
      { success: false, error: error.message },
      { status: error.message === 'Invalid session' ? 401 : 500 }
    );
  }
}

export async function PATCH(request) {
  try {
    const auth = getAuthManager();
    
    // Check authentication and admin role
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const session = auth.validateSession(token);

    if (session.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { userId, action } = await request.json();

    if (!userId || !action) {
      return NextResponse.json(
        { success: false, error: 'User ID and action are required' },
        { status: 400 }
      );
    }

    const db = getDatabase();
    
    let result;
    let logMessage;

    switch (action) {
      case 'toggle_status':
        result = db.toggleUserStatus(userId);
        logMessage = `Toggled user status for user ID: ${userId}`;
        break;
      
      case 'logout_all':
        auth.logoutAllSessions(userId);
        logMessage = `Logged out all sessions for user ID: ${userId}`;
        result = { changes: 1 };
        break;
      
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

    if (result.changes === 0) {
      return NextResponse.json(
        { success: false, error: 'User not found or no changes made' },
        { status: 404 }
      );
    }

    // Log activity
    db.logActivity(
      session.userId,
      'ADMIN_USER_ACTION',
      logMessage,
      auth.getClientIP(request)
    );

    return NextResponse.json({
      success: true,
      message: 'User action completed successfully'
    });

  } catch (error) {
    console.error('User action error:', error);
    return NextResponse.json(
      { success: false, error: error.message },
      { status: error.message === 'Invalid session' ? 401 : 500 }
    );
  }
}