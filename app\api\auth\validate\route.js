import { NextResponse } from 'next/server';
const { getAuthManager } = require('../../../../lib/auth');
const { getDatabase } = require('../../../../lib/database');

export async function GET(request) {
  try {
    const auth = getAuthManager();
    const db = getDatabase();
    
    // Get token from header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);

    try {
      // Validate session
      const session = auth.validateSession(token);

      // Get license status for non-admin users
      let licenseStatus = null;
      if (session.role !== 'admin') {
        licenseStatus = db.getUserLicenseStatus(session.userId);
      }

      return NextResponse.json({
        success: true,
        user: {
          id: session.userId,
          username: session.username,
          role: session.role
        },
        valid: true,
        licenseStatus: licenseStatus
      });

    } catch (authError) {
      return NextResponse.json(
        { success: false, error: 'Invalid session', valid: false },
        { status: 401 }
      );
    }

  } catch (error) {
    console.error('Token validation error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', valid: false },
      { status: 500 }
    );
  }
}