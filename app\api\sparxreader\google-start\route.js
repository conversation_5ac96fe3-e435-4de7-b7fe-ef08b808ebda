import { NextResponse } from 'next/server';
import { chromium } from 'playwright-extra';
import StealthPlugin from 'playwright-extra-plugin-stealth';
import path from 'path';
const { setGlobalBrowser, getGlobalBrowser, getGlobalPage, clearGlobalBrowser } = require('../browser-context.js');
const { getAuthManager } = require('../../../../lib/auth');
const { getDatabase } = require('../../../../lib/database');

// Add stealth plugin to avoid detection
chromium.use(StealthPlugin());

// Authentication and validation middleware
async function withAuth(request, handler) {
  try {
    const auth = getAuthManager();

    // Get token from header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const session = auth.validateSession(token);

    if (!session) {
      return NextResponse.json({ error: 'Invalid session' }, { status: 401 });
    }

    // Check daily main job limit
    const db = getDatabase();
    const mainJobLimitConfig = db.getSystemConfig('default_max_main_jobs_per_day');
    const maxMainJobsPerDay = mainJobLimitConfig ? mainJobLimitConfig.config_value : 5;

    const today = new Date().toISOString().split('T')[0];
    const mainJobCountStmt = db.db.prepare(`
      SELECT COUNT(*) as count
      FROM queue_jobs
      WHERE user_id = ? AND DATE(created_at) = ? AND batch_id IS NULL AND schedule_id IS NULL
    `);
    const mainJobCount = mainJobCountStmt.get(session.id, today);

    if (mainJobCount.count >= maxMainJobsPerDay) {
      return NextResponse.json({
        error: 'Daily main job limit reached',
        current_count: mainJobCount.count,
        max_allowed: maxMainJobsPerDay,
        message: `You have reached your daily limit of ${maxMainJobsPerDay} main automation job${maxMainJobsPerDay > 1 ? 's' : ''}. Please try again tomorrow.`
      }, { status: 429 });
    }

    return handler(request, session);
  } catch (error) {
    console.error('Auth error:', error);
    return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
  }
}

export async function POST(request) {
  return withAuth(request, async (request, user) => {
  try {
    const { url, targetSrp, credentials } = await request.json();
    
    const extensionPath = path.resolve(process.cwd(), 'Sparxext reader');
    
    try {
      // Always create new browser session for first try
      const existingBrowser = getGlobalBrowser();
      
      // Close any existing browser session
      if (existingBrowser) {
        try {
          await existingBrowser.close();
          console.log('Closed existing browser session');
        } catch (error) {
          console.log('Error closing existing browser:', error.message);
        }
      }
      
      // Create new browser session
      console.log('Creating new browser session...');
      
      const browser = await chromium.launchPersistentContext('', {
        headless: false,
        args: [
          `--disable-extensions-except=${extensionPath}`,
          `--load-extension=${extensionPath}`,
          '--no-sandbox',
          '--disable-setuid-sandbox'
        ]
      });
      
      const page = await browser.newPage();
      
      // Store globally for use in other endpoints
      setGlobalBrowser(browser, page);
      
      console.log('Navigating to Sparx Learning...');
      await page.goto(url, { timeout: 15000 });
      
      try {
        await page.waitForLoadState('networkidle', { timeout: 10000 });
      } catch (loadError) {
        console.log('Page load timeout, but continuing');
      }
      
      // Login process with better error handling
      try {
        // Use school from credentials
        if (!credentials || !credentials.school) {
          throw new Error('School name is required');
        }
        
        await page.fill('input[type="text"], input[type="search"], input', credentials.school);
        await page.press('input[type="text"], input[type="search"], input', 'Enter');
        await page.waitForTimeout(1000);
        
        try {
          await page.click('button:has-text("Continue")', { timeout: 5000 });
        } catch (error) {
          console.log('Continue button not found, proceeding anyway');
        }
        
        await page.waitForTimeout(2000);
        
        try {
          await page.click('div#cookiescript_accept', { timeout: 5000 });
        } catch (error) {
          console.log('Cookie accept button not found, proceeding anyway');
        }
        
        await page.waitForTimeout(2000);
        await page.click('div:has-text("Log in to Sparx using Google")');
        await page.waitForTimeout(3000);
        
        // Use credentials from request
        if (!credentials || !credentials.email || !credentials.password) {
          throw new Error('Login credentials are required');
        }
        
        await page.fill('input[type="email"], input[name="email"], input[id*="email"]', credentials.email);
        await page.waitForTimeout(2000);
        await page.click('button:has-text("next"), button:has-text("Next"), button[id*="next"], button[class*="next"]');
        await page.waitForTimeout(4000);
        await page.fill('input[type="password"], input[name="password"], input[id*="password"]', credentials.password);
        await page.waitForTimeout(2000);
        await page.click('button:has-text("next"), button:has-text("Next"), button[id*="next"], button[class*="next"]');
      } catch (loginError) {
        console.log('Login process error:', loginError.message);
        // Continue anyway as user might already be logged in
      }
              await page.waitForTimeout(3000);

      // COMPLETELY REWRITTEN APPROACH - EXTRACT SRP AND TITLE IMMEDIATELY
      
      // First, navigate to the library page
      console.log('Navigating to Sparx Reader library page...');
      try {
        // Direct navigation to library page - most reliable approach
        await page.goto('https://reader.sparx-learning.com/library', { timeout: 15000, waitUntil: 'domcontentloaded' });
        console.log('Navigation to library page initiated');
      } catch (navError) {
        console.log('Direct navigation to library page failed, trying alternative method');
        try {
          await page.click('a[href="https://reader.sparx-learning.com"]', { timeout: 5000 });
        } catch (clickError) {
          console.log('Both navigation methods failed, but continuing anyway');
        }
      }
      
      // Extract only the user's total SRP - we'll ask user for their target
      console.log('Extracting user total SRP...');
      let userTotalSrp = null;
      
      // Try to get user total SRP with multiple attempts
      for (let attempt = 0; attempt < 5; attempt++) {
        try {
          userTotalSrp = await page.evaluate(() => {
            // Get the user's total SRP
            const userTotalSrpElement = document.querySelector('.sr_92b39de6');
            return userTotalSrpElement ? userTotalSrpElement.textContent.replace(/[^\d,]/g, '').replace(',', '') : null;
          });
          
          if (userTotalSrp) {
            console.log(`User Total SRP extracted on attempt ${attempt + 1}: ${userTotalSrp}`);
            break;
          }
          
          // If we didn't get the info, wait a short time and try again
          if (attempt < 4) {
            await page.waitForTimeout(200);
          }
        } catch (error) {
          console.log(`SRP extraction attempt ${attempt + 1} failed:`, error.message);
          if (attempt < 4) {
            await page.waitForTimeout(200);
          }
        }
      }
      
      // Store initial SRP info globally and in browser localStorage
      global.sessionSrpInfo = { 
        initialUserTotalSrp: userTotalSrp,
        targetSrpNeeded: targetSrp || null
      };
      
      // Store target SRP and initial SRP in browser localStorage so extension can access it
      if (targetSrp) {
        await page.evaluate(({target, initial}) => {
          localStorage.setItem('targetSrp', target.toString());
          localStorage.setItem('initialSrp', initial || '0');
        }, {target: targetSrp, initial: userTotalSrp});
        console.log(`Target SRP stored in localStorage: ${targetSrp}`);
        console.log(`Initial SRP stored in localStorage: ${userTotalSrp}`);
      }
      
      // Book title will be extracted later when we're actually in the book
      let bookTitle = 'Book Found';
      
      // Take a screenshot of the library page with shorter timeout
      const screenshotPath = path.resolve(process.cwd(), 'public', 'screenshot.png');
      try {
        await page.screenshot({ path: screenshotPath, timeout: 10000 });
        console.log('Screenshot taken successfully');
      } catch (screenshotError) {
        console.log('Screenshot failed, continuing without it:', screenshotError.message);
      }
      
      // Return the response with the book title and current SRP for user confirmation
      return NextResponse.json({ 
        success: true, 
        message: 'Successfully logged in and extracted book title',
        bookTitle: bookTitle || 'No book found',
        currentSrp: userTotalSrp || 'Unknown',
        targetSrp: targetSrp || null,
        screenshot: '/screenshot.png'
      });
    } catch (playwrightError) {
      if (playwrightError.message.includes("Executable doesn't exist")) {
        return NextResponse.json({ 
          success: false, 
          error: "Playwright browsers not installed. Please run 'npx playwright install chromium' in your terminal.",
          needsPlaywright: true
        }, { status: 500 });
      } else {
        throw playwrightError;
      }
    }
  } catch (error) {
    console.error('Error opening Sparx Reader with extension:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
  });
}

// Re-export functions for backward compatibility
export { getGlobalBrowser, getGlobalPage, clearGlobalBrowser } from '../browser-context.js';
