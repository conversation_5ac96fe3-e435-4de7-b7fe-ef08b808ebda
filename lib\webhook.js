const https = require('https');
const http = require('http');
const { URL } = require('url');

class WebhookManager {
  constructor() {
    this.discordWebhookUrl = 'https://discord.com/api/webhooks/1391133719351394314/VEY8LIMPErwXKx8ZGgsJvhLwbjfqHEtzhsbiZfwHb3aSUp9htUtWDy9mrW4N2LhuD6c9';
    this.monitoringWebhookUrl = 'https://discord.com/api/webhooks/1391560096470929448/IUUj62U5RiL0LLM_p3-XGkSi54SLVXXJlhgaUG45iElsIL_7OmlwzUwC0SZr8a3oV_uc';
    this.websiteUrl = 'https://auto.sparxext.com';
    this.lastWebsiteStatus = null;
    this.lastStatusMessageId = null;
    this.monitoringInterval = null;
    this.isMonitoring = false;
  }

  async sendDiscordNotification(title, description, color = 0x3498db, fields = []) {
    const embed = {
      title,
      description,
      color,
      timestamp: new Date().toISOString(),
      fields,
      footer: {
        text: 'SparxReader Queue System'
      }
    };

    const payload = {
      embeds: [embed]
    };

    try {
      await this.sendWebhook(this.discordWebhookUrl, payload);
      console.log('Discord notification sent:', title);
    } catch (error) {
      console.error('Failed to send Discord notification:', error.message);
    }
  }

  async sendLicenseViolation(username, violation, details) {
    await this.sendDiscordNotification(
      '🚫 License Violation Detected',
      `User **${username}** attempted an unauthorized action`,
      0xe74c3c, // Red color
      [
        { name: 'Violation Type', value: violation, inline: true },
        { name: 'Details', value: details, inline: false },
        { name: 'Timestamp', value: new Date().toLocaleString(), inline: true }
      ]
    );
  }

  async sendScheduleConflict(username, scheduledTime, conflictDetails) {
    await this.sendDiscordNotification(
      '⚠️ Schedule Conflict Detected',
      `Schedule conflict for user **${username}**`,
      0xf39c12, // Orange color
      [
        { name: 'Requested Time', value: new Date(scheduledTime).toLocaleString(), inline: true },
        { name: 'Conflict Details', value: conflictDetails, inline: false },
        { name: 'User', value: username, inline: true }
      ]
    );
  }

  async sendPriorityAdjustment(jobId, oldPriority, newPriority, reason, adminUser = null) {
    await this.sendDiscordNotification(
      '🔄 Priority Adjustment',
      `Job priority has been adjusted`,
      0x9b59b6, // Purple color
      [
        { name: 'Job ID', value: jobId.toString(), inline: true },
        { name: 'Old Priority', value: oldPriority.toString(), inline: true },
        { name: 'New Priority', value: newPriority.toString(), inline: true },
        { name: 'Reason', value: reason, inline: false },
        ...(adminUser ? [{ name: 'Admin User', value: adminUser, inline: true }] : [])
      ]
    );
  }

  async sendBatchCreated(username, batchName, accountCount, scheduledTime = null) {
    await this.sendDiscordNotification(
      '📦 New Batch Created',
      `User **${username}** created a new batch`,
      0x2ecc71, // Green color
      [
        { name: 'Batch Name', value: batchName, inline: true },
        { name: 'Account Count', value: accountCount.toString(), inline: true },
        { name: 'User', value: username, inline: true },
        ...(scheduledTime ? [{ name: 'Scheduled Time', value: new Date(scheduledTime).toLocaleString(), inline: false }] : [])
      ]
    );
  }

  async sendBatchCompleted(username, batchName, processedCount, failedCount, duration) {
    await this.sendDiscordNotification(
      '✅ Batch Completed',
      `Batch processing completed for **${username}**`,
      0x27ae60, // Dark green color
      [
        { name: 'Batch Name', value: batchName, inline: true },
        { name: 'Processed', value: processedCount.toString(), inline: true },
        { name: 'Failed', value: failedCount.toString(), inline: true },
        { name: 'Duration', value: `${Math.round(duration / 60)} minutes`, inline: true },
        { name: 'User', value: username, inline: true }
      ]
    );
  }

  async sendQueueAlert(alertType, message, details = {}) {
    const colors = {
      'high_load': 0xe67e22, // Orange
      'system_error': 0xe74c3c, // Red
      'maintenance': 0x3498db, // Blue
      'info': 0x95a5a6 // Gray
    };

    const icons = {
      'high_load': '🔥',
      'system_error': '💥',
      'maintenance': '🔧',
      'info': 'ℹ️'
    };

    const fields = Object.entries(details).map(([key, value]) => ({
      name: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      value: value.toString(),
      inline: true
    }));

    await this.sendDiscordNotification(
      `${icons[alertType] || 'ℹ️'} Queue System Alert`,
      message,
      colors[alertType] || 0x95a5a6,
      fields
    );
  }

  sendWebhook(webhookUrl, payload) {
    return new Promise((resolve, reject) => {
      const url = new URL(webhookUrl);
      const data = JSON.stringify(payload);

      const options = {
        hostname: url.hostname,
        port: url.port || 443,
        path: url.pathname + url.search,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(data),
          'User-Agent': 'SparxReader-Queue-System/1.0'
        }
      };

      const req = https.request(options, (res) => {
        let responseData = '';
        
        res.on('data', (chunk) => {
          responseData += chunk;
        });

        res.on('end', () => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(responseData);
          } else {
            reject(new Error(`Webhook request failed with status ${res.statusCode}: ${responseData}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Webhook request timed out'));
      });

      req.setTimeout(10000); // 10 second timeout
      req.write(data);
      req.end();
    });
  }

  // Website monitoring methods
  async checkWebsiteStatus() {
    try {
      const response = await this.makeHttpRequest(this.websiteUrl);
      return {
        isUp: response.statusCode >= 200 && response.statusCode < 400,
        statusCode: response.statusCode,
        responseTime: response.responseTime,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        isUp: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  async makeHttpRequest(url) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const urlObj = new URL(url);

      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: 'GET',
        timeout: 10000,
        headers: {
          'User-Agent': 'SparxReader-Monitor/1.0'
        }
      };

      const req = (urlObj.protocol === 'https:' ? https : http).request(options, (res) => {
        const responseTime = Date.now() - startTime;
        resolve({
          statusCode: res.statusCode,
          responseTime,
          headers: res.headers
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      req.end();
    });
  }

  async startWebsiteMonitoring(intervalMinutes = 5) {
    if (this.isMonitoring) {
      console.log('Website monitoring already running');
      return;
    }

    this.isMonitoring = true;
    console.log(`Starting website monitoring for ${this.websiteUrl} every ${intervalMinutes} minutes`);

    // Initial check
    await this.performWebsiteCheck();

    // Set up interval
    this.monitoringInterval = setInterval(async () => {
      await this.performWebsiteCheck();
    }, intervalMinutes * 60 * 1000);
  }

  async stopWebsiteMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    console.log('Website monitoring stopped');
  }

  async performWebsiteCheck() {
    try {
      const status = await this.checkWebsiteStatus();
      const currentStatus = status.isUp ? 'up' : 'down';

      // Check if status changed
      if (this.lastWebsiteStatus !== currentStatus) {
        await this.sendWebsiteStatusUpdate(status, this.lastWebsiteStatus);
        this.lastWebsiteStatus = currentStatus;
      }
    } catch (error) {
      console.error('Website monitoring error:', error);
      await this.sendErrorReport('Website Monitoring Error', error, {
        website: this.websiteUrl,
        monitoring: true
      });
    }
  }

  async sendWebsiteStatusUpdate(status, previousStatus) {
    try {
      // Delete previous status message if exists
      if (this.lastStatusMessageId) {
        await this.deleteDiscordMessage(this.lastStatusMessageId);
      }

      const isUp = status.isUp;
      const color = isUp ? 0x00ff00 : 0xff0000; // Green for up, red for down
      const emoji = isUp ? '🟢' : '🔴';

      let title, description;

      if (previousStatus === null) {
        // Initial status
        title = `${emoji} Website Status Check`;
        description = `${this.websiteUrl} is currently **${isUp ? 'UP' : 'DOWN'}**`;
      } else if (isUp && previousStatus === 'down') {
        // Website back up
        title = `✅ Website Back Online`;
        description = `${this.websiteUrl} is **BACK UP** and responding normally`;
      } else if (!isUp && previousStatus === 'up') {
        // Website went down
        title = `❌ Website Down`;
        description = `${this.websiteUrl} is currently **DOWN** and not responding`;
      }

      const fields = [
        { name: 'URL', value: this.websiteUrl, inline: true },
        { name: 'Status', value: isUp ? 'Online' : 'Offline', inline: true }
      ];

      if (status.statusCode) {
        fields.push({ name: 'Status Code', value: status.statusCode.toString(), inline: true });
      }

      if (status.responseTime) {
        fields.push({ name: 'Response Time', value: `${status.responseTime}ms`, inline: true });
      }

      if (status.error) {
        fields.push({ name: 'Error', value: status.error, inline: false });
      }

      const embed = {
        title,
        description,
        color,
        timestamp: new Date().toISOString(),
        fields,
        footer: {
          text: 'SparxReader Website Monitor'
        }
      };

      const payload = {
        embeds: [embed]
      };

      const response = await this.sendWebhookWithResponse(this.monitoringWebhookUrl, payload);
      if (response && response.id) {
        this.lastStatusMessageId = response.id;
      }

    } catch (error) {
      console.error('Failed to send website status update:', error);
    }
  }

  async deleteDiscordMessage(messageId) {
    try {
      const url = `${this.monitoringWebhookUrl}/messages/${messageId}`;
      await this.makeDeleteRequest(url);
    } catch (error) {
      console.error('Failed to delete Discord message:', error);
    }
  }

  async makeDeleteRequest(url) {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);

      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || 443,
        path: urlObj.pathname + urlObj.search,
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      };

      const req = https.request(options, (res) => {
        resolve(res.statusCode);
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.end();
    });
  }

  async sendWebhookWithResponse(webhookUrl, payload) {
    return new Promise((resolve, reject) => {
      const data = JSON.stringify(payload);
      const url = new URL(webhookUrl);

      const options = {
        hostname: url.hostname,
        port: url.port || 443,
        path: url.pathname + url.search,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(data)
        }
      };

      const req = https.request(options, (res) => {
        let responseData = '';

        res.on('data', (chunk) => {
          responseData += chunk;
        });

        res.on('end', () => {
          try {
            const response = JSON.parse(responseData);
            resolve(response);
          } catch (error) {
            resolve(null);
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.setTimeout(10000);
      req.write(data);
      req.end();
    });
  }

  // Enhanced error reporting
  async sendErrorReport(title, error, context = {}) {
    try {
      const embed = {
        title: `🚨 ${title}`,
        description: error.message || error.toString(),
        color: 0xff0000, // Red
        timestamp: new Date().toISOString(),
        fields: [
          { name: 'Error Type', value: error.name || 'Unknown', inline: true },
          { name: 'Timestamp', value: new Date().toLocaleString(), inline: true }
        ],
        footer: {
          text: 'SparxReader Error Monitor'
        }
      };

      // Add stack trace if available
      if (error.stack) {
        embed.fields.push({
          name: 'Stack Trace',
          value: '```\n' + error.stack.substring(0, 1000) + (error.stack.length > 1000 ? '...' : '') + '\n```',
          inline: false
        });
      }

      // Add context information
      if (Object.keys(context).length > 0) {
        embed.fields.push({
          name: 'Context',
          value: '```json\n' + JSON.stringify(context, null, 2).substring(0, 500) + '\n```',
          inline: false
        });
      }

      // Add system information
      embed.fields.push({
        name: 'System Info',
        value: `Node.js: ${process.version}\nPlatform: ${process.platform}\nMemory: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`,
        inline: true
      });

      const payload = {
        embeds: [embed]
      };

      await this.sendWebhook(this.monitoringWebhookUrl, payload);
      console.log('Error report sent to Discord');
    } catch (webhookError) {
      console.error('Failed to send error report:', webhookError);
    }
  }

  // Terminal error capture
  setupGlobalErrorHandling() {
    // Capture uncaught exceptions
    process.on('uncaughtException', async (error) => {
      console.error('Uncaught Exception:', error);
      await this.sendErrorReport('Uncaught Exception', error, {
        type: 'uncaughtException',
        pid: process.pid
      });
    });

    // Capture unhandled promise rejections
    process.on('unhandledRejection', async (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      await this.sendErrorReport('Unhandled Promise Rejection', reason, {
        type: 'unhandledRejection',
        pid: process.pid
      });
    });

    // Capture console.error calls
    const originalConsoleError = console.error;
    console.error = async (...args) => {
      originalConsoleError.apply(console, args);

      // Send to Discord if it looks like an error
      const message = args.join(' ');
      if (message.toLowerCase().includes('error') || message.toLowerCase().includes('failed')) {
        await this.sendErrorReport('Console Error', new Error(message), {
          type: 'console.error',
          args: args.slice(0, 3) // Limit to first 3 arguments
        });
      }
    };

    console.log('Global error handling setup complete');
  }
}

// Export singleton instance
let webhookInstance = null;

function getWebhookManager() {
  if (!webhookInstance) {
    webhookInstance = new WebhookManager();
  }
  return webhookInstance;
}

module.exports = { getWebhookManager, WebhookManager };