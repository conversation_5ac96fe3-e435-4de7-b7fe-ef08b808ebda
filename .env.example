# JWT Secret Key - Change this in production!
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-make-it-long-and-random

# Database Configuration
DATABASE_PATH=./data/app.db

# Admin Configuration
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=admin123

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_DURATION_DAYS=7

# Rate Limiting
LOGIN_RATE_LIMIT_ATTEMPTS=5
LOGIN_RATE_LIMIT_WINDOW_MINUTES=15
REGISTER_RATE_LIMIT_ATTEMPTS=3
REGISTER_RATE_LIMIT_WINDOW_MINUTES=60

# Application Configuration
APP_NAME=Sparx Reader Auto
APP_VERSION=1.0.0
NODE_ENV=development

# Daily Limits Configuration
# Default maximum main automation jobs per day (1-100, default: 5)
DEFAULT_MAX_MAIN_JOBS_PER_DAY=5

# Default maximum batch jobs per day (0-50, default: 1)
DEFAULT_MAX_BATCHES_PER_DAY=1

# Default maximum scheduled jobs per week (0-20, default: 1)
DEFAULT_MAX_SCHEDULES_PER_WEEK=1