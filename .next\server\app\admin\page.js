/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin/page";
exports.ids = ["app/admin/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.jsx&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.jsx&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/page.jsx */ \"(rsc)/./app/admin/page.jsx\")), \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.jsx */ \"(rsc)/./app/layout.jsx\")), \"D:\\\\reader-auto-main\\\\app\\\\layout.jsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/admin/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin/page\",\n        pathname: \"/admin\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhZG1pbiUyRnBhZ2UmcGFnZT0lMkZhZG1pbiUyRnBhZ2UmYXBwUGF0aHM9JTJGYWRtaW4lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYWRtaW4lMkZwYWdlLmpzeCZhcHBEaXI9RCUzQSU1Q3JlYWRlci1hdXRvLW1haW4lNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUQlM0ElNUNyZWFkZXItYXV0by1tYWluJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsYUFBYSxzQkFBc0I7QUFDaUU7QUFDckM7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDLHVCQUF1QixvSkFBK0U7QUFDdEc7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSx5QkFBeUIsNElBQTBFO0FBQ25HLG9CQUFvQiwwTkFBZ0Y7QUFDcEc7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBQzZEO0FBQ3BGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ3VEO0FBQ3ZEO0FBQ08sd0JBQXdCLDhHQUFrQjtBQUNqRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFkZXItYXV0by8/ZDU2ZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcIlRVUkJPUEFDSyB7IHRyYW5zaXRpb246IG5leHQtc3NyIH1cIjtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICdhZG1pbicsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgIHBhZ2U6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHJlYWRlci1hdXRvLW1haW5cXFxcYXBwXFxcXGFkbWluXFxcXHBhZ2UuanN4XCIpLCBcIkQ6XFxcXHJlYWRlci1hdXRvLW1haW5cXFxcYXBwXFxcXGFkbWluXFxcXHBhZ2UuanN4XCJdLFxuICAgICAgICAgIFxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICBcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxyZWFkZXItYXV0by1tYWluXFxcXGFwcFxcXFxsYXlvdXQuanN4XCIpLCBcIkQ6XFxcXHJlYWRlci1hdXRvLW1haW5cXFxcYXBwXFxcXGxheW91dC5qc3hcIl0sXG4nbm90LWZvdW5kJzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIkQ6XFxcXHJlYWRlci1hdXRvLW1haW5cXFxcYXBwXFxcXGFkbWluXFxcXHBhZ2UuanN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCI7XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvYWRtaW4vcGFnZVwiO1xuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCI7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9hZG1pbi9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hZG1pblwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.jsx&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Creader-auto-main%5Capp%5Cadmin%5Cpage.jsx&server=true!":
/*!***************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Creader-auto-main%5Capp%5Cadmin%5Cpage.jsx&server=true! ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/admin/page.jsx */ \"(ssr)/./app/admin/page.jsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q3JlYWRlci1hdXRvLW1haW4lNUNhcHAlNUNhZG1pbiU1Q3BhZ2UuanN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3JlYWRlci1hdXRvLz8yMTZkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxccmVhZGVyLWF1dG8tbWFpblxcXFxhcHBcXFxcYWRtaW5cXFxccGFnZS5qc3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Creader-auto-main%5Capp%5Cadmin%5Cpage.jsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Creader-auto-main%5Capp%5Cglobals.css&server=true!":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Creader-auto-main%5Capp%5Cglobals.css&server=true! ***!
  \**********************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Creader-auto-main%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/admin/page.jsx":
/*!****************************!*\
  !*** ./app/admin/page.jsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction AdminDashboard() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"keys\");\n    const [keys, setKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [addons, setAddons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [analytics, setAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [queueConfig, setQueueConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        maxConcurrentJobs: 3,\n        browserTimeoutMinutes: 9\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [keyForm, setKeyForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        duration: \"30\",\n        maxUses: \"1\",\n        priorityLevel: \"0\",\n        maxAccountsPerBatch: \"10\",\n        schedulingAccess: false\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const token = localStorage.getItem(\"token\");\n        const userData = localStorage.getItem(\"user\");\n        if (!token || !userData) {\n            router.push(\"/login\");\n            return;\n        }\n        const parsedUser = JSON.parse(userData);\n        if (parsedUser.role !== \"admin\") {\n            router.push(\"/\");\n            return;\n        }\n        setUser(parsedUser);\n        fetchData(token);\n    }, []);\n    const fetchData = async (token)=>{\n        try {\n            const [keysRes, addonsRes, usersRes, queueRes] = await Promise.all([\n                fetch(\"/api/admin/keys\", {\n                    headers: {\n                        \"Authorization\": `Bearer ${token}`\n                    }\n                }),\n                fetch(\"/api/admin/addons\", {\n                    headers: {\n                        \"Authorization\": `Bearer ${token}`\n                    }\n                }),\n                fetch(\"/api/admin/users\", {\n                    headers: {\n                        \"Authorization\": `Bearer ${token}`\n                    }\n                }),\n                fetch(\"/api/admin/queue-config\", {\n                    headers: {\n                        \"Authorization\": `Bearer ${token}`\n                    }\n                })\n            ]);\n            const keysData = await keysRes.json();\n            const addonsData = await addonsRes.json();\n            const usersData = await usersRes.json();\n            const queueData = await queueRes.json();\n            if (keysData.success) {\n                setKeys(keysData.keys || []);\n            }\n            if (addonsData.success) {\n                setAddons(addonsData.addons || []);\n            }\n            if (usersData.success) {\n                setUsers(usersData.users || []);\n            }\n            if (queueData.success) {\n                setQueueConfig(queueData.config || {\n                    maxConcurrentJobs: 3,\n                    browserTimeoutMinutes: 9\n                });\n            }\n        } catch (error) {\n            console.error(\"Error fetching data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const createKey = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/admin/keys\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${token}`\n                },\n                body: JSON.stringify({\n                    duration: parseInt(keyForm.duration),\n                    maxUses: parseInt(keyForm.maxUses),\n                    priority_level: parseInt(keyForm.priorityLevel),\n                    max_accounts_per_batch: parseInt(keyForm.maxAccountsPerBatch),\n                    scheduling_access: keyForm.schedulingAccess\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                fetchData(token);\n                setKeyForm({\n                    duration: \"30\",\n                    maxUses: \"1\",\n                    selectedAddons: []\n                });\n                alert(`License key created successfully!\\nKey: ${data.key.keyCode}`);\n            } else {\n                alert(`Error: ${data.error}`);\n            }\n        } catch (error) {\n            console.error(\"Error creating key:\", error);\n            alert(\"Failed to create license key\");\n        }\n    };\n    const toggleUserStatus = async (userId)=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/admin/users\", {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${token}`\n                },\n                body: JSON.stringify({\n                    userId,\n                    action: \"toggle_status\"\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                fetchData(token);\n            } else {\n                alert(`Error: ${data.error}`);\n            }\n        } catch (error) {\n            console.error(\"Error toggling user status:\", error);\n            alert(\"Failed to toggle user status\");\n        }\n    };\n    const logoutAllUserSessions = async (userId)=>{\n        if (!confirm(\"Are you sure you want to logout all sessions for this user?\")) {\n            return;\n        }\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/admin/users\", {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${token}`\n                },\n                body: JSON.stringify({\n                    userId,\n                    action: \"logout_all\"\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                alert(\"All user sessions have been logged out\");\n            } else {\n                alert(`Error: ${data.error}`);\n            }\n        } catch (error) {\n            console.error(\"Error logging out user sessions:\", error);\n            alert(\"Failed to logout user sessions\");\n        }\n    };\n    const deactivateLicenseKey = async (keyId)=>{\n        if (!confirm(\"Are you sure you want to deactivate this license key?\")) {\n            return;\n        }\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/admin/keys\", {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${token}`\n                },\n                body: JSON.stringify({\n                    keyId,\n                    action: \"deactivate\"\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                fetchData(token);\n                alert(\"License key deactivated successfully\");\n            } else {\n                alert(`Error: ${data.error}`);\n            }\n        } catch (error) {\n            console.error(\"Error deactivating license key:\", error);\n            alert(\"Failed to deactivate license key\");\n        }\n    };\n    const updateQueueConfig = async ()=>{\n        try {\n            const token = localStorage.getItem(\"token\");\n            const response = await fetch(\"/api/admin/queue-config\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${token}`\n                },\n                body: JSON.stringify(queueConfig)\n            });\n            const data = await response.json();\n            if (data.success) {\n                alert(\"Queue configuration updated successfully!\");\n            } else {\n                alert(`Error: ${data.error}`);\n            }\n        } catch (error) {\n            console.error(\"Error updating queue config:\", error);\n            alert(\"Failed to update queue configuration\");\n        }\n    };\n    const logout = ()=>{\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"user\");\n        router.push(\"/login\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin\"\n            }, void 0, false, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                lineNumber: 242,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n            lineNumber: 241,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent\"\n            }, void 0, false, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"border-b border-gray-800 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-light text-white\",\n                                            children: \"Admin Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: \"Manage license keys and system settings\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-300\",\n                                            children: [\n                                                \"Welcome, \",\n                                                user?.username\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: logout,\n                                            className: \"px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded transition-colors\",\n                                            children: \"Logout\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-b border-gray-800\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"flex space-x-8\",\n                                        children: [\n                                            {\n                                                id: \"keys\",\n                                                label: \"License Keys\"\n                                            },\n                                            {\n                                                id: \"users\",\n                                                label: \"Users\"\n                                            },\n                                            {\n                                                id: \"queue\",\n                                                label: \"Queue Config\"\n                                            }\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(tab.id),\n                                                className: `py-4 px-1 border-b-2 font-medium text-sm transition-colors ${activeTab === tab.id ? \"border-blue-500 text-blue-400\" : \"border-transparent text-gray-400 hover:text-gray-300\"}`,\n                                                children: tab.label\n                                            }, tab.id, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            activeTab === \"keys\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-medium text-white mb-6\",\n                                                children: \"Create New License Key\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-gray-300 text-sm mb-2\",\n                                                                children: \"Duration (days)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: keyForm.duration,\n                                                                onChange: (e)=>setKeyForm({\n                                                                        ...keyForm,\n                                                                        duration: e.target.value\n                                                                    }),\n                                                                className: \"w-full px-4 py-2 bg-black border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500\",\n                                                                min: \"1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-gray-300 text-sm mb-2\",\n                                                                children: \"Max Uses\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: keyForm.maxUses,\n                                                                onChange: (e)=>setKeyForm({\n                                                                        ...keyForm,\n                                                                        maxUses: e.target.value\n                                                                    }),\n                                                                className: \"w-full px-4 py-2 bg-black border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500\",\n                                                                min: \"1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-gray-300 text-sm mb-2\",\n                                                                children: \"Priority Level\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: keyForm.priorityLevel,\n                                                                onChange: (e)=>setKeyForm({\n                                                                        ...keyForm,\n                                                                        priorityLevel: e.target.value\n                                                                    }),\n                                                                className: \"w-full px-4 py-2 bg-black border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500\",\n                                                                min: \"0\",\n                                                                max: \"10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-gray-300 text-sm mb-2\",\n                                                                children: \"Max Accounts Per Batch\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: keyForm.maxAccountsPerBatch,\n                                                                onChange: (e)=>setKeyForm({\n                                                                        ...keyForm,\n                                                                        maxAccountsPerBatch: e.target.value\n                                                                    }),\n                                                                className: \"w-full px-4 py-2 bg-black border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500\",\n                                                                min: \"0\",\n                                                                max: \"1000\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-gray-300 text-sm mb-2\",\n                                                                children: \"Scheduling Access\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: keyForm.schedulingAccess,\n                                                                onChange: (e)=>setKeyForm({\n                                                                        ...keyForm,\n                                                                        schedulingAccess: e.target.checked\n                                                                    }),\n                                                                className: \"mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: createKey,\n                                                className: \"mt-6 px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors\",\n                                                children: \"Generate Key\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-medium text-white mb-6\",\n                                                children: \"Existing Keys\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"border-b border-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-left py-3 px-4 text-gray-300\",\n                                                                        children: \"Key Code\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-left py-3 px-4 text-gray-300\",\n                                                                        children: \"Created\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-left py-3 px-4 text-gray-300\",\n                                                                        children: \"Expires\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 382,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-left py-3 px-4 text-gray-300\",\n                                                                        children: \"Uses\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-left py-3 px-4 text-gray-300\",\n                                                                        children: \"Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 384,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"text-left py-3 px-4 text-gray-300\",\n                                                                        children: \"Actions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 385,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            children: keys.map((key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    className: \"border-b border-gray-800\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-3 px-4 text-white font-mono text-sm\",\n                                                                            children: key.key_code\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                            lineNumber: 391,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-3 px-4 text-gray-300 text-sm\",\n                                                                            children: new Date(key.created_at).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                            lineNumber: 392,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-3 px-4 text-gray-300 text-sm\",\n                                                                            children: new Date(key.expires_at).toLocaleDateString()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                            lineNumber: 395,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-3 px-4 text-gray-300 text-sm\",\n                                                                            children: [\n                                                                                key.users_count,\n                                                                                \"/\",\n                                                                                key.max_uses\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                            lineNumber: 398,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-3 px-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: `px-2 py-1 rounded text-xs ${new Date(key.expires_at) > new Date() && key.is_active ? \"bg-green-500/20 text-green-400\" : \"bg-red-500/20 text-red-400\"}`,\n                                                                                children: new Date(key.expires_at) > new Date() && key.is_active ? \"Active\" : \"Expired\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                                lineNumber: 402,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                            lineNumber: 401,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"py-3 px-4\",\n                                                                            children: key.is_active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>deactivateLicenseKey(key.id),\n                                                                                className: \"px-2 py-1 bg-red-500/20 text-red-400 hover:bg-red-500/30 rounded text-xs transition-colors\",\n                                                                                children: \"Deactivate\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                                lineNumber: 412,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, key.id, true, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"users\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-medium text-white mb-6\",\n                                        children: \"User Management\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-x-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                className: \"w-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"border-b border-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 text-gray-300\",\n                                                                    children: \"Username\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 text-gray-300\",\n                                                                    children: \"Role\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 text-gray-300\",\n                                                                    children: \"License Key\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 text-gray-300\",\n                                                                    children: \"Created\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 text-gray-300\",\n                                                                    children: \"Last Login\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 text-gray-300\",\n                                                                    children: \"Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                    className: \"text-left py-3 px-4 text-gray-300\",\n                                                                    children: \"Actions\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                        children: users.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"border-b border-gray-800\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-3 px-4 text-white font-medium\",\n                                                                        children: user.username\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-3 px-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: `px-2 py-1 rounded text-xs ${user.role === \"admin\" ? \"bg-purple-500/20 text-purple-400\" : \"bg-blue-500/20 text-blue-400\"}`,\n                                                                            children: user.role\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                            lineNumber: 452,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 451,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-3 px-4 text-gray-300 font-mono text-sm\",\n                                                                        children: user.key_code || \"N/A\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 460,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-3 px-4 text-gray-300 text-sm\",\n                                                                        children: new Date(user.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-3 px-4 text-gray-300 text-sm\",\n                                                                        children: user.last_login ? new Date(user.last_login).toLocaleDateString() : \"Never\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 466,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-3 px-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: `px-2 py-1 rounded text-xs ${user.is_active ? \"bg-green-500/20 text-green-400\" : \"bg-red-500/20 text-red-400\"}`,\n                                                                            children: user.is_active ? \"Active\" : \"Inactive\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                            lineNumber: 470,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                        className: \"py-3 px-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>toggleUserStatus(user.id),\n                                                                                    className: `px-2 py-1 rounded text-xs transition-colors ${user.is_active ? \"bg-red-500/20 text-red-400 hover:bg-red-500/30\" : \"bg-green-500/20 text-green-400 hover:bg-green-500/30\"}`,\n                                                                                    children: user.is_active ? \"Deactivate\" : \"Activate\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                                    lineNumber: 480,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                user.role !== \"admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>logoutAllUserSessions(user.id),\n                                                                                    className: \"px-2 py-1 bg-orange-500/20 text-orange-400 hover:bg-orange-500/30 rounded text-xs transition-colors\",\n                                                                                    children: \"Logout All\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                                    lineNumber: 491,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                            lineNumber: 479,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                        lineNumber: 478,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, user.id, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 17\n                                            }, this),\n                                            users.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-gray-400\",\n                                                children: \"No users found\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"queue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-medium text-white mb-6\",\n                                                children: \"Queue Configuration\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                children: \"Max Concurrent Jobs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                min: \"1\",\n                                                                max: \"10\",\n                                                                value: queueConfig.maxConcurrentJobs,\n                                                                onChange: (e)=>setQueueConfig((prev)=>({\n                                                                            ...prev,\n                                                                            maxConcurrentJobs: parseInt(e.target.value)\n                                                                        })),\n                                                                className: \"w-full px-3 py-2 bg-black border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mt-1\",\n                                                                children: \"Number of jobs that can process simultaneously (1-10)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                                children: \"Browser Timeout (minutes)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                min: \"1\",\n                                                                max: \"30\",\n                                                                value: queueConfig.browserTimeoutMinutes,\n                                                                onChange: (e)=>setQueueConfig((prev)=>({\n                                                                            ...prev,\n                                                                            browserTimeoutMinutes: parseInt(e.target.value)\n                                                                        })),\n                                                                className: \"w-full px-3 py-2 bg-black border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 548,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mt-1\",\n                                                                children: \"Auto-close browsers after this time to prevent stuck jobs\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: updateQueueConfig,\n                                                    className: \"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors\",\n                                                    children: \"Update Configuration\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 border border-gray-800 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-white mb-4\",\n                                                children: \"Current Queue Status\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-blue-400\",\n                                                                children: queueConfig.maxConcurrentJobs\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 580,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"Max Concurrent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 583,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-green-400\",\n                                                                children: [\n                                                                    queueConfig.browserTimeoutMinutes,\n                                                                    \"m\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"Browser Timeout\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl font-bold text-yellow-400\",\n                                                                children: \"Active\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"Queue Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                        lineNumber: 576,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                                lineNumber: 516,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\reader-auto-main\\\\app\\\\admin\\\\page.jsx\",\n        lineNumber: 248,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/admin/page.jsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ff337214c78b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFkZXItYXV0by8uL2FwcC9nbG9iYWxzLmNzcz9lNzExIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZmYzMzcyMTRjNzhiXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/admin/page.jsx":
/*!****************************!*\
  !*** ./app/admin/page.jsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\reader-auto-main\app\admin\page.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.jsx":
/*!************************!*\
  !*** ./app/layout.jsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: \"Reader Auto\",\n    description: \"Sparx Reader Automation\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\reader-auto-main\\\\app\\\\layout.jsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\reader-auto-main\\\\app\\\\layout.jsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBc0I7QUFFZixNQUFNQSxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUFFQyxRQUFRLEVBQUU7SUFDN0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO3NCQUFNSDs7Ozs7Ozs7Ozs7QUFHYiIsInNvdXJjZXMiOlsid2VicGFjazovL3JlYWRlci1hdXRvLy4vYXBwL2xheW91dC5qc3g/MGM4MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdSZWFkZXIgQXV0bycsXG4gIGRlc2NyaXB0aW9uOiAnU3BhcnggUmVhZGVyIEF1dG9tYXRpb24nLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHsgY2hpbGRyZW4gfSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHk+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.jsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fadmin%2Fpage&page=%2Fadmin%2Fpage&appPaths=%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.jsx&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();