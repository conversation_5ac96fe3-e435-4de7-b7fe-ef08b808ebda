import { NextResponse } from 'next/server';
const { getDatabase } = require('../../../../lib/database');
const { getWebhookManager } = require('../../../../lib/webhook');
const { getAuthManager } = require('../../../../lib/auth');

// Middleware wrapper for Next.js API routes
function withMiddleware(handler) {
  return async (request, context) => {
    try {
      const auth = getAuthManager();
      
      // Get token from header
      const authHeader = request.headers.get('authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json({ error: 'No token provided' }, { status: 401 });
      }

      const token = authHeader.substring(7);
      const session = auth.validateSession(token);
      
      if (!session) {
        return NextResponse.json({ error: 'Invalid session' }, { status: 401 });
      }

      const req = {
        ...request,
        body: await request.json().catch(() => ({})),
        user: session,
        url: request.url
      };

      return handler(req, context);
    } catch (error) {
      console.error('Admin auth error:', error);
      return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
    }
  };
}

// GET - Get current daily limits configuration
export async function GET(request) {
  const handler = withMiddleware(async (req) => {
    try {
      // Check admin access
      if (req.user.role !== 'admin') {
        return NextResponse.json({
          error: 'Admin access required'
        }, { status: 403 });
      }

      const db = getDatabase();
      const configs = db.getAllSystemConfig();
      
      // Filter for daily limit configs
      const dailyLimitConfigs = configs.filter(config => 
        config.config_key.includes('max_') && 
        (config.config_key.includes('per_day') || config.config_key.includes('per_week'))
      );

      return NextResponse.json({
        success: true,
        daily_limits: dailyLimitConfigs.reduce((acc, config) => {
          acc[config.config_key] = {
            value: config.config_value,
            description: config.description,
            updated_at: config.updated_at
          };
          return acc;
        }, {}),
        all_configs: configs
      });

    } catch (error) {
      console.error('Error fetching daily limits:', error);
      return NextResponse.json({
        error: 'Failed to fetch daily limits configuration',
        details: error.message
      }, { status: 500 });
    }
  });

  return handler(request);
}

// POST - Update daily limits configuration
export async function POST(request) {
  const handler = withMiddleware(async (req) => {
    try {
      // Check admin access
      if (req.user.role !== 'admin') {
        return NextResponse.json({
          error: 'Admin access required'
        }, { status: 403 });
      }

      const db = getDatabase();
      const webhook = getWebhookManager();
      const { 
        default_max_main_jobs_per_day,
        default_max_batches_per_day,
        default_max_schedules_per_week,
        dead_letter_queue_enabled,
        max_retry_attempts,
        circuit_breaker_enabled
      } = req.body;

      const updates = [];

      // Validate and update main jobs per day
      if (default_max_main_jobs_per_day !== undefined) {
        if (default_max_main_jobs_per_day < 1 || default_max_main_jobs_per_day > 100) {
          return NextResponse.json({
            error: 'default_max_main_jobs_per_day must be between 1 and 100'
          }, { status: 400 });
        }
        db.setSystemConfig('default_max_main_jobs_per_day', default_max_main_jobs_per_day, 'number');
        updates.push(`Main jobs per day: ${default_max_main_jobs_per_day}`);
      }

      // Validate and update batches per day
      if (default_max_batches_per_day !== undefined) {
        if (default_max_batches_per_day < 0 || default_max_batches_per_day > 50) {
          return NextResponse.json({
            error: 'default_max_batches_per_day must be between 0 and 50'
          }, { status: 400 });
        }
        db.setSystemConfig('default_max_batches_per_day', default_max_batches_per_day, 'number');
        updates.push(`Batches per day: ${default_max_batches_per_day}`);
      }

      // Validate and update schedules per week
      if (default_max_schedules_per_week !== undefined) {
        if (default_max_schedules_per_week < 0 || default_max_schedules_per_week > 20) {
          return NextResponse.json({
            error: 'default_max_schedules_per_week must be between 0 and 20'
          }, { status: 400 });
        }
        db.setSystemConfig('default_max_schedules_per_week', default_max_schedules_per_week, 'number');
        updates.push(`Schedules per week: ${default_max_schedules_per_week}`);
      }

      // Update dead letter queue setting
      if (dead_letter_queue_enabled !== undefined) {
        db.setSystemConfig('dead_letter_queue_enabled', dead_letter_queue_enabled, 'boolean');
        updates.push(`Dead letter queue: ${dead_letter_queue_enabled ? 'enabled' : 'disabled'}`);
      }

      // Update max retry attempts
      if (max_retry_attempts !== undefined) {
        if (max_retry_attempts < 1 || max_retry_attempts > 10) {
          return NextResponse.json({
            error: 'max_retry_attempts must be between 1 and 10'
          }, { status: 400 });
        }
        db.setSystemConfig('max_retry_attempts', max_retry_attempts, 'number');
        updates.push(`Max retry attempts: ${max_retry_attempts}`);
      }

      // Update circuit breaker setting
      if (circuit_breaker_enabled !== undefined) {
        db.setSystemConfig('circuit_breaker_enabled', circuit_breaker_enabled, 'boolean');
        updates.push(`Circuit breaker: ${circuit_breaker_enabled ? 'enabled' : 'disabled'}`);
      }

      // Log admin action
      db.logActivity(
        req.user.id,
        'ADMIN_DAILY_LIMITS_UPDATE',
        `Updated daily limits: ${updates.join(', ')}`
      );

      // Send webhook notification
      await webhook.sendDiscordNotification(
        '⚙️ Daily Limits Updated',
        `Admin **${req.user.username}** updated system daily limits`,
        0xe74c3c, // Red color
        updates.map(update => ({ name: 'Change', value: update, inline: true }))
      );

      return NextResponse.json({
        success: true,
        message: 'Daily limits configuration updated successfully',
        updates: updates
      });

    } catch (error) {
      console.error('Error updating daily limits:', error);
      
      const webhook = getWebhookManager();
      await webhook.sendQueueAlert(
        'system_error',
        'Daily limits update failed',
        {
          admin_user: req.user?.username || 'unknown',
          error: error.message
        }
      );

      return NextResponse.json({
        error: 'Failed to update daily limits configuration',
        details: error.message
      }, { status: 500 });
    }
  });

  return handler(request);
}
