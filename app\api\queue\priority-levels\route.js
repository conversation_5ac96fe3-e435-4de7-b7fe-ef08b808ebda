import { NextResponse } from 'next/server';
const { getDatabase } = require('../../../../lib/database');
const QueueMiddleware = require('../../../../lib/queueMiddleware');

// Middleware wrapper for Next.js API routes
function withMiddleware(handler, middlewares) {
  return async (request, context) => {
    const req = {
      ...request,
      body: await request.json().catch(() => ({})),
      user: null,
      licenseFeatures: null
    };

    const res = {
      status: (code) => ({ json: (data) => NextResponse.json(data, { status: code }) }),
      json: (data) => NextResponse.json(data)
    };

    // Authenticate user first
    try {
      const db = getDatabase();
      const authHeader = request.headers.get('authorization');
      
      if (!authHeader?.startsWith('Bearer ')) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }
      
      const token = authHeader.substring(7);
      const session = db.validateSession(token);
      
      if (!session) {
        return NextResponse.json({ error: 'Invalid or expired session' }, { status: 401 });
      }
      
      req.user = {
        id: session.user_id,
        username: session.username,
        role: session.role
      };
    } catch (error) {
      return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
    }

    // Apply middlewares
    for (const middleware of middlewares) {
      try {
        let nextCalled = false;
        const next = () => { nextCalled = true; };
        
        const result = await middleware(req, res, next);
        
        if (result instanceof NextResponse) {
          return result;
        }
        
        if (!nextCalled) {
          return NextResponse.json({ error: 'Middleware blocked request' }, { status: 403 });
        }
      } catch (error) {
        console.error('Middleware error:', error);
        return NextResponse.json({ error: 'Request processing failed' }, { status: 500 });
      }
    }

    return handler(req, context);
  };
}

// GET - Get priority level information
export async function GET(request) {
  const handler = withMiddleware(async (req) => {
    try {
      const db = getDatabase();
      
      // Priority level descriptions
      const priorityDescriptions = {
        0: { name: 'Basic', description: 'Standard queue processing', color: '#95a5a6' },
        1: { name: 'Low', description: 'Slightly elevated priority', color: '#3498db' },
        2: { name: 'Low+', description: 'Enhanced low priority', color: '#2980b9' },
        3: { name: 'Medium-', description: 'Below medium priority', color: '#f39c12' },
        4: { name: 'Medium', description: 'Medium priority processing', color: '#e67e22' },
        5: { name: 'Medium+', description: 'Enhanced medium priority', color: '#d35400' },
        6: { name: 'High-', description: 'Below high priority', color: '#e74c3c' },
        7: { name: 'High', description: 'High priority processing', color: '#c0392b' },
        8: { name: 'High+', description: 'Enhanced high priority', color: '#8e44ad' },
        9: { name: 'Critical', description: 'Critical priority processing', color: '#9b59b6' },
        10: { name: 'Maximum', description: 'Maximum priority processing', color: '#2c3e50' }
      };

      // Get user's current license features
      const userFeatures = req.licenseFeatures;
      const maxPriority = userFeatures.priority_level;

      // Get queue statistics by priority
      const queueStats = db.getQueueStats();
      
      // Get current queue distribution
      const queueDistribution = db.db.prepare(`
        SELECT 
          effective_priority,
          COUNT(*) as job_count,
          AVG(julianday('now') - julianday(created_at)) * 24 * 60 as avg_wait_minutes
        FROM queue_jobs 
        WHERE status = 'queued'
        GROUP BY effective_priority
        ORDER BY effective_priority DESC
      `).all();

      // Build available priority levels for user
      const availablePriorities = [];
      for (let i = 0; i <= maxPriority; i++) {
        const queueInfo = queueDistribution.find(q => q.effective_priority === i) || { job_count: 0, avg_wait_minutes: 0 };
        
        availablePriorities.push({
          level: i,
          ...priorityDescriptions[i],
          available: true,
          current_queue_count: queueInfo.job_count,
          estimated_wait_minutes: Math.round(queueInfo.avg_wait_minutes || 0)
        });
      }

      // Add unavailable priority levels for reference
      const unavailablePriorities = [];
      for (let i = maxPriority + 1; i <= 10; i++) {
        unavailablePriorities.push({
          level: i,
          ...priorityDescriptions[i],
          available: false,
          reason: 'License upgrade required'
        });
      }

      return NextResponse.json({
        user_priority_info: {
          max_priority_level: maxPriority,
          current_license_type: maxPriority === 0 ? 'Basic' : 
                               maxPriority <= 3 ? 'Standard' :
                               maxPriority <= 6 ? 'Professional' :
                               maxPriority <= 8 ? 'Premium' : 'Enterprise'
        },
        available_priorities: availablePriorities,
        unavailable_priorities: unavailablePriorities,
        queue_statistics: {
          total_queued_jobs: queueStats.jobsByStatus?.find(s => s.status === 'queued')?.count || 0,
          total_processing_jobs: queueStats.jobsByStatus?.find(s => s.status === 'processing')?.count || 0,
          average_wait_time_minutes: Math.round(queueStats.averageWaitTime || 0),
          priority_distribution: queueDistribution
        },
        recommendations: generatePriorityRecommendations(maxPriority, queueDistribution)
      });

    } catch (error) {
      console.error('Get priority levels error:', error);
      return NextResponse.json({
        error: 'Failed to retrieve priority information',
        details: error.message
      }, { status: 500 });
    }
  }, [
    QueueMiddleware.validateLicenseFeatures
  ]);

  return handler(request);
}

function generatePriorityRecommendations(maxPriority, queueDistribution) {
  const recommendations = [];
  
  // Find the priority level with shortest queue
  const shortestQueue = queueDistribution.reduce((min, current) => 
    current.job_count < min.job_count ? current : min, 
    { job_count: Infinity, effective_priority: maxPriority }
  );

  if (shortestQueue.job_count < Infinity) {
    recommendations.push({
      type: 'optimal_priority',
      priority_level: shortestQueue.effective_priority,
      reason: `Priority level ${shortestQueue.effective_priority} has the shortest queue (${shortestQueue.job_count} jobs)`,
      estimated_wait: Math.round(shortestQueue.avg_wait_minutes || 0)
    });
  }

  // Recommend upgrade if user is at basic level
  if (maxPriority === 0) {
    recommendations.push({
      type: 'upgrade_suggestion',
      reason: 'Consider upgrading your license for higher priority access and faster processing',
      benefits: ['Higher priority levels', 'Shorter wait times', 'Advanced scheduling features']
    });
  }

  // Warn about high queue times
  const highWaitTime = queueDistribution.find(q => q.avg_wait_minutes > 60);
  if (highWaitTime) {
    recommendations.push({
      type: 'timing_suggestion',
      reason: 'Queue wait times are currently high. Consider scheduling your jobs for off-peak hours.',
      suggested_times: ['Early morning (6-8 AM)', 'Late evening (10 PM - 12 AM)', 'Weekend mornings']
    });
  }

  return recommendations;
}