import { NextResponse } from 'next/server';
const { getDatabase } = require('../../../../lib/database');
const { getAuthManager } = require('../../../../lib/auth');

export async function GET(request) {
  try {
    const auth = getAuthManager();
    
    // Check authentication and admin role
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const session = auth.validateSession(token);

    if (session.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const db = getDatabase();
    
    // Get query parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit')) || 50;
    const offset = parseInt(url.searchParams.get('offset')) || 0;

    const keys = db.getLicenseKeys(limit, offset);

    return NextResponse.json({
      success: true,
      keys,
      pagination: {
        limit,
        offset,
        hasMore: keys.length === limit
      }
    });

  } catch (error) {
    console.error('Get keys error:', error);
    return NextResponse.json(
      { success: false, error: error.message },
      { status: error.message === 'Invalid session' ? 401 : 500 }
    );
  }
}

export async function POST(request) {
  try {
    const auth = getAuthManager();
    
    // Check authentication and admin role
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const session = auth.validateSession(token);

    if (session.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { duration, maxUses, priority_level, max_accounts_per_batch, scheduling_access } = await request.json();

    // Validate input
    if (!duration || duration < 1) {
      return NextResponse.json(
        { success: false, error: 'Duration must be at least 1 day' },
        { status: 400 }
      );
    }

    if (!maxUses || maxUses < 1) {
      return NextResponse.json(
        { success: false, error: 'Max uses must be at least 1' },
        { status: 400 }
      );
    }

    if (priority_level < 0 || priority_level > 10) {
      return NextResponse.json(
        { success: false, error: 'Priority level must be between 0 and 10' },
        { status: 400 }
      );
    }

    if (max_accounts_per_batch < 0 || max_accounts_per_batch > 1000) {
      return NextResponse.json(
        { success: false, error: 'Max accounts per batch must be between 0 and 1000' },
        { status: 400 }
      );
    }

    const db = getDatabase();
    
    // Create license key
    const keyData = db.createLicenseKey(
      parseInt(duration),
      parseInt(maxUses),
      [],
      session.userId
    );

    // Set license features
    db.setLicenseFeatures(keyData.id, {
      max_accounts_per_batch: parseInt(max_accounts_per_batch),
      priority_level: parseInt(priority_level),
      scheduling_access: scheduling_access
    });

    // Log activity
    db.logActivity(
      session.userId,
      'CREATE_LICENSE_KEY',
      `Created license key: ${keyData.keyCode} (${duration} days, ${maxUses} uses)`,
      auth.getClientIP(request)
    );

    return NextResponse.json({
      success: true,
      key: keyData,
      message: 'License key created successfully'
    });

  } catch (error) {
    console.error('Create key error:', error);
    return NextResponse.json(
      { success: false, error: error.message },
      { status: error.message === 'Invalid session' ? 401 : 500 }
    );
  }
}

export async function PATCH(request) {
  try {
    const auth = getAuthManager();
    
    // Check authentication and admin role
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const session = auth.validateSession(token);

    if (session.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { keyId, action } = await request.json();

    if (!keyId || !action) {
      return NextResponse.json(
        { success: false, error: 'Key ID and action are required' },
        { status: 400 }
      );
    }

    if (action === 'deactivate') {
      const db = getDatabase();
      
      // Deactivate license key
      const result = db.deactivateLicenseKey(keyId);

      if (result.changes === 0) {
        return NextResponse.json(
          { success: false, error: 'License key not found' },
          { status: 404 }
        );
      }

      // Log activity
      db.logActivity(
        session.userId,
        'DEACTIVATE_LICENSE_KEY',
        `Deactivated license key ID: ${keyId}`,
        auth.getClientIP(request)
      );

      return NextResponse.json({
        success: true,
        message: 'License key deactivated successfully'
      });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Deactivate key error:', error);
    return NextResponse.json(
      { success: false, error: error.message },
      { status: error.message === 'Invalid session' ? 401 : 500 }
    );
  }
}