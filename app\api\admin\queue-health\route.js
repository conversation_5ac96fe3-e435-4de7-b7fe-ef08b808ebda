import { NextResponse } from 'next/server';
const { getDatabase } = require('../../../../lib/database');
const { getWebhookManager } = require('../../../../lib/webhook');
const { getAuthManager } = require('../../../../lib/auth');
const { getQueueProcessor } = require('../../../../lib/queueProcessor');

// Middleware wrapper for Next.js API routes
function withMiddleware(handler) {
  return async (request, context) => {
    try {
      const auth = getAuthManager();
      
      // Get token from header
      const authHeader = request.headers.get('authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json({ error: 'No token provided' }, { status: 401 });
      }

      const token = authHeader.substring(7);
      const session = auth.validateSession(token);
      
      if (!session) {
        return NextResponse.json({ error: 'Invalid session' }, { status: 401 });
      }

      const req = {
        ...request,
        body: await request.json().catch(() => ({})),
        user: session,
        url: request.url
      };

      return handler(req, context);
    } catch (error) {
      console.error('Admin auth error:', error);
      return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
    }
  };
}

// GET - Get queue health metrics and status
export async function GET(request) {
  const handler = withMiddleware(async (req) => {
    try {
      // Check admin access
      if (req.user.role !== 'admin') {
        return NextResponse.json({
          error: 'Admin access required'
        }, { status: 403 });
      }

      const db = getDatabase();
      const queueProcessor = getQueueProcessor();

      // Get queue statistics
      const queueStats = {
        queued: db.db.prepare('SELECT COUNT(*) as count FROM queue_jobs WHERE status = ?').get('queued').count,
        processing: db.db.prepare('SELECT COUNT(*) as count FROM queue_jobs WHERE status = ?').get('processing').count,
        completed: db.db.prepare('SELECT COUNT(*) as count FROM queue_jobs WHERE status = ?').get('completed').count,
        failed: db.db.prepare('SELECT COUNT(*) as count FROM queue_jobs WHERE status = ?').get('failed').count,
        total: db.db.prepare('SELECT COUNT(*) as count FROM queue_jobs').get().count
      };

      // Get batch statistics
      const batchStats = {
        pending: db.db.prepare('SELECT COUNT(*) as count FROM queue_batches WHERE status = ?').get('pending').count,
        processing: db.db.prepare('SELECT COUNT(*) as count FROM queue_batches WHERE status = ?').get('processing').count,
        completed: db.db.prepare('SELECT COUNT(*) as count FROM queue_batches WHERE status = ?').get('completed').count,
        failed: db.db.prepare('SELECT COUNT(*) as count FROM queue_batches WHERE status = ?').get('failed').count,
        total: db.db.prepare('SELECT COUNT(*) as count FROM queue_batches').get().count
      };

      // Get dead letter queue statistics
      const dlqStats = {
        failed: db.db.prepare('SELECT COUNT(*) as count FROM dead_letter_queue WHERE status = ?').get('failed').count,
        investigating: db.db.prepare('SELECT COUNT(*) as count FROM dead_letter_queue WHERE status = ?').get('investigating').count,
        resolved: db.db.prepare('SELECT COUNT(*) as count FROM dead_letter_queue WHERE status = ?').get('resolved').count,
        discarded: db.db.prepare('SELECT COUNT(*) as count FROM dead_letter_queue WHERE status = ?').get('discarded').count,
        total: db.db.prepare('SELECT COUNT(*) as count FROM dead_letter_queue').get().count
      };

      // Get recent activity (last 24 hours)
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = yesterday.toISOString();

      const recentActivity = {
        jobs_created: db.db.prepare('SELECT COUNT(*) as count FROM queue_jobs WHERE created_at >= ?').get(yesterdayStr).count,
        jobs_completed: db.db.prepare('SELECT COUNT(*) as count FROM queue_jobs WHERE status = ? AND completed_at >= ?').get('completed', yesterdayStr).count,
        jobs_failed: db.db.prepare('SELECT COUNT(*) as count FROM queue_jobs WHERE status = ? AND updated_at >= ?').get('failed', yesterdayStr).count,
        batches_created: db.db.prepare('SELECT COUNT(*) as count FROM queue_batches WHERE created_at >= ?').get(yesterdayStr).count,
        batches_completed: db.db.prepare('SELECT COUNT(*) as count FROM queue_batches WHERE status = ? AND completed_at >= ?').get('completed', yesterdayStr).count
      };

      // Get system configuration
      const systemConfig = db.getAllSystemConfig();
      const configMap = systemConfig.reduce((acc, config) => {
        acc[config.config_key] = config.config_value;
        return acc;
      }, {});

      // Get queue processor health metrics
      const processorHealth = {
        is_processing: queueProcessor.isProcessing,
        currently_processing_count: queueProcessor.currentlyProcessing.size,
        max_concurrent_jobs: queueProcessor.maxConcurrentJobs,
        browser_timeout_minutes: queueProcessor.browserTimeoutMinutes,
        circuit_breaker_status: queueProcessor.circuitBreaker.getStatus(),
        health_metrics: queueProcessor.healthMetrics
      };

      // Calculate success rate
      const totalProcessed = processorHealth.health_metrics.totalJobsProcessed;
      const successRate = totalProcessed > 0 ? 
        (processorHealth.health_metrics.successfulJobs / totalProcessed * 100).toFixed(2) : 0;

      // Determine overall health status
      let healthStatus = 'healthy';
      const issues = [];

      if (queueStats.failed > queueStats.completed * 0.1) {
        healthStatus = 'warning';
        issues.push('High failure rate detected');
      }

      if (processorHealth.circuit_breaker_status.state === 'OPEN') {
        healthStatus = 'critical';
        issues.push('Circuit breaker is open - external services failing');
      }

      if (queueStats.processing > processorHealth.max_concurrent_jobs) {
        healthStatus = 'warning';
        issues.push('More jobs processing than configured limit');
      }

      if (dlqStats.failed > 50) {
        healthStatus = 'warning';
        issues.push('High number of jobs in dead letter queue');
      }

      return NextResponse.json({
        success: true,
        health_status: healthStatus,
        issues: issues,
        timestamp: new Date().toISOString(),
        queue_statistics: queueStats,
        batch_statistics: batchStats,
        dead_letter_queue_statistics: dlqStats,
        recent_activity: recentActivity,
        processor_health: processorHealth,
        success_rate: `${successRate}%`,
        system_configuration: configMap,
        uptime: process.uptime(),
        memory_usage: process.memoryUsage()
      });

    } catch (error) {
      console.error('Error fetching queue health:', error);
      return NextResponse.json({
        error: 'Failed to fetch queue health metrics',
        details: error.message
      }, { status: 500 });
    }
  });

  return handler(request);
}

// POST - Perform health check actions (restart processor, clear circuit breaker, etc.)
export async function POST(request) {
  const handler = withMiddleware(async (req) => {
    try {
      // Check admin access
      if (req.user.role !== 'admin') {
        return NextResponse.json({
          error: 'Admin access required'
        }, { status: 403 });
      }

      const db = getDatabase();
      const webhook = getWebhookManager();
      const queueProcessor = getQueueProcessor();
      const { action } = req.body;

      if (!action) {
        return NextResponse.json({
          error: 'action is required'
        }, { status: 400 });
      }

      let result = { success: true, message: '' };

      switch (action) {
        case 'restart_processor':
          queueProcessor.stop();
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
          queueProcessor.start();
          result.message = 'Queue processor restarted successfully';
          break;

        case 'clear_circuit_breaker':
          queueProcessor.circuitBreaker.onSuccess(); // Reset circuit breaker
          result.message = 'Circuit breaker reset successfully';
          break;

        case 'force_cleanup':
          // Force cleanup of stuck jobs
          const stuckJobs = db.db.prepare(`
            UPDATE queue_jobs 
            SET status = 'failed', error_message = 'Force cleaned by admin', updated_at = CURRENT_TIMESTAMP
            WHERE status = 'processing' AND updated_at < datetime('now', '-30 minutes')
          `).run();
          result.message = `Force cleaned ${stuckJobs.changes} stuck jobs`;
          break;

        case 'clear_timeouts':
          queueProcessor.jobTimeouts.clear();
          result.message = 'Job timeouts cleared';
          break;

        default:
          return NextResponse.json({
            error: `Unknown action: ${action}`
          }, { status: 400 });
      }

      // Log admin action
      db.logActivity(
        req.user.id,
        'ADMIN_QUEUE_HEALTH_ACTION',
        `Performed ${action}: ${result.message}`
      );

      // Send webhook notification
      await webhook.sendDiscordNotification(
        '🔧 Queue Health Action',
        `Admin **${req.user.username}** performed **${action}**`,
        0x3498db, // Blue color
        [
          { name: 'Action', value: action, inline: true },
          { name: 'Result', value: result.message, inline: false },
          { name: 'Admin', value: req.user.username, inline: true }
        ]
      );

      return NextResponse.json(result);

    } catch (error) {
      console.error('Queue health action error:', error);
      
      const webhook = getWebhookManager();
      await webhook.sendQueueAlert(
        'system_error',
        'Queue health action failed',
        {
          admin_user: req.user?.username || 'unknown',
          action: req.body?.action || 'unknown',
          error: error.message
        }
      );

      return NextResponse.json({
        error: 'Queue health action failed',
        details: error.message
      }, { status: 500 });
    }
  });

  return handler(request);
}
