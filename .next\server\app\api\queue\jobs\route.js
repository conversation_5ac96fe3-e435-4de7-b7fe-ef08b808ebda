"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/queue/jobs/route";
exports.ids = ["app/api/queue/jobs/route"];
exports.modules = {

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("better-sqlite3");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueue%2Fjobs%2Froute&page=%2Fapi%2Fqueue%2Fjobs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueue%2Fjobs%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueue%2Fjobs%2Froute&page=%2Fapi%2Fqueue%2Fjobs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueue%2Fjobs%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var D_reader_auto_main_app_api_queue_jobs_route_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./app/api/queue/jobs/route.js */ \"(rsc)/./app/api/queue/jobs/route.js\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/queue/jobs/route\",\n        pathname: \"/api/queue/jobs\",\n        filename: \"route\",\n        bundlePath: \"app/api/queue/jobs/route\"\n    },\n    resolvedPagePath: \"D:\\\\reader-auto-main\\\\app\\\\api\\\\queue\\\\jobs\\\\route.js\",\n    nextConfigOutput,\n    userland: D_reader_auto_main_app_api_queue_jobs_route_js__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/queue/jobs/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueue%2Fjobs%2Froute&page=%2Fapi%2Fqueue%2Fjobs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueue%2Fjobs%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/queue/jobs/route.js":
/*!*************************************!*\
  !*** ./app/api/queue/jobs/route.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\nconst { getDatabase } = __webpack_require__(/*! ../../../../lib/database */ \"(rsc)/./lib/database.js\");\nconst { getAuthManager } = __webpack_require__(/*! ../../../../lib/auth */ \"(rsc)/./lib/auth.js\");\n// Middleware function for authentication\nasync function withMiddleware(request, handler) {\n    try {\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Missing or invalid authorization header\"\n            }, {\n                status: 401\n            });\n        }\n        const token = authHeader.substring(7);\n        const authManager = getAuthManager();\n        const user = await authManager.verifyToken(token);\n        if (!user) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid or expired token\"\n            }, {\n                status: 401\n            });\n        }\n        return await handler(request, user);\n    } catch (error) {\n        console.error(\"Middleware error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Authentication failed\"\n        }, {\n            status: 401\n        });\n    }\n}\n// POST /api/queue/jobs - Create a new queue job\nasync function POST(request) {\n    return withMiddleware(request, async (request, user)=>{\n        try {\n            const body = await request.json();\n            const { job_type, job_data, srp_target = 100, priority = 0 } = body;\n            // Validate required fields\n            if (!job_type || !job_data) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    success: false,\n                    error: \"Missing required fields: job_type and job_data\"\n                }, {\n                    status: 400\n                });\n            }\n            // Validate job_data has required fields\n            const { school, email, password, login_type, bookTitle } = job_data;\n            if (!school || !email || !password || !login_type || !bookTitle) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    success: false,\n                    error: \"Missing required job_data fields: school, email, password, login_type, bookTitle\"\n                }, {\n                    status: 400\n                });\n            }\n            // Validate SRP target\n            if (srp_target < 1 || srp_target > 400) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    success: false,\n                    error: \"SRP target must be between 1 and 400\"\n                }, {\n                    status: 400\n                });\n            }\n            const db = getDatabase();\n            // Check daily main job limit using system config\n            const mainJobLimitConfig = db.getSystemConfig(\"default_max_main_jobs_per_day\");\n            const maxMainJobsPerDay = mainJobLimitConfig ? mainJobLimitConfig.config_value : 5;\n            const today = new Date().toISOString().split(\"T\")[0];\n            const mainJobCountStmt = db.db.prepare(`\n        SELECT COUNT(*) as count\n        FROM queue_jobs\n        WHERE user_id = ? AND DATE(created_at) = ? AND batch_id IS NULL\n      `);\n            const mainJobCount = mainJobCountStmt.get(user.id, today);\n            if (mainJobCount.count >= maxMainJobsPerDay) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    success: false,\n                    error: \"Daily main job limit reached\",\n                    current_count: mainJobCount.count,\n                    max_allowed: maxMainJobsPerDay,\n                    message: `You have reached your daily limit of ${maxMainJobsPerDay} main automation job${maxMainJobsPerDay > 1 ? \"s\" : \"\"}. Please try again tomorrow.`\n                }, {\n                    status: 429\n                });\n            }\n            // Create the queue job\n            const jobId = db.createQueueJob({\n                username: user.username,\n                job_type,\n                job_data: JSON.stringify(job_data),\n                srp_target,\n                priority,\n                status: \"queued\"\n            });\n            console.log(`✅ Queue job created: ID ${jobId} for user ${user.username}`);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                job_id: jobId,\n                message: \"Job added to queue successfully\"\n            });\n        } catch (error) {\n            console.error(\"Error creating queue job:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Failed to create queue job\"\n            }, {\n                status: 500\n            });\n        }\n    });\n}\n// GET /api/queue/jobs - Get user's queue jobs\nasync function GET(request) {\n    return withMiddleware(request, async (request, user)=>{\n        try {\n            const db = getDatabase();\n            const jobs = db.getQueueJobs().filter((job)=>job.username === user.username);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                jobs: jobs.map((job)=>({\n                        id: job.id,\n                        job_type: job.job_type,\n                        status: job.status,\n                        priority: job.priority,\n                        srp_target: job.srp_target,\n                        created_at: job.created_at,\n                        started_at: job.started_at,\n                        completed_at: job.completed_at,\n                        retry_count: job.retry_count,\n                        max_retries: job.max_retries\n                    }))\n            });\n        } catch (error) {\n            console.error(\"Error fetching queue jobs:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"Failed to fetch queue jobs\"\n            }, {\n                status: 500\n            });\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/queue/jobs/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/auth.js":
/*!*********************!*\
  !*** ./lib/auth.js ***!
  \*********************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst jwt = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst { getDatabase } = __webpack_require__(/*! ./database */ \"(rsc)/./lib/database.js\");\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key-change-this-in-production\";\nconst TOKEN_EXPIRY = \"7d\"; // 7 days\nclass AuthManager {\n    constructor(){\n        this.db = getDatabase();\n    }\n    // Generate JWT token\n    generateToken(user) {\n        const payload = {\n            userId: user.id,\n            username: user.username,\n            role: user.role,\n            iat: Math.floor(Date.now() / 1000)\n        };\n        return jwt.sign(payload, JWT_SECRET, {\n            expiresIn: TOKEN_EXPIRY\n        });\n    }\n    // Verify JWT token\n    verifyToken(token) {\n        try {\n            return jwt.verify(token, JWT_SECRET);\n        } catch (error) {\n            throw new Error(\"Invalid token\");\n        }\n    }\n    // Hash token for database storage\n    hashToken(token) {\n        return crypto.createHash(\"sha256\").update(token).digest(\"hex\");\n    }\n    // Create session with token\n    createSession(user, ipAddress = null, userAgent = null) {\n        const token = this.generateToken(user);\n        const tokenHash = this.hashToken(token);\n        // Calculate expiry date\n        const expiresAt = new Date();\n        expiresAt.setDate(expiresAt.getDate() + 7); // 7 days\n        // Store session in database\n        this.db.createSession(user.id, tokenHash, expiresAt.toISOString(), ipAddress, userAgent);\n        // Log activity\n        this.db.logActivity(user.id, \"LOGIN\", `User logged in from ${ipAddress}`, ipAddress);\n        return {\n            token,\n            user: {\n                id: user.id,\n                username: user.username,\n                role: user.role,\n                lastLogin: user.last_login\n            },\n            expiresAt: expiresAt.toISOString()\n        };\n    }\n    // Validate session\n    validateSession(token) {\n        try {\n            // First verify JWT\n            const decoded = this.verifyToken(token);\n            // Then check database session\n            const tokenHash = this.hashToken(token);\n            const session = this.db.validateSession(tokenHash);\n            if (!session || !session.user_active) {\n                throw new Error(\"Session invalid or user inactive\");\n            }\n            return {\n                userId: session.user_id,\n                username: session.username,\n                role: session.role,\n                sessionId: session.id\n            };\n        } catch (error) {\n            throw new Error(\"Invalid session\");\n        }\n    }\n    // Logout user\n    logout(token, userId = null) {\n        const tokenHash = this.hashToken(token);\n        this.db.invalidateSession(tokenHash);\n        if (userId) {\n            this.db.logActivity(userId, \"LOGOUT\", \"User logged out\");\n        }\n    }\n    // Logout all sessions for user\n    logoutAllSessions(userId) {\n        this.db.invalidateAllUserSessions(userId);\n        this.db.logActivity(userId, \"LOGOUT_ALL\", \"All sessions invalidated\");\n    }\n    // Middleware for protecting routes\n    requireAuth(requiredRole = null) {\n        return async (req, res, next)=>{\n            try {\n                const authHeader = req.headers.authorization;\n                if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n                    return res.status(401).json({\n                        error: \"No token provided\"\n                    });\n                }\n                const token = authHeader.substring(7);\n                const session = await this.validateSession(token);\n                // Check role if required\n                if (requiredRole && session.role !== requiredRole) {\n                    return res.status(403).json({\n                        error: \"Insufficient permissions\"\n                    });\n                }\n                // Add user info to request\n                req.user = session;\n                req.token = token;\n                next();\n            } catch (error) {\n                return res.status(401).json({\n                    error: error.message\n                });\n            }\n        };\n    }\n    // Admin only middleware\n    requireAdmin() {\n        return this.requireAuth(\"admin\");\n    }\n    // Extract IP address from request (Next.js compatible)\n    getClientIP(req) {\n        // For Next.js App Router requests\n        if (req.headers && typeof req.headers.get === \"function\") {\n            return req.headers.get(\"x-forwarded-for\") || req.headers.get(\"x-real-ip\") || req.ip || \"127.0.0.1\";\n        }\n        // For traditional Node.js requests\n        return req.headers[\"x-forwarded-for\"] || req.connection?.remoteAddress || req.socket?.remoteAddress || (req.connection?.socket ? req.connection.socket.remoteAddress : null) || \"127.0.0.1\";\n    }\n    // Extract user agent (Next.js compatible)\n    getUserAgent(req) {\n        // For Next.js App Router requests\n        if (req.headers && typeof req.headers.get === \"function\") {\n            return req.headers.get(\"user-agent\") || \"Unknown\";\n        }\n        // For traditional Node.js requests\n        return req.headers[\"user-agent\"] || \"Unknown\";\n    }\n    // Rate limiting helper\n    checkRateLimit(identifier, maxAttempts = 5, windowMinutes = 15) {\n        // This is a simple in-memory rate limiter\n        // In production, you might want to use Redis or database\n        if (!this.rateLimitStore) {\n            this.rateLimitStore = new Map();\n        }\n        const now = Date.now();\n        const windowMs = windowMinutes * 60 * 1000;\n        const key = `rate_limit_${identifier}`;\n        if (!this.rateLimitStore.has(key)) {\n            this.rateLimitStore.set(key, {\n                count: 1,\n                resetTime: now + windowMs\n            });\n            return {\n                allowed: true,\n                remaining: maxAttempts - 1\n            };\n        }\n        const record = this.rateLimitStore.get(key);\n        if (now > record.resetTime) {\n            // Reset the window\n            this.rateLimitStore.set(key, {\n                count: 1,\n                resetTime: now + windowMs\n            });\n            return {\n                allowed: true,\n                remaining: maxAttempts - 1\n            };\n        }\n        if (record.count >= maxAttempts) {\n            return {\n                allowed: false,\n                remaining: 0,\n                resetTime: record.resetTime\n            };\n        }\n        record.count++;\n        return {\n            allowed: true,\n            remaining: maxAttempts - record.count\n        };\n    }\n    // Clean up expired rate limit entries\n    cleanupRateLimit() {\n        if (!this.rateLimitStore) return;\n        const now = Date.now();\n        for (const [key, record] of this.rateLimitStore.entries()){\n            if (now > record.resetTime) {\n                this.rateLimitStore.delete(key);\n            }\n        }\n    }\n    // Password strength validation\n    validatePasswordStrength(password) {\n        const minLength = 8;\n        const hasUpperCase = /[A-Z]/.test(password);\n        const hasLowerCase = /[a-z]/.test(password);\n        const hasNumbers = /\\d/.test(password);\n        const hasSpecialChar = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n        const errors = [];\n        if (password.length < minLength) {\n            errors.push(`Password must be at least ${minLength} characters long`);\n        }\n        if (!hasUpperCase) {\n            errors.push(\"Password must contain at least one uppercase letter\");\n        }\n        if (!hasLowerCase) {\n            errors.push(\"Password must contain at least one lowercase letter\");\n        }\n        if (!hasNumbers) {\n            errors.push(\"Password must contain at least one number\");\n        }\n        if (!hasSpecialChar) {\n            errors.push(\"Password must contain at least one special character\");\n        }\n        return {\n            isValid: errors.length === 0,\n            errors,\n            strength: this.calculatePasswordStrength(password)\n        };\n    }\n    calculatePasswordStrength(password) {\n        let score = 0;\n        // Length bonus\n        score += Math.min(password.length * 2, 20);\n        // Character variety bonus\n        if (/[a-z]/.test(password)) score += 5;\n        if (/[A-Z]/.test(password)) score += 5;\n        if (/[0-9]/.test(password)) score += 5;\n        if (/[^A-Za-z0-9]/.test(password)) score += 10;\n        // Penalty for common patterns\n        if (/(.)\\1{2,}/.test(password)) score -= 10; // Repeated characters\n        if (/123|abc|qwe/i.test(password)) score -= 10; // Sequential patterns\n        if (score < 30) return \"weak\";\n        if (score < 60) return \"medium\";\n        return \"strong\";\n    }\n}\n// Export singleton instance\nlet authInstance = null;\nfunction getAuthManager() {\n    if (!authInstance) {\n        authInstance = new AuthManager();\n    }\n    return authInstance;\n}\nmodule.exports = {\n    getAuthManager,\n    AuthManager\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.js\n");

/***/ }),

/***/ "(rsc)/./lib/database.js":
/*!*************************!*\
  !*** ./lib/database.js ***!
  \*************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst Database = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\nconst bcrypt = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/umd/index.js\");\nconst { v4: uuidv4 } = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/cjs/index.js\");\nconst path = __webpack_require__(/*! path */ \"path\");\nclass DatabaseManager {\n    constructor(){\n        const dbPath = path.join(process.cwd(), \"data\", \"app.db\");\n        this.db = new Database(dbPath);\n        this.initializeTables();\n        this.createDefaultAdmin();\n    }\n    initializeTables() {\n        // Users table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS users (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        username TEXT UNIQUE NOT NULL,\n        password_hash TEXT NOT NULL,\n        role TEXT DEFAULT 'user' CHECK(role IN ('user', 'admin')),\n        license_key_id INTEGER,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        is_active BOOLEAN DEFAULT 1,\n        last_login DATETIME,\n        FOREIGN KEY (license_key_id) REFERENCES license_keys (id)\n      )\n    `);\n        // License keys table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS license_keys (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        key_code TEXT UNIQUE NOT NULL,\n        duration_days INTEGER NOT NULL,\n        max_uses INTEGER NOT NULL DEFAULT 1,\n        current_uses INTEGER DEFAULT 0,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        expires_at DATETIME NOT NULL,\n        is_active BOOLEAN DEFAULT 1,\n        created_by INTEGER,\n        features TEXT DEFAULT '[]',\n        FOREIGN KEY (created_by) REFERENCES users (id)\n      )\n    `);\n        // User sessions table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS user_sessions (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        token_hash TEXT NOT NULL,\n        expires_at DATETIME NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        ip_address TEXT,\n        user_agent TEXT,\n        is_active BOOLEAN DEFAULT 1,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Activity logs table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS activity_logs (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER,\n        action TEXT NOT NULL,\n        details TEXT,\n        ip_address TEXT,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // System settings table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS system_settings (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        setting_key TEXT UNIQUE NOT NULL,\n        setting_value TEXT,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_by INTEGER,\n        FOREIGN KEY (updated_by) REFERENCES users (id)\n      )\n    `);\n        // Encrypted login credentials table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS encrypted_credentials (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        login_key TEXT UNIQUE NOT NULL,\n        login_method TEXT NOT NULL CHECK(login_method IN ('normal', 'microsoft', 'google')),\n        encrypted_school TEXT NOT NULL,\n        encrypted_email TEXT NOT NULL,\n        encrypted_password TEXT NOT NULL,\n        encryption_iv TEXT NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        is_active BOOLEAN DEFAULT 1,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // License feature settings table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS license_feature_settings (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        license_key_id INTEGER NOT NULL,\n        max_accounts_per_batch INTEGER DEFAULT 0,\n        priority_level INTEGER DEFAULT 0 CHECK(priority_level >= 0 AND priority_level <= 10),\n        scheduling_access BOOLEAN DEFAULT 0,\n        multi_user_access BOOLEAN DEFAULT 0,\n        max_batches_per_day INTEGER DEFAULT 1,\n        max_schedules_per_week INTEGER DEFAULT 1,\n        max_main_jobs_per_day INTEGER DEFAULT 5,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (license_key_id) REFERENCES license_keys (id),\n        UNIQUE(license_key_id)\n      )\n    `);\n        // System configuration table for admin settings\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS system_config (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        config_key TEXT UNIQUE NOT NULL,\n        config_value TEXT NOT NULL,\n        config_type TEXT DEFAULT 'string' CHECK(config_type IN ('string', 'number', 'boolean', 'json')),\n        description TEXT,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Dead letter queue table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS dead_letter_queue (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        original_job_id INTEGER,\n        job_type TEXT NOT NULL,\n        job_data TEXT NOT NULL,\n        user_id INTEGER NOT NULL,\n        failure_reason TEXT,\n        failure_count INTEGER DEFAULT 1,\n        last_failure_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        status TEXT DEFAULT 'failed' CHECK(status IN ('failed', 'investigating', 'resolved', 'discarded')),\n        FOREIGN KEY (user_id) REFERENCES users (id),\n        FOREIGN KEY (original_job_id) REFERENCES queue_jobs (id)\n      )\n    `);\n        // Queue batches table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_batches (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        batch_name TEXT,\n        login_type TEXT DEFAULT 'normal' CHECK(login_type IN ('normal', 'google', 'microsoft')),\n        total_accounts INTEGER NOT NULL,\n        processed_accounts INTEGER DEFAULT 0,\n        failed_accounts INTEGER DEFAULT 0,\n        status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),\n        priority_level INTEGER DEFAULT 0,\n        srp_target INTEGER DEFAULT 100,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        started_at DATETIME,\n        completed_at DATETIME,\n        scheduled_time DATETIME,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Add srp_target column to queue_batches if it doesn't exist\n        try {\n            this.db.exec(`ALTER TABLE queue_batches ADD COLUMN srp_target INTEGER DEFAULT 100`);\n        } catch (error) {\n        // Column already exists, ignore error\n        }\n        // Queue jobs table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_jobs (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        batch_id INTEGER,\n        user_id INTEGER NOT NULL,\n        job_type TEXT NOT NULL DEFAULT 'sparx_reader',\n        job_data TEXT NOT NULL,\n        status TEXT DEFAULT 'queued' CHECK(status IN ('queued', 'processing', 'completed', 'failed', 'cancelled')),\n        priority_level INTEGER DEFAULT 0,\n        effective_priority INTEGER DEFAULT 0,\n        srp_target INTEGER DEFAULT 100,\n        scheduled_time DATETIME,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        started_at DATETIME,\n        completed_at DATETIME,\n        error_message TEXT,\n        retry_count INTEGER DEFAULT 0,\n        max_retries INTEGER DEFAULT 3,\n        FOREIGN KEY (batch_id) REFERENCES queue_batches (id),\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Add srp_target column to queue_jobs if it doesn't exist\n        try {\n            this.db.exec(`ALTER TABLE queue_jobs ADD COLUMN srp_target INTEGER DEFAULT 100`);\n        } catch (error) {\n        // Column already exists, ignore error\n        }\n        // Queue schedules table for conflict detection\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_schedules (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        scheduled_time DATETIME NOT NULL,\n        duration_minutes INTEGER DEFAULT 30,\n        srp_target INTEGER DEFAULT 100,\n        job_id INTEGER,\n        batch_id INTEGER,\n        status TEXT DEFAULT 'scheduled' CHECK(status IN ('scheduled', 'active', 'completed', 'cancelled')),\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users (id),\n        FOREIGN KEY (job_id) REFERENCES queue_jobs (id),\n        FOREIGN KEY (batch_id) REFERENCES queue_batches (id)\n      )\n    `);\n        // Add srp_target column if it doesn't exist (for existing databases)\n        try {\n            this.db.exec(`ALTER TABLE queue_schedules ADD COLUMN srp_target INTEGER DEFAULT 100`);\n        } catch (error) {\n        // Column already exists, ignore error\n        }\n        // Create indexes for better performance\n        this.db.exec(`\n      CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);\n      CREATE INDEX IF NOT EXISTS idx_users_license_key ON users(license_key_id);\n      CREATE INDEX IF NOT EXISTS idx_license_keys_code ON license_keys(key_code);\n      CREATE INDEX IF NOT EXISTS idx_license_keys_expires ON license_keys(expires_at);\n      CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(token_hash);\n      CREATE INDEX IF NOT EXISTS idx_sessions_user ON user_sessions(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_user ON activity_logs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_created ON activity_logs(created_at);\n      CREATE INDEX IF NOT EXISTS idx_encrypted_credentials_user ON encrypted_credentials(user_id);\n      CREATE INDEX IF NOT EXISTS idx_encrypted_credentials_key ON encrypted_credentials(login_key);\n      CREATE INDEX IF NOT EXISTS idx_license_features ON license_feature_settings(license_key_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_user ON queue_batches(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_status ON queue_batches(status);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_scheduled ON queue_batches(scheduled_time);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_batch ON queue_jobs(batch_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_user ON queue_jobs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_status ON queue_jobs(status);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_priority ON queue_jobs(effective_priority);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_scheduled ON queue_jobs(scheduled_time);\n      CREATE INDEX IF NOT EXISTS idx_queue_schedules_user ON queue_schedules(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_schedules_time ON queue_schedules(scheduled_time);\n    `);\n        // Migration: Add max_batches_per_day column if it doesn't exist\n        try {\n            const columns = this.db.prepare(\"PRAGMA table_info(license_feature_settings)\").all();\n            const hasMaxBatchesPerDay = columns.some((col)=>col.name === \"max_batches_per_day\");\n            if (!hasMaxBatchesPerDay) {\n                console.log(\"Adding max_batches_per_day column to license_feature_settings...\");\n                this.db.exec(`ALTER TABLE license_feature_settings ADD COLUMN max_batches_per_day INTEGER DEFAULT 1`);\n            }\n        } catch (error) {\n            console.error(\"Migration error for max_batches_per_day:\", error);\n        }\n        // Migration: Add login_type column to queue_batches if it doesn't exist\n        try {\n            const batchColumns = this.db.prepare(\"PRAGMA table_info(queue_batches)\").all();\n            const hasLoginType = batchColumns.some((col)=>col.name === \"login_type\");\n            if (!hasLoginType) {\n                console.log(\"Adding login_type column to queue_batches...\");\n                this.db.exec(`ALTER TABLE queue_batches ADD COLUMN login_type TEXT DEFAULT 'normal' CHECK(login_type IN ('normal', 'google', 'microsoft'))`);\n            }\n        } catch (error) {\n            console.error(\"Migration error for login_type:\", error);\n        }\n    }\n    createDefaultAdmin() {\n        const adminExists = this.db.prepare(\"SELECT id FROM users WHERE role = ? LIMIT 1\").get(\"admin\");\n        if (!adminExists) {\n            const hashedPassword = bcrypt.hashSync(process.env.DEFAULT_ADMIN_PASSWORD, 12);\n            const stmt = this.db.prepare(`\n        INSERT INTO users (username, password_hash, role, is_active)\n        VALUES (?, ?, ?, ?)\n      `);\n            stmt.run(process.env.DEFAULT_ADMIN_USERNAME, hashedPassword, \"admin\", 1);\n            console.log(`Default admin user created: ${process.env.DEFAULT_ADMIN_USERNAME}/[hidden]`);\n        }\n        // Initialize default system configuration\n        this.initializeDefaultConfig();\n    }\n    initializeDefaultConfig() {\n        const defaultConfigs = [\n            {\n                key: \"default_max_main_jobs_per_day\",\n                value: process.env.DEFAULT_MAX_MAIN_JOBS_PER_DAY || \"5\",\n                type: \"number\",\n                description: \"Default daily limit for main automation jobs\"\n            },\n            {\n                key: \"default_max_batches_per_day\",\n                value: process.env.DEFAULT_MAX_BATCHES_PER_DAY || \"1\",\n                type: \"number\",\n                description: \"Default daily limit for batch jobs\"\n            },\n            {\n                key: \"default_max_schedules_per_week\",\n                value: process.env.DEFAULT_MAX_SCHEDULES_PER_WEEK || \"1\",\n                type: \"number\",\n                description: \"Default weekly limit for scheduled jobs\"\n            },\n            {\n                key: \"dead_letter_queue_enabled\",\n                value: \"true\",\n                type: \"boolean\",\n                description: \"Enable dead letter queue for failed jobs\"\n            },\n            {\n                key: \"max_retry_attempts\",\n                value: \"3\",\n                type: \"number\",\n                description: \"Maximum retry attempts before moving to dead letter queue\"\n            },\n            {\n                key: \"circuit_breaker_enabled\",\n                value: \"true\",\n                type: \"boolean\",\n                description: \"Enable circuit breaker for external API calls\"\n            }\n        ];\n        for (const config of defaultConfigs){\n            const existingStmt = this.db.prepare(\"SELECT id FROM system_config WHERE config_key = ?\");\n            const existing = existingStmt.get(config.key);\n            if (!existing) {\n                const insertStmt = this.db.prepare(`\n          INSERT INTO system_config (config_key, config_value, config_type, description)\n          VALUES (?, ?, ?, ?)\n        `);\n                insertStmt.run(config.key, config.value, config.type, config.description);\n            }\n        }\n    }\n    // User management methods\n    createUser(username, password, licenseKey) {\n        const transaction = this.db.transaction(()=>{\n            // Validate license key\n            const licenseStmt = this.db.prepare(`\n        SELECT id, max_uses, current_uses, expires_at, is_active \n        FROM license_keys \n        WHERE key_code = ? AND is_active = 1\n      `);\n            const license = licenseStmt.get(licenseKey);\n            if (!license) {\n                throw new Error(\"Invalid license key\");\n            }\n            if (new Date(license.expires_at) < new Date()) {\n                throw new Error(\"License key has expired\");\n            }\n            if (license.current_uses >= license.max_uses) {\n                throw new Error(\"License key has reached maximum uses\");\n            }\n            // Check if username already exists\n            const userExists = this.db.prepare(\"SELECT id FROM users WHERE username = ?\").get(username);\n            if (userExists) {\n                throw new Error(\"Username already exists\");\n            }\n            // Create user\n            const hashedPassword = bcrypt.hashSync(password, 12);\n            const userStmt = this.db.prepare(`\n        INSERT INTO users (username, password_hash, license_key_id, role)\n        VALUES (?, ?, ?, ?)\n      `);\n            const result = userStmt.run(username, hashedPassword, license.id, \"user\");\n            // Update license key usage\n            const updateLicenseStmt = this.db.prepare(`\n        UPDATE license_keys \n        SET current_uses = current_uses + 1 \n        WHERE id = ?\n      `);\n            updateLicenseStmt.run(license.id);\n            return result.lastInsertRowid;\n        });\n        return transaction();\n    }\n    authenticateUser(username, password) {\n        const stmt = this.db.prepare(`\n      SELECT u.*, lk.expires_at as license_expires, lk.is_active as license_active\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      WHERE u.username = ? AND u.is_active = 1\n    `);\n        const user = stmt.get(username);\n        if (!user) {\n            throw new Error(\"Invalid credentials\");\n        }\n        const isValidPassword = bcrypt.compareSync(password, user.password_hash);\n        if (!isValidPassword) {\n            throw new Error(\"Invalid credentials\");\n        }\n        // Check license validity for non-admin users\n        if (user.role !== \"admin\") {\n            if (!user.license_active || new Date(user.license_expires) < new Date()) {\n                throw new Error(\"License has expired\");\n            }\n        }\n        // Update last login\n        const updateStmt = this.db.prepare(\"UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?\");\n        updateStmt.run(user.id);\n        // Remove sensitive data\n        delete user.password_hash;\n        return user;\n    }\n    // License key management methods\n    createLicenseKey(durationDays, maxUses = 1, features = [], createdBy = null) {\n        const keyCode = this.generateLicenseKey();\n        const expiresAt = new Date();\n        expiresAt.setDate(expiresAt.getDate() + durationDays);\n        const stmt = this.db.prepare(`\n      INSERT INTO license_keys (key_code, duration_days, max_uses, expires_at, created_by, features)\n      VALUES (?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(keyCode, durationDays, maxUses, expiresAt.toISOString(), createdBy, JSON.stringify(features));\n        return {\n            id: result.lastInsertRowid,\n            keyCode,\n            durationDays,\n            maxUses,\n            expiresAt: expiresAt.toISOString(),\n            features\n        };\n    }\n    generateLicenseKey() {\n        const segments = [];\n        for(let i = 0; i < 4; i++){\n            segments.push(uuidv4().replace(/-/g, \"\").substring(0, 8).toUpperCase());\n        }\n        return `SRX-${segments.join(\"-\")}`;\n    }\n    getLicenseKeys(limit = 50, offset = 0) {\n        const stmt = this.db.prepare(`\n      SELECT \n        lk.*,\n        u.username as created_by_username,\n        COUNT(users.id) as users_count\n      FROM license_keys lk\n      LEFT JOIN users u ON lk.created_by = u.id\n      LEFT JOIN users ON users.license_key_id = lk.id\n      GROUP BY lk.id\n      ORDER BY lk.created_at DESC\n      LIMIT ? OFFSET ?\n    `);\n        return stmt.all(limit, offset);\n    }\n    deactivateLicenseKey(keyId) {\n        const stmt = this.db.prepare(\"UPDATE license_keys SET is_active = 0 WHERE id = ?\");\n        return stmt.run(keyId);\n    }\n    // Get detailed license information for a user\n    getUserLicenseStatus(userId) {\n        const stmt = this.db.prepare(`\n      SELECT \n        u.id as user_id,\n        u.username,\n        lk.id as license_id,\n        lk.key_code,\n        lk.max_uses,\n        lk.current_uses,\n        lk.expires_at,\n        lk.is_active,\n        CASE \n          WHEN lk.expires_at <= datetime('now') THEN 'expired'\n          WHEN lk.current_uses >= lk.max_uses THEN 'maxed_out'\n          WHEN lk.is_active = 0 THEN 'inactive'\n          ELSE 'valid'\n        END as license_status\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      WHERE u.id = ?\n    `);\n        return stmt.get(userId);\n    }\n    // Validate a license key for renewal (check if it's valid and has available uses)\n    validateLicenseForRenewal(licenseKey) {\n        const stmt = this.db.prepare(`\n      SELECT \n        id,\n        key_code,\n        max_uses,\n        current_uses,\n        expires_at,\n        is_active,\n        CASE \n          WHEN expires_at <= datetime('now') THEN 'expired'\n          WHEN current_uses >= max_uses THEN 'maxed_out'\n          WHEN is_active = 0 THEN 'inactive'\n          ELSE 'valid'\n        END as status\n      FROM license_keys \n      WHERE key_code = ?\n    `);\n        const license = stmt.get(licenseKey);\n        if (!license) {\n            return {\n                valid: false,\n                error: \"License key not found\"\n            };\n        }\n        if (license.status !== \"valid\") {\n            let errorMessage = \"License key is not valid\";\n            switch(license.status){\n                case \"expired\":\n                    errorMessage = \"License key has expired\";\n                    break;\n                case \"maxed_out\":\n                    errorMessage = \"License key has reached maximum uses\";\n                    break;\n                case \"inactive\":\n                    errorMessage = \"License key is inactive\";\n                    break;\n            }\n            return {\n                valid: false,\n                error: errorMessage\n            };\n        }\n        return {\n            valid: true,\n            license\n        };\n    }\n    // Renew user's license with a new license key\n    renewUserLicense(userId, newLicenseKey) {\n        const transaction = this.db.transaction(()=>{\n            // First validate the new license key\n            const validation = this.validateLicenseForRenewal(newLicenseKey);\n            if (!validation.valid) {\n                throw new Error(validation.error);\n            }\n            const newLicense = validation.license;\n            // Update user's license_key_id to the new license\n            const updateUserStmt = this.db.prepare(`\n        UPDATE users \n        SET license_key_id = ?, updated_at = CURRENT_TIMESTAMP \n        WHERE id = ?\n      `);\n            updateUserStmt.run(newLicense.id, userId);\n            // Increment the new license's current_uses\n            const updateLicenseStmt = this.db.prepare(`\n        UPDATE license_keys \n        SET current_uses = current_uses + 1 \n        WHERE id = ?\n      `);\n            updateLicenseStmt.run(newLicense.id);\n            // Log the renewal activity\n            this.logActivity(userId, \"LICENSE_RENEWED\", `License renewed with key: ${newLicenseKey}`);\n            return {\n                success: true,\n                newLicenseId: newLicense.id,\n                newLicenseKey: newLicenseKey,\n                expiresAt: newLicense.expires_at,\n                maxUses: newLicense.max_uses,\n                currentUses: newLicense.current_uses + 1\n            };\n        });\n        return transaction();\n    }\n    // Session management\n    createSession(userId, tokenHash, expiresAt, ipAddress = null, userAgent = null) {\n        const stmt = this.db.prepare(`\n      INSERT INTO user_sessions (user_id, token_hash, expires_at, ip_address, user_agent)\n      VALUES (?, ?, ?, ?, ?)\n    `);\n        return stmt.run(userId, tokenHash, expiresAt, ipAddress, userAgent);\n    }\n    validateSession(tokenHash) {\n        const stmt = this.db.prepare(`\n      SELECT s.*, u.username, u.role, u.is_active as user_active\n      FROM user_sessions s\n      JOIN users u ON s.user_id = u.id\n      WHERE s.token_hash = ? AND s.is_active = 1 AND s.expires_at > datetime('now')\n    `);\n        return stmt.get(tokenHash);\n    }\n    invalidateSession(tokenHash) {\n        const stmt = this.db.prepare(\"UPDATE user_sessions SET is_active = 0 WHERE token_hash = ?\");\n        return stmt.run(tokenHash);\n    }\n    invalidateAllUserSessions(userId) {\n        const stmt = this.db.prepare(\"UPDATE user_sessions SET is_active = 0 WHERE user_id = ?\");\n        return stmt.run(userId);\n    }\n    // Activity logging\n    logActivity(userId, action, details = null, ipAddress = null) {\n        const stmt = this.db.prepare(`\n      INSERT INTO activity_logs (user_id, action, details, ip_address)\n      VALUES (?, ?, ?, ?)\n    `);\n        return stmt.run(userId, action, details, ipAddress);\n    }\n    // Cleanup expired keys older than 30 days\n    cleanupExpiredKeys() {\n        try {\n            const stmt = this.db.prepare(`\n        DELETE FROM license_keys\n        WHERE datetime(expires_at) < datetime('now', '-30 days')\n      `);\n            const result = stmt.run();\n            if (result.changes > 0) {\n                console.log(`🧹 Cleaned up ${result.changes} expired license keys older than 30 days`);\n                this.logActivity(null, \"SYSTEM_CLEANUP\", `Removed ${result.changes} expired keys older than 30 days`);\n            }\n            return result.changes;\n        } catch (error) {\n            console.error(\"Error cleaning up expired keys:\", error);\n            return 0;\n        }\n    }\n    getActivityLogs(userId = null, limit = 100, offset = 0) {\n        let query = `\n      SELECT \n        al.*,\n        u.username\n      FROM activity_logs al\n      LEFT JOIN users u ON al.user_id = u.id\n    `;\n        const params = [];\n        if (userId) {\n            query += \" WHERE al.user_id = ?\";\n            params.push(userId);\n        }\n        query += \" ORDER BY al.created_at DESC LIMIT ? OFFSET ?\";\n        params.push(limit, offset);\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    // Analytics methods\n    getSystemStats() {\n        const stats = {};\n        // Total users\n        stats.totalUsers = this.db.prepare(\"SELECT COUNT(*) as count FROM users WHERE role = 'user'\").get().count;\n        // Active users (logged in within last 30 days)\n        stats.activeUsers = this.db.prepare(`\n      SELECT COUNT(*) as count FROM users \n      WHERE role = 'user' AND last_login > datetime('now', '-30 days')\n    `).get().count;\n        // Total license keys\n        stats.totalLicenseKeys = this.db.prepare(\"SELECT COUNT(*) as count FROM license_keys\").get().count;\n        // Active license keys\n        stats.activeLicenseKeys = this.db.prepare(`\n      SELECT COUNT(*) as count FROM license_keys \n      WHERE is_active = 1 AND expires_at > datetime('now')\n    `).get().count;\n        // Expired license keys\n        stats.expiredLicenseKeys = this.db.prepare(`\n      SELECT COUNT(*) as count FROM license_keys \n      WHERE expires_at <= datetime('now')\n    `).get().count;\n        // Recent activity (last 24 hours)\n        stats.recentActivity = this.db.prepare(`\n      SELECT COUNT(*) as count FROM activity_logs \n      WHERE created_at > datetime('now', '-1 day')\n    `).get().count;\n        return stats;\n    }\n    // User management for admin\n    getUsers(limit = 50, offset = 0) {\n        const stmt = this.db.prepare(`\n      SELECT \n        u.id,\n        u.username,\n        u.role,\n        u.created_at,\n        u.last_login,\n        u.is_active,\n        lk.key_code,\n        lk.expires_at as license_expires\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      ORDER BY u.created_at DESC\n      LIMIT ? OFFSET ?\n    `);\n        return stmt.all(limit, offset);\n    }\n    toggleUserStatus(userId) {\n        const stmt = this.db.prepare(\"UPDATE users SET is_active = NOT is_active WHERE id = ?\");\n        return stmt.run(userId);\n    }\n    // Cleanup methods\n    cleanupExpiredSessions() {\n        const stmt = this.db.prepare('DELETE FROM user_sessions WHERE expires_at <= datetime(\"now\")');\n        return stmt.run();\n    }\n    cleanupOldLogs(daysToKeep = 90) {\n        const stmt = this.db.prepare(`\n      DELETE FROM activity_logs \n      WHERE created_at <= datetime('now', '-${daysToKeep} days')\n    `);\n        return stmt.run();\n    }\n    // Encrypted credentials methods\n    saveEncryptedCredentials(userId, loginMethod, school, email, password) {\n        const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n        // Check for required environment variable\n        const secret = process.env.CREDENTIAL_ENCRYPTION_SECRET;\n        if (!secret) {\n            throw new Error(\"CREDENTIAL_ENCRYPTION_SECRET environment variable is required\");\n        }\n        // Generate a unique login key\n        const loginKey = \"SLK-\" + crypto.randomBytes(8).toString(\"hex\").toUpperCase();\n        // Create encryption IV\n        const iv = crypto.randomBytes(16);\n        // Derive key using stable secret + userId as salt for user isolation\n        const key = crypto.scryptSync(secret, String(userId), 32);\n        // Encrypt school, email and password\n        const cipher1 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedSchool = cipher1.update(school, \"utf8\", \"hex\") + cipher1.final(\"hex\");\n        const cipher2 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedEmail = cipher2.update(email, \"utf8\", \"hex\") + cipher2.final(\"hex\");\n        const cipher3 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedPassword = cipher3.update(password, \"utf8\", \"hex\") + cipher3.final(\"hex\");\n        const stmt = this.db.prepare(`\n      INSERT INTO encrypted_credentials (user_id, login_key, login_method, encrypted_school, encrypted_email, encrypted_password, encryption_iv)\n      VALUES (?, ?, ?, ?, ?, ?, ?)\n    `);\n        stmt.run(userId, loginKey, loginMethod, encryptedSchool, encryptedEmail, encryptedPassword, iv.toString(\"hex\"));\n        return loginKey;\n    }\n    getEncryptedCredentials(loginKey) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM encrypted_credentials \n      WHERE login_key = ? AND is_active = 1\n    `);\n        const result = stmt.get(loginKey);\n        if (!result) return null;\n        const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n        try {\n            // Check for required environment variable\n            const secret = process.env.CREDENTIAL_ENCRYPTION_SECRET;\n            if (!secret) {\n                throw new Error(\"CREDENTIAL_ENCRYPTION_SECRET environment variable is required\");\n            }\n            // Derive key using stable secret + userId as salt (same as during encryption)\n            const key = crypto.scryptSync(secret, String(result.user_id), 32);\n            const iv = Buffer.from(result.encryption_iv, \"hex\");\n            // Decrypt school, email and password\n            const decipher1 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const school = result.encrypted_school ? decipher1.update(result.encrypted_school, \"hex\", \"utf8\") + decipher1.final(\"utf8\") : null;\n            const decipher2 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const email = result.encrypted_email ? decipher2.update(result.encrypted_email, \"hex\", \"utf8\") + decipher2.final(\"utf8\") : null;\n            const decipher3 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const password = result.encrypted_password ? decipher3.update(result.encrypted_password, \"hex\", \"utf8\") + decipher3.final(\"utf8\") : null;\n            return {\n                loginMethod: result.login_method,\n                school,\n                email,\n                password,\n                userId: result.user_id\n            };\n        } catch (error) {\n            console.error(\"Failed to decrypt credentials:\", error);\n            return null;\n        }\n    }\n    getUserCredentials(userId) {\n        const stmt = this.db.prepare(`\n      SELECT login_key, login_method, created_at FROM encrypted_credentials \n      WHERE user_id = ? AND is_active = 1\n      ORDER BY created_at DESC\n    `);\n        return stmt.all(userId);\n    }\n    deactivateCredentials(loginKey) {\n        const stmt = this.db.prepare(\"UPDATE encrypted_credentials SET is_active = 0 WHERE login_key = ?\");\n        return stmt.run(loginKey);\n    }\n    // License Feature Settings Methods\n    setLicenseFeatures(licenseKeyId, features) {\n        const stmt = this.db.prepare(`\n      INSERT OR REPLACE INTO license_feature_settings\n      (license_key_id, max_accounts_per_batch, priority_level, scheduling_access, max_batches_per_day, updated_at)\n      VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)\n    `);\n        return stmt.run(licenseKeyId, features.max_accounts_per_batch || 0, features.priority_level || 0, features.scheduling_access ? 1 : 0, features.max_batches_per_day || 1);\n    }\n    getLicenseFeatures(licenseKeyId) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM license_feature_settings WHERE license_key_id = ?\n    `);\n        const result = stmt.get(licenseKeyId);\n        if (!result) {\n            // Return default features if none set\n            return {\n                max_accounts_per_batch: 0,\n                priority_level: 0,\n                scheduling_access: false,\n                max_batches_per_day: 1\n            };\n        }\n        return {\n            max_accounts_per_batch: result.max_accounts_per_batch,\n            priority_level: result.priority_level,\n            scheduling_access: Boolean(result.scheduling_access),\n            max_batches_per_day: result.max_batches_per_day || 1\n        };\n    }\n    getUserLicenseFeatures(userId) {\n        const stmt = this.db.prepare(`\n      SELECT lfs.* FROM license_feature_settings lfs\n      JOIN users u ON u.license_key_id = lfs.license_key_id\n      WHERE u.id = ?\n    `);\n        const result = stmt.get(userId);\n        if (!result) {\n            return {\n                max_accounts_per_batch: 0,\n                priority_level: 0,\n                scheduling_access: false,\n                max_batches_per_day: 1\n            };\n        }\n        return {\n            max_accounts_per_batch: result.max_accounts_per_batch,\n            priority_level: result.priority_level,\n            scheduling_access: Boolean(result.scheduling_access),\n            max_batches_per_day: result.max_batches_per_day || 1\n        };\n    }\n    // Daily batch count check\n    getUserDailyBatchCount(userId, date = null) {\n        const targetDate = date || new Date().toISOString().split(\"T\")[0]; // YYYY-MM-DD format\n        const stmt = this.db.prepare(`\n      SELECT COUNT(*) as count\n      FROM queue_batches\n      WHERE user_id = ?\n      AND DATE(created_at) = ?\n    `);\n        const result = stmt.get(userId, targetDate);\n        return result.count;\n    }\n    // Weekly schedule count check\n    getUserWeeklyScheduleCount(userId) {\n        // Get the start of the current week (Monday)\n        const now = new Date();\n        const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.\n        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // If Sunday, go back 6 days to Monday\n        const startOfWeek = new Date(now);\n        startOfWeek.setDate(now.getDate() - daysToMonday);\n        startOfWeek.setHours(0, 0, 0, 0);\n        const stmt = this.db.prepare(`\n      SELECT COUNT(*) as count\n      FROM queue_schedules\n      WHERE user_id = ?\n      AND created_at >= ?\n      AND status != 'cancelled'\n    `);\n        const result = stmt.get(userId, startOfWeek.toISOString());\n        return result.count;\n    }\n    // Queue Batch Methods\n    createQueueBatch(userId, batchName, accounts, scheduledTime = null, loginType = \"normal\", srpTarget = 100) {\n        const transaction = this.db.transaction(()=>{\n            // Get user's license features\n            const features = this.getUserLicenseFeatures(userId);\n            // Check daily batch limit\n            const dailyBatchCount = this.getUserDailyBatchCount(userId);\n            if (dailyBatchCount >= features.max_batches_per_day) {\n                throw new Error(`Daily batch limit reached (${features.max_batches_per_day} batches per day). Please try again tomorrow.`);\n            }\n            // Validate batch size against license limits\n            if (features.max_accounts_per_batch > 0 && accounts.length > features.max_accounts_per_batch) {\n                throw new Error(`Batch size (${accounts.length}) exceeds license limit (${features.max_accounts_per_batch})`);\n            }\n            // Validate scheduling access\n            if (scheduledTime && !features.scheduling_access) {\n                throw new Error(\"Scheduling access not available for this license\");\n            }\n            // Validate login type\n            if (![\n                \"normal\",\n                \"google\",\n                \"microsoft\"\n            ].includes(loginType)) {\n                throw new Error(\"Invalid login type specified\");\n            }\n            // Validate SRP target\n            if (srpTarget < 1 || srpTarget > 400) {\n                throw new Error(\"SRP target must be between 1 and 400\");\n            }\n            // Create batch\n            const batchStmt = this.db.prepare(`\n        INSERT INTO queue_batches (user_id, batch_name, login_type, total_accounts, priority_level, srp_target, scheduled_time)\n        VALUES (?, ?, ?, ?, ?, ?, ?)\n      `);\n            const batchResult = batchStmt.run(userId, batchName, loginType, accounts.length, features.priority_level, srpTarget, scheduledTime);\n            const batchId = batchResult.lastInsertRowid;\n            // Create individual jobs for each account\n            const jobStmt = this.db.prepare(`\n        INSERT INTO queue_jobs (batch_id, user_id, job_data, priority_level, effective_priority, srp_target, scheduled_time)\n        VALUES (?, ?, ?, ?, ?, ?, ?)\n      `);\n            accounts.forEach((account)=>{\n                const effectivePriority = this.calculateEffectivePriority(features.priority_level, scheduledTime);\n                jobStmt.run(batchId, userId, JSON.stringify(account), features.priority_level, effectivePriority, srpTarget, scheduledTime);\n            });\n            // Create schedule entry if scheduled\n            if (scheduledTime) {\n                this.createScheduleEntry(userId, scheduledTime, null, batchId, 30, srpTarget);\n            }\n            // Log activity\n            this.logActivity(userId, \"BATCH_CREATED\", `Created batch: ${batchName} with ${accounts.length} accounts`);\n            return batchId;\n        });\n        return transaction();\n    }\n    calculateEffectivePriority(basePriority, scheduledTime) {\n        let effectivePriority = basePriority;\n        // Boost priority for scheduled jobs approaching their time\n        if (scheduledTime) {\n            const now = new Date();\n            const scheduled = new Date(scheduledTime);\n            const timeDiff = scheduled.getTime() - now.getTime();\n            const hoursUntil = timeDiff / (1000 * 60 * 60);\n            if (hoursUntil <= 1) {\n                effectivePriority += 5; // High boost for jobs due within an hour\n            } else if (hoursUntil <= 6) {\n                effectivePriority += 2; // Medium boost for jobs due within 6 hours\n            }\n        }\n        // Apply starvation prevention (boost priority for old jobs)\n        // This would be implemented in a background process\n        return Math.min(effectivePriority, 10); // Cap at maximum priority\n    }\n    getQueueBatches(userId = null, status = null, limit = 50, offset = 0) {\n        let query = `\n      SELECT qb.*, u.username\n      FROM queue_batches qb\n      JOIN users u ON qb.user_id = u.id\n      WHERE 1=1\n    `;\n        const params = [];\n        if (userId) {\n            query += \" AND qb.user_id = ?\";\n            params.push(userId);\n        }\n        if (status) {\n            query += \" AND qb.status = ?\";\n            params.push(status);\n        }\n        query += \" ORDER BY qb.created_at DESC LIMIT ? OFFSET ?\";\n        params.push(limit, offset);\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    getBatchJobs(batchId) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM queue_jobs \n      WHERE batch_id = ? \n      ORDER BY effective_priority DESC, created_at ASC\n    `);\n        return stmt.all(batchId);\n    }\n    updateBatchStatus(batchId, status, completedAt = null) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_batches\n      SET status = ?, completed_at = ?\n      WHERE id = ?\n    `);\n        return stmt.run(status, completedAt, batchId);\n    }\n    // Queue Job Methods\n    createQueueJob({ username, job_type = \"sparx_reader\", job_data, srp_target = 100, priority = 0, status = \"queued\" }) {\n        // Get user ID\n        const userStmt = this.db.prepare(\"SELECT id FROM users WHERE username = ?\");\n        const user = userStmt.get(username);\n        if (!user) {\n            throw new Error(\"User not found\");\n        }\n        // Create the job\n        const stmt = this.db.prepare(`\n      INSERT INTO queue_jobs (user_id, job_type, job_data, priority_level, effective_priority, srp_target, status)\n      VALUES (?, ?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(user.id, job_type, job_data, priority, priority, srp_target, status);\n        return result.lastInsertRowid;\n    }\n    getQueueJobs(userId = null) {\n        let query = `\n      SELECT qj.*, qb.batch_name, u.username\n      FROM queue_jobs qj\n      LEFT JOIN queue_batches qb ON qj.batch_id = qb.id\n      JOIN users u ON qj.user_id = u.id\n    `;\n        const params = [];\n        if (userId) {\n            query += \" WHERE qj.user_id = ?\";\n            params.push(userId);\n        }\n        query += \" ORDER BY qj.created_at DESC\";\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    getNextQueueJob() {\n        const stmt = this.db.prepare(`\n      SELECT qj.*, qb.batch_name, u.username\n      FROM queue_jobs qj\n      LEFT JOIN queue_batches qb ON qj.batch_id = qb.id\n      JOIN users u ON qj.user_id = u.id\n      WHERE qj.status = 'queued'\n      AND (qj.scheduled_time IS NULL OR qj.scheduled_time <= datetime('now'))\n      ORDER BY qj.effective_priority DESC, qj.created_at ASC\n      LIMIT 1\n    `);\n        return stmt.get();\n    }\n    updateJobStatus(jobId, status, errorMessage = null, startedAt = null, completedAt = null) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET status = ?, error_message = ?, started_at = ?, completed_at = ?\n      WHERE id = ?\n    `);\n        return stmt.run(status, errorMessage, startedAt, completedAt, jobId);\n    }\n    incrementJobRetry(jobId) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET retry_count = retry_count + 1, status = 'queued'\n      WHERE id = ? AND retry_count < max_retries\n    `);\n        return stmt.run(jobId);\n    }\n    // Scheduling Methods\n    createScheduleEntry(userId, scheduledTime, jobId = null, batchId = null, durationMinutes = 30, srpTarget = 100) {\n        // Check for conflicts\n        const conflicts = this.checkScheduleConflicts(userId, scheduledTime, durationMinutes);\n        if (conflicts.length > 0) {\n            throw new Error(`Schedule conflict detected at ${scheduledTime}`);\n        }\n        const stmt = this.db.prepare(`\n      INSERT INTO queue_schedules (user_id, scheduled_time, duration_minutes, srp_target, job_id, batch_id)\n      VALUES (?, ?, ?, ?, ?, ?)\n    `);\n        return stmt.run(userId, scheduledTime, durationMinutes, srpTarget, jobId, batchId);\n    }\n    checkScheduleConflicts(userId, scheduledTime, durationMinutes) {\n        const startTime = new Date(scheduledTime);\n        const endTime = new Date(startTime.getTime() + durationMinutes * 60 * 1000);\n        const stmt = this.db.prepare(`\n      SELECT * FROM queue_schedules\n      WHERE user_id = ? \n      AND status IN ('scheduled', 'active')\n      AND (\n        (scheduled_time <= ? AND datetime(scheduled_time, '+' || duration_minutes || ' minutes') > ?) OR\n        (scheduled_time < ? AND datetime(scheduled_time, '+' || duration_minutes || ' minutes') >= ?)\n      )\n    `);\n        return stmt.all(userId, startTime.toISOString(), startTime.toISOString(), endTime.toISOString(), endTime.toISOString());\n    }\n    getUserSchedules(userId, startDate = null, endDate = null) {\n        let query = `\n      SELECT qs.*, qj.job_type, qb.batch_name\n      FROM queue_schedules qs\n      LEFT JOIN queue_jobs qj ON qs.job_id = qj.id\n      LEFT JOIN queue_batches qb ON qs.batch_id = qb.id\n      WHERE qs.user_id = ?\n    `;\n        const params = [\n            userId\n        ];\n        if (startDate) {\n            query += \" AND qs.scheduled_time >= ?\";\n            params.push(startDate);\n        }\n        if (endDate) {\n            query += \" AND qs.scheduled_time <= ?\";\n            params.push(endDate);\n        }\n        query += \" ORDER BY qs.scheduled_time ASC\";\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    // Priority Management Methods\n    updateJobPriority(jobId, newPriority, adminOverride = false) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET effective_priority = ?, priority_level = ?\n      WHERE id = ?\n    `);\n        const result = stmt.run(newPriority, adminOverride ? newPriority : null, jobId);\n        if (adminOverride) {\n            this.logActivity(null, \"ADMIN_PRIORITY_OVERRIDE\", `Job ${jobId} priority set to ${newPriority}`);\n        }\n        return result;\n    }\n    applyStarvationPrevention() {\n        // Boost priority for jobs that have been waiting too long\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET effective_priority = CASE \n        WHEN datetime('now') > datetime(created_at, '+2 hours') THEN MIN(effective_priority + 2, 10)\n        WHEN datetime('now') > datetime(created_at, '+1 hour') THEN MIN(effective_priority + 1, 10)\n        ELSE effective_priority\n      END\n      WHERE status = 'queued'\n    `);\n        return stmt.run();\n    }\n    getQueueStats() {\n        const stats = {};\n        // Total jobs by status\n        const statusStmt = this.db.prepare(`\n      SELECT status, COUNT(*) as count \n      FROM queue_jobs \n      GROUP BY status\n    `);\n        stats.jobsByStatus = statusStmt.all();\n        // Jobs by priority level\n        const priorityStmt = this.db.prepare(`\n      SELECT effective_priority, COUNT(*) as count \n      FROM queue_jobs \n      WHERE status = 'queued'\n      GROUP BY effective_priority\n      ORDER BY effective_priority DESC\n    `);\n        stats.jobsByPriority = priorityStmt.all();\n        // Average wait time\n        const waitTimeStmt = this.db.prepare(`\n      SELECT AVG(julianday(started_at) - julianday(created_at)) * 24 * 60 as avg_wait_minutes\n      FROM queue_jobs \n      WHERE started_at IS NOT NULL\n    `);\n        stats.averageWaitTime = waitTimeStmt.get()?.avg_wait_minutes || 0;\n        return stats;\n    }\n    close() {\n        this.db.close();\n    }\n    // System Configuration Methods\n    getSystemConfig(key) {\n        const stmt = this.db.prepare(\"SELECT * FROM system_config WHERE config_key = ?\");\n        const config = stmt.get(key);\n        if (!config) return null;\n        // Parse value based on type\n        switch(config.config_type){\n            case \"number\":\n                return {\n                    ...config,\n                    config_value: parseInt(config.config_value)\n                };\n            case \"boolean\":\n                return {\n                    ...config,\n                    config_value: config.config_value === \"true\"\n                };\n            case \"json\":\n                return {\n                    ...config,\n                    config_value: JSON.parse(config.config_value)\n                };\n            default:\n                return config;\n        }\n    }\n    setSystemConfig(key, value, type = \"string\", description = null) {\n        const stringValue = type === \"json\" ? JSON.stringify(value) : value.toString();\n        const stmt = this.db.prepare(`\n      INSERT OR REPLACE INTO system_config (config_key, config_value, config_type, description, updated_at)\n      VALUES (?, ?, ?, COALESCE(?, (SELECT description FROM system_config WHERE config_key = ?)), CURRENT_TIMESTAMP)\n    `);\n        return stmt.run(key, stringValue, type, description, key);\n    }\n    getAllSystemConfig() {\n        const stmt = this.db.prepare(\"SELECT * FROM system_config ORDER BY config_key\");\n        const configs = stmt.all();\n        return configs.map((config)=>{\n            switch(config.config_type){\n                case \"number\":\n                    return {\n                        ...config,\n                        config_value: parseInt(config.config_value)\n                    };\n                case \"boolean\":\n                    return {\n                        ...config,\n                        config_value: config.config_value === \"true\"\n                    };\n                case \"json\":\n                    return {\n                        ...config,\n                        config_value: JSON.parse(config.config_value)\n                    };\n                default:\n                    return config;\n            }\n        });\n    }\n    // Dead Letter Queue Methods\n    addToDeadLetterQueue(originalJobId, jobType, jobData, userId, failureReason) {\n        const stmt = this.db.prepare(`\n      INSERT INTO dead_letter_queue (original_job_id, job_type, job_data, user_id, failure_reason)\n      VALUES (?, ?, ?, ?, ?)\n    `);\n        return stmt.run(originalJobId, jobType, JSON.stringify(jobData), userId, failureReason);\n    }\n    getDeadLetterQueueJobs(status = null, limit = 50, offset = 0) {\n        let query = `\n      SELECT dlq.*, u.username\n      FROM dead_letter_queue dlq\n      JOIN users u ON dlq.user_id = u.id\n    `;\n        const params = [];\n        if (status) {\n            query += \" WHERE dlq.status = ?\";\n            params.push(status);\n        }\n        query += \" ORDER BY dlq.created_at DESC LIMIT ? OFFSET ?\";\n        params.push(limit, offset);\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    updateDeadLetterQueueStatus(id, status) {\n        const stmt = this.db.prepare(`\n      UPDATE dead_letter_queue\n      SET status = ?, updated_at = CURRENT_TIMESTAMP\n      WHERE id = ?\n    `);\n        return stmt.run(status, id);\n    }\n    retryDeadLetterQueueJob(id) {\n        const getStmt = this.db.prepare(\"SELECT * FROM dead_letter_queue WHERE id = ?\");\n        const job = getStmt.get(id);\n        if (!job) {\n            throw new Error(\"Dead letter queue job not found\");\n        }\n        // Create new job in queue_jobs using the original job data\n        const insertStmt = this.db.prepare(`\n      INSERT INTO queue_jobs (user_id, job_type, job_data, priority, effective_priority, srp_target, status)\n      VALUES (?, ?, ?, 0, 0, 100, 'queued')\n    `);\n        const result = insertStmt.run(job.user_id, job.job_type, job.job_data, 0, 0, 100);\n        // Update dead letter queue status\n        this.updateDeadLetterQueueStatus(id, \"resolved\");\n        return result.lastInsertRowid;\n    }\n}\n// Create data directory if it doesn't exist\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst dataDir = path.join(process.cwd(), \"data\");\nif (!fs.existsSync(dataDir)) {\n    fs.mkdirSync(dataDir, {\n        recursive: true\n    });\n}\n// Export singleton instance\nlet dbInstance = null;\nfunction getDatabase() {\n    if (!dbInstance) {\n        dbInstance = new DatabaseManager();\n    }\n    return dbInstance;\n}\nmodule.exports = {\n    getDatabase,\n    DatabaseManager\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvZGF0YWJhc2UuanMiLCJtYXBwaW5ncyI6IjtBQUFBLE1BQU1BLFdBQVdDLG1CQUFPQSxDQUFDO0FBQ3pCLE1BQU1DLFNBQVNELG1CQUFPQSxDQUFDO0FBQ3ZCLE1BQU0sRUFBRUUsSUFBSUMsTUFBTSxFQUFFLEdBQUdILG1CQUFPQSxDQUFDO0FBQy9CLE1BQU1JLE9BQU9KLG1CQUFPQSxDQUFDO0FBRXJCLE1BQU1LO0lBQ0pDLGFBQWM7UUFDWixNQUFNQyxTQUFTSCxLQUFLSSxJQUFJLENBQUNDLFFBQVFDLEdBQUcsSUFBSSxRQUFRO1FBQ2hELElBQUksQ0FBQ0MsRUFBRSxHQUFHLElBQUlaLFNBQVNRO1FBQ3ZCLElBQUksQ0FBQ0ssZ0JBQWdCO1FBQ3JCLElBQUksQ0FBQ0Msa0JBQWtCO0lBQ3pCO0lBRUFELG1CQUFtQjtRQUNqQixjQUFjO1FBQ2QsSUFBSSxDQUFDRCxFQUFFLENBQUNHLElBQUksQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7O0lBYWQsQ0FBQztRQUVELHFCQUFxQjtRQUNyQixJQUFJLENBQUNILEVBQUUsQ0FBQ0csSUFBSSxDQUFDLENBQUM7Ozs7Ozs7Ozs7Ozs7O0lBY2QsQ0FBQztRQUVELHNCQUFzQjtRQUN0QixJQUFJLENBQUNILEVBQUUsQ0FBQ0csSUFBSSxDQUFDLENBQUM7Ozs7Ozs7Ozs7OztJQVlkLENBQUM7UUFFRCxzQkFBc0I7UUFDdEIsSUFBSSxDQUFDSCxFQUFFLENBQUNHLElBQUksQ0FBQyxDQUFDOzs7Ozs7Ozs7O0lBVWQsQ0FBQztRQUVELHdCQUF3QjtRQUN4QixJQUFJLENBQUNILEVBQUUsQ0FBQ0csSUFBSSxDQUFDLENBQUM7Ozs7Ozs7OztJQVNkLENBQUM7UUFFRCxvQ0FBb0M7UUFDcEMsSUFBSSxDQUFDSCxFQUFFLENBQUNHLElBQUksQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7SUFlZCxDQUFDO1FBRUQsaUNBQWlDO1FBQ2pDLElBQUksQ0FBQ0gsRUFBRSxDQUFDRyxJQUFJLENBQUMsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7OztJQWdCZCxDQUFDO1FBRUQsZ0RBQWdEO1FBQ2hELElBQUksQ0FBQ0gsRUFBRSxDQUFDRyxJQUFJLENBQUMsQ0FBQzs7Ozs7Ozs7OztJQVVkLENBQUM7UUFFRCwwQkFBMEI7UUFDMUIsSUFBSSxDQUFDSCxFQUFFLENBQUNHLElBQUksQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7SUFlZCxDQUFDO1FBRUQsc0JBQXNCO1FBQ3RCLElBQUksQ0FBQ0gsRUFBRSxDQUFDRyxJQUFJLENBQUMsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBa0JkLENBQUM7UUFFRCw2REFBNkQ7UUFDN0QsSUFBSTtZQUNGLElBQUksQ0FBQ0gsRUFBRSxDQUFDRyxJQUFJLENBQUMsQ0FBQyxtRUFBbUUsQ0FBQztRQUNwRixFQUFFLE9BQU9DLE9BQU87UUFDZCxzQ0FBc0M7UUFDeEM7UUFFQSxtQkFBbUI7UUFDbkIsSUFBSSxDQUFDSixFQUFFLENBQUNHLElBQUksQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFxQmQsQ0FBQztRQUVELDBEQUEwRDtRQUMxRCxJQUFJO1lBQ0YsSUFBSSxDQUFDSCxFQUFFLENBQUNHLElBQUksQ0FBQyxDQUFDLGdFQUFnRSxDQUFDO1FBQ2pGLEVBQUUsT0FBT0MsT0FBTztRQUNkLHNDQUFzQztRQUN4QztRQUVBLCtDQUErQztRQUMvQyxJQUFJLENBQUNKLEVBQUUsQ0FBQ0csSUFBSSxDQUFDLENBQUM7Ozs7Ozs7Ozs7Ozs7OztJQWVkLENBQUM7UUFFRCxxRUFBcUU7UUFDckUsSUFBSTtZQUNGLElBQUksQ0FBQ0gsRUFBRSxDQUFDRyxJQUFJLENBQUMsQ0FBQyxxRUFBcUUsQ0FBQztRQUN0RixFQUFFLE9BQU9DLE9BQU87UUFDZCxzQ0FBc0M7UUFDeEM7UUFFQSx3Q0FBd0M7UUFDeEMsSUFBSSxDQUFDSixFQUFFLENBQUNHLElBQUksQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBc0JkLENBQUM7UUFFRCxnRUFBZ0U7UUFDaEUsSUFBSTtZQUNGLE1BQU1FLFVBQVUsSUFBSSxDQUFDTCxFQUFFLENBQUNNLE9BQU8sQ0FBQywrQ0FBK0NDLEdBQUc7WUFDbEYsTUFBTUMsc0JBQXNCSCxRQUFRSSxJQUFJLENBQUNDLENBQUFBLE1BQU9BLElBQUlDLElBQUksS0FBSztZQUU3RCxJQUFJLENBQUNILHFCQUFxQjtnQkFDeEJJLFFBQVFDLEdBQUcsQ0FBQztnQkFDWixJQUFJLENBQUNiLEVBQUUsQ0FBQ0csSUFBSSxDQUFDLENBQUMscUZBQXFGLENBQUM7WUFDdEc7UUFDRixFQUFFLE9BQU9DLE9BQU87WUFDZFEsUUFBUVIsS0FBSyxDQUFDLDRDQUE0Q0E7UUFDNUQ7UUFFQSx3RUFBd0U7UUFDeEUsSUFBSTtZQUNGLE1BQU1VLGVBQWUsSUFBSSxDQUFDZCxFQUFFLENBQUNNLE9BQU8sQ0FBQyxvQ0FBb0NDLEdBQUc7WUFDNUUsTUFBTVEsZUFBZUQsYUFBYUwsSUFBSSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJQyxJQUFJLEtBQUs7WUFFM0QsSUFBSSxDQUFDSSxjQUFjO2dCQUNqQkgsUUFBUUMsR0FBRyxDQUFDO2dCQUNaLElBQUksQ0FBQ2IsRUFBRSxDQUFDRyxJQUFJLENBQUMsQ0FBQyw0SEFBNEgsQ0FBQztZQUM3STtRQUNGLEVBQUUsT0FBT0MsT0FBTztZQUNkUSxRQUFRUixLQUFLLENBQUMsbUNBQW1DQTtRQUNuRDtJQUNGO0lBRUFGLHFCQUFxQjtRQUNuQixNQUFNYyxjQUFjLElBQUksQ0FBQ2hCLEVBQUUsQ0FBQ00sT0FBTyxDQUFDLCtDQUErQ1csR0FBRyxDQUFDO1FBRXZGLElBQUksQ0FBQ0QsYUFBYTtZQUNoQixNQUFNRSxpQkFBaUI1QixPQUFPNkIsUUFBUSxDQUFDckIsUUFBUXNCLEdBQUcsQ0FBQ0Msc0JBQXNCLEVBQUU7WUFDM0UsTUFBTUMsT0FBTyxJQUFJLENBQUN0QixFQUFFLENBQUNNLE9BQU8sQ0FBQyxDQUFDOzs7TUFHOUIsQ0FBQztZQUVEZ0IsS0FBS0MsR0FBRyxDQUFDekIsUUFBUXNCLEdBQUcsQ0FBQ0ksc0JBQXNCLEVBQUVOLGdCQUFnQixTQUFTO1lBQ3RFTixRQUFRQyxHQUFHLENBQUMsQ0FBQyw0QkFBNEIsRUFBRWYsUUFBUXNCLEdBQUcsQ0FBQ0ksc0JBQXNCLENBQUMsU0FBUyxDQUFDO1FBQzFGO1FBRUEsMENBQTBDO1FBQzFDLElBQUksQ0FBQ0MsdUJBQXVCO0lBQzlCO0lBRUFBLDBCQUEwQjtRQUN4QixNQUFNQyxpQkFBaUI7WUFDckI7Z0JBQ0VDLEtBQUs7Z0JBQ0xDLE9BQVE5QixRQUFRc0IsR0FBRyxDQUFDUyw2QkFBNkIsSUFBSTtnQkFDckRDLE1BQU07Z0JBQ05DLGFBQWE7WUFDZjtZQUNBO2dCQUNFSixLQUFLO2dCQUNMQyxPQUFROUIsUUFBUXNCLEdBQUcsQ0FBQ1ksMkJBQTJCLElBQUk7Z0JBQ25ERixNQUFNO2dCQUNOQyxhQUFhO1lBQ2Y7WUFDQTtnQkFDRUosS0FBSztnQkFDTEMsT0FBUTlCLFFBQVFzQixHQUFHLENBQUNhLDhCQUE4QixJQUFJO2dCQUN0REgsTUFBTTtnQkFDTkMsYUFBYTtZQUNmO1lBQ0E7Z0JBQUVKLEtBQUs7Z0JBQTZCQyxPQUFPO2dCQUFRRSxNQUFNO2dCQUFXQyxhQUFhO1lBQTJDO1lBQzVIO2dCQUFFSixLQUFLO2dCQUFzQkMsT0FBTztnQkFBS0UsTUFBTTtnQkFBVUMsYUFBYTtZQUE0RDtZQUNsSTtnQkFBRUosS0FBSztnQkFBMkJDLE9BQU87Z0JBQVFFLE1BQU07Z0JBQVdDLGFBQWE7WUFBZ0Q7U0FDaEk7UUFFRCxLQUFLLE1BQU1HLFVBQVVSLGVBQWdCO1lBQ25DLE1BQU1TLGVBQWUsSUFBSSxDQUFDbkMsRUFBRSxDQUFDTSxPQUFPLENBQUM7WUFDckMsTUFBTThCLFdBQVdELGFBQWFsQixHQUFHLENBQUNpQixPQUFPUCxHQUFHO1lBRTVDLElBQUksQ0FBQ1MsVUFBVTtnQkFDYixNQUFNQyxhQUFhLElBQUksQ0FBQ3JDLEVBQUUsQ0FBQ00sT0FBTyxDQUFDLENBQUM7OztRQUdwQyxDQUFDO2dCQUNEK0IsV0FBV2QsR0FBRyxDQUFDVyxPQUFPUCxHQUFHLEVBQUVPLE9BQU9OLEtBQUssRUFBRU0sT0FBT0osSUFBSSxFQUFFSSxPQUFPSCxXQUFXO1lBQzFFO1FBQ0Y7SUFDRjtJQUVBLDBCQUEwQjtJQUMxQk8sV0FBV0MsUUFBUSxFQUFFQyxRQUFRLEVBQUVDLFVBQVUsRUFBRTtRQUN6QyxNQUFNQyxjQUFjLElBQUksQ0FBQzFDLEVBQUUsQ0FBQzBDLFdBQVcsQ0FBQztZQUN0Qyx1QkFBdUI7WUFDdkIsTUFBTUMsY0FBYyxJQUFJLENBQUMzQyxFQUFFLENBQUNNLE9BQU8sQ0FBQyxDQUFDOzs7O01BSXJDLENBQUM7WUFDRCxNQUFNc0MsVUFBVUQsWUFBWTFCLEdBQUcsQ0FBQ3dCO1lBRWhDLElBQUksQ0FBQ0csU0FBUztnQkFDWixNQUFNLElBQUlDLE1BQU07WUFDbEI7WUFFQSxJQUFJLElBQUlDLEtBQUtGLFFBQVFHLFVBQVUsSUFBSSxJQUFJRCxRQUFRO2dCQUM3QyxNQUFNLElBQUlELE1BQU07WUFDbEI7WUFFQSxJQUFJRCxRQUFRSSxZQUFZLElBQUlKLFFBQVFLLFFBQVEsRUFBRTtnQkFDNUMsTUFBTSxJQUFJSixNQUFNO1lBQ2xCO1lBRUEsbUNBQW1DO1lBQ25DLE1BQU1LLGFBQWEsSUFBSSxDQUFDbEQsRUFBRSxDQUFDTSxPQUFPLENBQUMsMkNBQTJDVyxHQUFHLENBQUNzQjtZQUNsRixJQUFJVyxZQUFZO2dCQUNkLE1BQU0sSUFBSUwsTUFBTTtZQUNsQjtZQUVBLGNBQWM7WUFDZCxNQUFNM0IsaUJBQWlCNUIsT0FBTzZCLFFBQVEsQ0FBQ3FCLFVBQVU7WUFDakQsTUFBTVcsV0FBVyxJQUFJLENBQUNuRCxFQUFFLENBQUNNLE9BQU8sQ0FBQyxDQUFDOzs7TUFHbEMsQ0FBQztZQUVELE1BQU04QyxTQUFTRCxTQUFTNUIsR0FBRyxDQUFDZ0IsVUFBVXJCLGdCQUFnQjBCLFFBQVFTLEVBQUUsRUFBRTtZQUVsRSwyQkFBMkI7WUFDM0IsTUFBTUMsb0JBQW9CLElBQUksQ0FBQ3RELEVBQUUsQ0FBQ00sT0FBTyxDQUFDLENBQUM7Ozs7TUFJM0MsQ0FBQztZQUNEZ0Qsa0JBQWtCL0IsR0FBRyxDQUFDcUIsUUFBUVMsRUFBRTtZQUVoQyxPQUFPRCxPQUFPRyxlQUFlO1FBQy9CO1FBRUEsT0FBT2I7SUFDVDtJQUVBYyxpQkFBaUJqQixRQUFRLEVBQUVDLFFBQVEsRUFBRTtRQUNuQyxNQUFNbEIsT0FBTyxJQUFJLENBQUN0QixFQUFFLENBQUNNLE9BQU8sQ0FBQyxDQUFDOzs7OztJQUs5QixDQUFDO1FBRUQsTUFBTW1ELE9BQU9uQyxLQUFLTCxHQUFHLENBQUNzQjtRQUV0QixJQUFJLENBQUNrQixNQUFNO1lBQ1QsTUFBTSxJQUFJWixNQUFNO1FBQ2xCO1FBRUEsTUFBTWEsa0JBQWtCcEUsT0FBT3FFLFdBQVcsQ0FBQ25CLFVBQVVpQixLQUFLRyxhQUFhO1FBQ3ZFLElBQUksQ0FBQ0YsaUJBQWlCO1lBQ3BCLE1BQU0sSUFBSWIsTUFBTTtRQUNsQjtRQUVBLDZDQUE2QztRQUM3QyxJQUFJWSxLQUFLSSxJQUFJLEtBQUssU0FBUztZQUN6QixJQUFJLENBQUNKLEtBQUtLLGNBQWMsSUFBSSxJQUFJaEIsS0FBS1csS0FBS00sZUFBZSxJQUFJLElBQUlqQixRQUFRO2dCQUN2RSxNQUFNLElBQUlELE1BQU07WUFDbEI7UUFDRjtRQUVBLG9CQUFvQjtRQUNwQixNQUFNbUIsYUFBYSxJQUFJLENBQUNoRSxFQUFFLENBQUNNLE9BQU8sQ0FBQztRQUNuQzBELFdBQVd6QyxHQUFHLENBQUNrQyxLQUFLSixFQUFFO1FBRXRCLHdCQUF3QjtRQUN4QixPQUFPSSxLQUFLRyxhQUFhO1FBQ3pCLE9BQU9IO0lBQ1Q7SUFFQSxpQ0FBaUM7SUFDakNRLGlCQUFpQkMsWUFBWSxFQUFFQyxVQUFVLENBQUMsRUFBRUMsV0FBVyxFQUFFLEVBQUVDLFlBQVksSUFBSSxFQUFFO1FBQzNFLE1BQU1DLFVBQVUsSUFBSSxDQUFDQyxrQkFBa0I7UUFDdkMsTUFBTUMsWUFBWSxJQUFJMUI7UUFDdEIwQixVQUFVQyxPQUFPLENBQUNELFVBQVVFLE9BQU8sS0FBS1I7UUFFeEMsTUFBTTVDLE9BQU8sSUFBSSxDQUFDdEIsRUFBRSxDQUFDTSxPQUFPLENBQUMsQ0FBQzs7O0lBRzlCLENBQUM7UUFFRCxNQUFNOEMsU0FBUzlCLEtBQUtDLEdBQUcsQ0FDckIrQyxTQUNBSixjQUNBQyxTQUNBSyxVQUFVRyxXQUFXLElBQ3JCTixXQUNBTyxLQUFLQyxTQUFTLENBQUNUO1FBR2pCLE9BQU87WUFDTGYsSUFBSUQsT0FBT0csZUFBZTtZQUMxQmU7WUFDQUo7WUFDQUM7WUFDQUssV0FBV0EsVUFBVUcsV0FBVztZQUNoQ1A7UUFDRjtJQUNGO0lBRUFHLHFCQUFxQjtRQUNuQixNQUFNTyxXQUFXLEVBQUU7UUFDbkIsSUFBSyxJQUFJQyxJQUFJLEdBQUdBLElBQUksR0FBR0EsSUFBSztZQUMxQkQsU0FBU0UsSUFBSSxDQUFDeEYsU0FBU3lGLE9BQU8sQ0FBQyxNQUFNLElBQUlDLFNBQVMsQ0FBQyxHQUFHLEdBQUdDLFdBQVc7UUFDdEU7UUFDQSxPQUFPLENBQUMsSUFBSSxFQUFFTCxTQUFTakYsSUFBSSxDQUFDLEtBQUssQ0FBQztJQUNwQztJQUVBdUYsZUFBZUMsUUFBUSxFQUFFLEVBQUVDLFNBQVMsQ0FBQyxFQUFFO1FBQ3JDLE1BQU1oRSxPQUFPLElBQUksQ0FBQ3RCLEVBQUUsQ0FBQ00sT0FBTyxDQUFDLENBQUM7Ozs7Ozs7Ozs7O0lBVzlCLENBQUM7UUFFRCxPQUFPZ0IsS0FBS2YsR0FBRyxDQUFDOEUsT0FBT0M7SUFDekI7SUFFQUMscUJBQXFCQyxLQUFLLEVBQUU7UUFDMUIsTUFBTWxFLE9BQU8sSUFBSSxDQUFDdEIsRUFBRSxDQUFDTSxPQUFPLENBQUM7UUFDN0IsT0FBT2dCLEtBQUtDLEdBQUcsQ0FBQ2lFO0lBQ2xCO0lBRUEsOENBQThDO0lBQzlDQyxxQkFBcUJDLE1BQU0sRUFBRTtRQUMzQixNQUFNcEUsT0FBTyxJQUFJLENBQUN0QixFQUFFLENBQUNNLE9BQU8sQ0FBQyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBbUI5QixDQUFDO1FBRUQsT0FBT2dCLEtBQUtMLEdBQUcsQ0FBQ3lFO0lBQ2xCO0lBRUEsa0ZBQWtGO0lBQ2xGQywwQkFBMEJsRCxVQUFVLEVBQUU7UUFDcEMsTUFBTW5CLE9BQU8sSUFBSSxDQUFDdEIsRUFBRSxDQUFDTSxPQUFPLENBQUMsQ0FBQzs7Ozs7Ozs7Ozs7Ozs7OztJQWdCOUIsQ0FBQztRQUVELE1BQU1zQyxVQUFVdEIsS0FBS0wsR0FBRyxDQUFDd0I7UUFFekIsSUFBSSxDQUFDRyxTQUFTO1lBQ1osT0FBTztnQkFBRWdELE9BQU87Z0JBQU94RixPQUFPO1lBQXdCO1FBQ3hEO1FBRUEsSUFBSXdDLFFBQVFpRCxNQUFNLEtBQUssU0FBUztZQUM5QixJQUFJQyxlQUFlO1lBQ25CLE9BQVFsRCxRQUFRaUQsTUFBTTtnQkFDcEIsS0FBSztvQkFDSEMsZUFBZTtvQkFDZjtnQkFDRixLQUFLO29CQUNIQSxlQUFlO29CQUNmO2dCQUNGLEtBQUs7b0JBQ0hBLGVBQWU7b0JBQ2Y7WUFDSjtZQUNBLE9BQU87Z0JBQUVGLE9BQU87Z0JBQU94RixPQUFPMEY7WUFBYTtRQUM3QztRQUVBLE9BQU87WUFBRUYsT0FBTztZQUFNaEQ7UUFBUTtJQUNoQztJQUVBLDhDQUE4QztJQUM5Q21ELGlCQUFpQkwsTUFBTSxFQUFFTSxhQUFhLEVBQUU7UUFDdEMsTUFBTXRELGNBQWMsSUFBSSxDQUFDMUMsRUFBRSxDQUFDMEMsV0FBVyxDQUFDO1lBQ3RDLHFDQUFxQztZQUNyQyxNQUFNdUQsYUFBYSxJQUFJLENBQUNOLHlCQUF5QixDQUFDSztZQUNsRCxJQUFJLENBQUNDLFdBQVdMLEtBQUssRUFBRTtnQkFDckIsTUFBTSxJQUFJL0MsTUFBTW9ELFdBQVc3RixLQUFLO1lBQ2xDO1lBRUEsTUFBTThGLGFBQWFELFdBQVdyRCxPQUFPO1lBRXJDLGtEQUFrRDtZQUNsRCxNQUFNdUQsaUJBQWlCLElBQUksQ0FBQ25HLEVBQUUsQ0FBQ00sT0FBTyxDQUFDLENBQUM7Ozs7TUFJeEMsQ0FBQztZQUNENkYsZUFBZTVFLEdBQUcsQ0FBQzJFLFdBQVc3QyxFQUFFLEVBQUVxQztZQUVsQywyQ0FBMkM7WUFDM0MsTUFBTXBDLG9CQUFvQixJQUFJLENBQUN0RCxFQUFFLENBQUNNLE9BQU8sQ0FBQyxDQUFDOzs7O01BSTNDLENBQUM7WUFDRGdELGtCQUFrQi9CLEdBQUcsQ0FBQzJFLFdBQVc3QyxFQUFFO1lBRW5DLDJCQUEyQjtZQUMzQixJQUFJLENBQUMrQyxXQUFXLENBQUNWLFFBQVEsbUJBQW1CLENBQUMsMEJBQTBCLEVBQUVNLGNBQWMsQ0FBQztZQUV4RixPQUFPO2dCQUNMSyxTQUFTO2dCQUNUQyxjQUFjSixXQUFXN0MsRUFBRTtnQkFDM0IyQyxlQUFlQTtnQkFDZnhCLFdBQVcwQixXQUFXbkQsVUFBVTtnQkFDaENvQixTQUFTK0IsV0FBV2pELFFBQVE7Z0JBQzVCc0QsYUFBYUwsV0FBV2xELFlBQVksR0FBRztZQUN6QztRQUNGO1FBRUEsT0FBT047SUFDVDtJQUVBLHFCQUFxQjtJQUNyQjhELGNBQWNkLE1BQU0sRUFBRWUsU0FBUyxFQUFFakMsU0FBUyxFQUFFa0MsWUFBWSxJQUFJLEVBQUVDLFlBQVksSUFBSSxFQUFFO1FBQzlFLE1BQU1yRixPQUFPLElBQUksQ0FBQ3RCLEVBQUUsQ0FBQ00sT0FBTyxDQUFDLENBQUM7OztJQUc5QixDQUFDO1FBRUQsT0FBT2dCLEtBQUtDLEdBQUcsQ0FBQ21FLFFBQVFlLFdBQVdqQyxXQUFXa0MsV0FBV0M7SUFDM0Q7SUFFQUMsZ0JBQWdCSCxTQUFTLEVBQUU7UUFDekIsTUFBTW5GLE9BQU8sSUFBSSxDQUFDdEIsRUFBRSxDQUFDTSxPQUFPLENBQUMsQ0FBQzs7Ozs7SUFLOUIsQ0FBQztRQUVELE9BQU9nQixLQUFLTCxHQUFHLENBQUN3RjtJQUNsQjtJQUVBSSxrQkFBa0JKLFNBQVMsRUFBRTtRQUMzQixNQUFNbkYsT0FBTyxJQUFJLENBQUN0QixFQUFFLENBQUNNLE9BQU8sQ0FBQztRQUM3QixPQUFPZ0IsS0FBS0MsR0FBRyxDQUFDa0Y7SUFDbEI7SUFFQUssMEJBQTBCcEIsTUFBTSxFQUFFO1FBQ2hDLE1BQU1wRSxPQUFPLElBQUksQ0FBQ3RCLEVBQUUsQ0FBQ00sT0FBTyxDQUFDO1FBQzdCLE9BQU9nQixLQUFLQyxHQUFHLENBQUNtRTtJQUNsQjtJQUVBLG1CQUFtQjtJQUNuQlUsWUFBWVYsTUFBTSxFQUFFcUIsTUFBTSxFQUFFQyxVQUFVLElBQUksRUFBRU4sWUFBWSxJQUFJLEVBQUU7UUFDNUQsTUFBTXBGLE9BQU8sSUFBSSxDQUFDdEIsRUFBRSxDQUFDTSxPQUFPLENBQUMsQ0FBQzs7O0lBRzlCLENBQUM7UUFFRCxPQUFPZ0IsS0FBS0MsR0FBRyxDQUFDbUUsUUFBUXFCLFFBQVFDLFNBQVNOO0lBQzNDO0lBRUEsMENBQTBDO0lBQzFDTyxxQkFBcUI7UUFDbkIsSUFBSTtZQUNGLE1BQU0zRixPQUFPLElBQUksQ0FBQ3RCLEVBQUUsQ0FBQ00sT0FBTyxDQUFDLENBQUM7OztNQUc5QixDQUFDO1lBQ0QsTUFBTThDLFNBQVM5QixLQUFLQyxHQUFHO1lBRXZCLElBQUk2QixPQUFPOEQsT0FBTyxHQUFHLEdBQUc7Z0JBQ3RCdEcsUUFBUUMsR0FBRyxDQUFDLENBQUMsY0FBYyxFQUFFdUMsT0FBTzhELE9BQU8sQ0FBQyx3Q0FBd0MsQ0FBQztnQkFDckYsSUFBSSxDQUFDZCxXQUFXLENBQUMsTUFBTSxrQkFBa0IsQ0FBQyxRQUFRLEVBQUVoRCxPQUFPOEQsT0FBTyxDQUFDLGdDQUFnQyxDQUFDO1lBQ3RHO1lBRUEsT0FBTzlELE9BQU84RCxPQUFPO1FBQ3ZCLEVBQUUsT0FBTzlHLE9BQU87WUFDZFEsUUFBUVIsS0FBSyxDQUFDLG1DQUFtQ0E7WUFDakQsT0FBTztRQUNUO0lBQ0Y7SUFFQStHLGdCQUFnQnpCLFNBQVMsSUFBSSxFQUFFTCxRQUFRLEdBQUcsRUFBRUMsU0FBUyxDQUFDLEVBQUU7UUFDdEQsSUFBSThCLFFBQVEsQ0FBQzs7Ozs7O0lBTWIsQ0FBQztRQUVELE1BQU1DLFNBQVMsRUFBRTtRQUVqQixJQUFJM0IsUUFBUTtZQUNWMEIsU0FBUztZQUNUQyxPQUFPckMsSUFBSSxDQUFDVTtRQUNkO1FBRUEwQixTQUFTO1FBQ1RDLE9BQU9yQyxJQUFJLENBQUNLLE9BQU9DO1FBRW5CLE1BQU1oRSxPQUFPLElBQUksQ0FBQ3RCLEVBQUUsQ0FBQ00sT0FBTyxDQUFDOEc7UUFDN0IsT0FBTzlGLEtBQUtmLEdBQUcsSUFBSThHO0lBQ3JCO0lBRUEsb0JBQW9CO0lBQ3BCQyxpQkFBaUI7UUFDZixNQUFNQyxRQUFRLENBQUM7UUFFZixjQUFjO1FBQ2RBLE1BQU1DLFVBQVUsR0FBRyxJQUFJLENBQUN4SCxFQUFFLENBQUNNLE9BQU8sQ0FBQywyREFBNkRXLEdBQUcsR0FBR3dHLEtBQUs7UUFFM0csK0NBQStDO1FBQy9DRixNQUFNRyxXQUFXLEdBQUcsSUFBSSxDQUFDMUgsRUFBRSxDQUFDTSxPQUFPLENBQUMsQ0FBQzs7O0lBR3JDLENBQUMsRUFBRVcsR0FBRyxHQUFHd0csS0FBSztRQUVkLHFCQUFxQjtRQUNyQkYsTUFBTUksZ0JBQWdCLEdBQUcsSUFBSSxDQUFDM0gsRUFBRSxDQUFDTSxPQUFPLENBQUMsOENBQThDVyxHQUFHLEdBQUd3RyxLQUFLO1FBRWxHLHNCQUFzQjtRQUN0QkYsTUFBTUssaUJBQWlCLEdBQUcsSUFBSSxDQUFDNUgsRUFBRSxDQUFDTSxPQUFPLENBQUMsQ0FBQzs7O0lBRzNDLENBQUMsRUFBRVcsR0FBRyxHQUFHd0csS0FBSztRQUVkLHVCQUF1QjtRQUN2QkYsTUFBTU0sa0JBQWtCLEdBQUcsSUFBSSxDQUFDN0gsRUFBRSxDQUFDTSxPQUFPLENBQUMsQ0FBQzs7O0lBRzVDLENBQUMsRUFBRVcsR0FBRyxHQUFHd0csS0FBSztRQUVkLGtDQUFrQztRQUNsQ0YsTUFBTU8sY0FBYyxHQUFHLElBQUksQ0FBQzlILEVBQUUsQ0FBQ00sT0FBTyxDQUFDLENBQUM7OztJQUd4QyxDQUFDLEVBQUVXLEdBQUcsR0FBR3dHLEtBQUs7UUFFZCxPQUFPRjtJQUNUO0lBRUEsNEJBQTRCO0lBQzVCUSxTQUFTMUMsUUFBUSxFQUFFLEVBQUVDLFNBQVMsQ0FBQyxFQUFFO1FBQy9CLE1BQU1oRSxPQUFPLElBQUksQ0FBQ3RCLEVBQUUsQ0FBQ00sT0FBTyxDQUFDLENBQUM7Ozs7Ozs7Ozs7Ozs7O0lBYzlCLENBQUM7UUFFRCxPQUFPZ0IsS0FBS2YsR0FBRyxDQUFDOEUsT0FBT0M7SUFDekI7SUFFQTBDLGlCQUFpQnRDLE1BQU0sRUFBRTtRQUN2QixNQUFNcEUsT0FBTyxJQUFJLENBQUN0QixFQUFFLENBQUNNLE9BQU8sQ0FBQztRQUM3QixPQUFPZ0IsS0FBS0MsR0FBRyxDQUFDbUU7SUFDbEI7SUFFQSxrQkFBa0I7SUFDbEJ1Qyx5QkFBeUI7UUFDdkIsTUFBTTNHLE9BQU8sSUFBSSxDQUFDdEIsRUFBRSxDQUFDTSxPQUFPLENBQUM7UUFDN0IsT0FBT2dCLEtBQUtDLEdBQUc7SUFDakI7SUFFQTJHLGVBQWVDLGFBQWEsRUFBRSxFQUFFO1FBQzlCLE1BQU03RyxPQUFPLElBQUksQ0FBQ3RCLEVBQUUsQ0FBQ00sT0FBTyxDQUFDLENBQUM7OzRDQUVVLEVBQUU2SCxXQUFXO0lBQ3JELENBQUM7UUFDRCxPQUFPN0csS0FBS0MsR0FBRztJQUNqQjtJQUVBLGdDQUFnQztJQUNoQzZHLHlCQUF5QjFDLE1BQU0sRUFBRTJDLFdBQVcsRUFBRUMsTUFBTSxFQUFFQyxLQUFLLEVBQUUvRixRQUFRLEVBQUU7UUFDckUsTUFBTWdHLFNBQVNuSixtQkFBT0EsQ0FBQztRQUV2QiwwQ0FBMEM7UUFDMUMsTUFBTW9KLFNBQVMzSSxRQUFRc0IsR0FBRyxDQUFDc0gsNEJBQTRCO1FBQ3ZELElBQUksQ0FBQ0QsUUFBUTtZQUNYLE1BQU0sSUFBSTVGLE1BQU07UUFDbEI7UUFFQSw4QkFBOEI7UUFDOUIsTUFBTThGLFdBQVcsU0FBU0gsT0FBT0ksV0FBVyxDQUFDLEdBQUdDLFFBQVEsQ0FBQyxPQUFPMUQsV0FBVztRQUUzRSx1QkFBdUI7UUFDdkIsTUFBTTJELEtBQUtOLE9BQU9JLFdBQVcsQ0FBQztRQUU5QixxRUFBcUU7UUFDckUsTUFBTWpILE1BQU02RyxPQUFPTyxVQUFVLENBQUNOLFFBQVFPLE9BQU90RCxTQUFTO1FBRXRELHFDQUFxQztRQUNyQyxNQUFNdUQsVUFBVVQsT0FBT1UsY0FBYyxDQUFDLGVBQWV2SCxLQUFLbUg7UUFDMUQsTUFBTUssa0JBQWtCRixRQUFRRyxNQUFNLENBQUNkLFFBQVEsUUFBUSxTQUFTVyxRQUFRSSxLQUFLLENBQUM7UUFFOUUsTUFBTUMsVUFBVWQsT0FBT1UsY0FBYyxDQUFDLGVBQWV2SCxLQUFLbUg7UUFDMUQsTUFBTVMsaUJBQWlCRCxRQUFRRixNQUFNLENBQUNiLE9BQU8sUUFBUSxTQUFTZSxRQUFRRCxLQUFLLENBQUM7UUFFNUUsTUFBTUcsVUFBVWhCLE9BQU9VLGNBQWMsQ0FBQyxlQUFldkgsS0FBS21IO1FBQzFELE1BQU1XLG9CQUFvQkQsUUFBUUosTUFBTSxDQUFDNUcsVUFBVSxRQUFRLFNBQVNnSCxRQUFRSCxLQUFLLENBQUM7UUFFbEYsTUFBTS9ILE9BQU8sSUFBSSxDQUFDdEIsRUFBRSxDQUFDTSxPQUFPLENBQUMsQ0FBQzs7O0lBRzlCLENBQUM7UUFFRGdCLEtBQUtDLEdBQUcsQ0FBQ21FLFFBQVFpRCxVQUFVTixhQUFhYyxpQkFBaUJJLGdCQUFnQkUsbUJBQW1CWCxHQUFHRCxRQUFRLENBQUM7UUFFeEcsT0FBT0Y7SUFDVDtJQUVBZSx3QkFBd0JmLFFBQVEsRUFBRTtRQUNoQyxNQUFNckgsT0FBTyxJQUFJLENBQUN0QixFQUFFLENBQUNNLE9BQU8sQ0FBQyxDQUFDOzs7SUFHOUIsQ0FBQztRQUVELE1BQU04QyxTQUFTOUIsS0FBS0wsR0FBRyxDQUFDMEg7UUFDeEIsSUFBSSxDQUFDdkYsUUFBUSxPQUFPO1FBRXBCLE1BQU1vRixTQUFTbkosbUJBQU9BLENBQUM7UUFFdkIsSUFBSTtZQUNGLDBDQUEwQztZQUMxQyxNQUFNb0osU0FBUzNJLFFBQVFzQixHQUFHLENBQUNzSCw0QkFBNEI7WUFDdkQsSUFBSSxDQUFDRCxRQUFRO2dCQUNYLE1BQU0sSUFBSTVGLE1BQU07WUFDbEI7WUFFQSw4RUFBOEU7WUFDOUUsTUFBTWxCLE1BQU02RyxPQUFPTyxVQUFVLENBQUNOLFFBQVFPLE9BQU81RixPQUFPdUcsT0FBTyxHQUFHO1lBQzlELE1BQU1iLEtBQUtjLE9BQU9DLElBQUksQ0FBQ3pHLE9BQU8wRyxhQUFhLEVBQUU7WUFFN0MscUNBQXFDO1lBQ3JDLE1BQU1DLFlBQVl2QixPQUFPd0IsZ0JBQWdCLENBQUMsZUFBZXJJLEtBQUttSDtZQUM5RCxNQUFNUixTQUFTbEYsT0FBTzZHLGdCQUFnQixHQUFHRixVQUFVWCxNQUFNLENBQUNoRyxPQUFPNkcsZ0JBQWdCLEVBQUUsT0FBTyxVQUFVRixVQUFVVixLQUFLLENBQUMsVUFBVTtZQUU5SCxNQUFNYSxZQUFZMUIsT0FBT3dCLGdCQUFnQixDQUFDLGVBQWVySSxLQUFLbUg7WUFDOUQsTUFBTVAsUUFBUW5GLE9BQU8rRyxlQUFlLEdBQUdELFVBQVVkLE1BQU0sQ0FBQ2hHLE9BQU8rRyxlQUFlLEVBQUUsT0FBTyxVQUFVRCxVQUFVYixLQUFLLENBQUMsVUFBVTtZQUUzSCxNQUFNZSxZQUFZNUIsT0FBT3dCLGdCQUFnQixDQUFDLGVBQWVySSxLQUFLbUg7WUFDOUQsTUFBTXRHLFdBQVdZLE9BQU9pSCxrQkFBa0IsR0FBR0QsVUFBVWhCLE1BQU0sQ0FBQ2hHLE9BQU9pSCxrQkFBa0IsRUFBRSxPQUFPLFVBQVVELFVBQVVmLEtBQUssQ0FBQyxVQUFVO1lBRXBJLE9BQU87Z0JBQ0xoQixhQUFhakYsT0FBT2tILFlBQVk7Z0JBQ2hDaEM7Z0JBQ0FDO2dCQUNBL0Y7Z0JBQ0FrRCxRQUFRdEMsT0FBT3VHLE9BQU87WUFDeEI7UUFDRixFQUFFLE9BQU92SixPQUFPO1lBQ2RRLFFBQVFSLEtBQUssQ0FBQyxrQ0FBa0NBO1lBQ2hELE9BQU87UUFDVDtJQUNGO0lBRUFtSyxtQkFBbUI3RSxNQUFNLEVBQUU7UUFDekIsTUFBTXBFLE9BQU8sSUFBSSxDQUFDdEIsRUFBRSxDQUFDTSxPQUFPLENBQUMsQ0FBQzs7OztJQUk5QixDQUFDO1FBRUQsT0FBT2dCLEtBQUtmLEdBQUcsQ0FBQ21GO0lBQ2xCO0lBRUE4RSxzQkFBc0I3QixRQUFRLEVBQUU7UUFDOUIsTUFBTXJILE9BQU8sSUFBSSxDQUFDdEIsRUFBRSxDQUFDTSxPQUFPLENBQUM7UUFDN0IsT0FBT2dCLEtBQUtDLEdBQUcsQ0FBQ29IO0lBQ2xCO0lBRUEsbUNBQW1DO0lBQ25DOEIsbUJBQW1CQyxZQUFZLEVBQUV0RyxRQUFRLEVBQUU7UUFDekMsTUFBTTlDLE9BQU8sSUFBSSxDQUFDdEIsRUFBRSxDQUFDTSxPQUFPLENBQUMsQ0FBQzs7OztJQUk5QixDQUFDO1FBRUQsT0FBT2dCLEtBQUtDLEdBQUcsQ0FDYm1KLGNBQ0F0RyxTQUFTdUcsc0JBQXNCLElBQUksR0FDbkN2RyxTQUFTd0csY0FBYyxJQUFJLEdBQzNCeEcsU0FBU3lHLGlCQUFpQixHQUFHLElBQUksR0FDakN6RyxTQUFTMEcsbUJBQW1CLElBQUk7SUFFcEM7SUFFQUMsbUJBQW1CTCxZQUFZLEVBQUU7UUFDL0IsTUFBTXBKLE9BQU8sSUFBSSxDQUFDdEIsRUFBRSxDQUFDTSxPQUFPLENBQUMsQ0FBQzs7SUFFOUIsQ0FBQztRQUVELE1BQU04QyxTQUFTOUIsS0FBS0wsR0FBRyxDQUFDeUo7UUFDeEIsSUFBSSxDQUFDdEgsUUFBUTtZQUNYLHNDQUFzQztZQUN0QyxPQUFPO2dCQUNMdUgsd0JBQXdCO2dCQUN4QkMsZ0JBQWdCO2dCQUNoQkMsbUJBQW1CO2dCQUNuQkMscUJBQXFCO1lBQ3ZCO1FBQ0Y7UUFFQSxPQUFPO1lBQ0xILHdCQUF3QnZILE9BQU91SCxzQkFBc0I7WUFDckRDLGdCQUFnQnhILE9BQU93SCxjQUFjO1lBQ3JDQyxtQkFBbUJHLFFBQVE1SCxPQUFPeUgsaUJBQWlCO1lBQ25EQyxxQkFBcUIxSCxPQUFPMEgsbUJBQW1CLElBQUk7UUFDckQ7SUFDRjtJQUVBRyx1QkFBdUJ2RixNQUFNLEVBQUU7UUFDN0IsTUFBTXBFLE9BQU8sSUFBSSxDQUFDdEIsRUFBRSxDQUFDTSxPQUFPLENBQUMsQ0FBQzs7OztJQUk5QixDQUFDO1FBRUQsTUFBTThDLFNBQVM5QixLQUFLTCxHQUFHLENBQUN5RTtRQUN4QixJQUFJLENBQUN0QyxRQUFRO1lBQ1gsT0FBTztnQkFDTHVILHdCQUF3QjtnQkFDeEJDLGdCQUFnQjtnQkFDaEJDLG1CQUFtQjtnQkFDbkJDLHFCQUFxQjtZQUN2QjtRQUNGO1FBRUEsT0FBTztZQUNMSCx3QkFBd0J2SCxPQUFPdUgsc0JBQXNCO1lBQ3JEQyxnQkFBZ0J4SCxPQUFPd0gsY0FBYztZQUNyQ0MsbUJBQW1CRyxRQUFRNUgsT0FBT3lILGlCQUFpQjtZQUNuREMscUJBQXFCMUgsT0FBTzBILG1CQUFtQixJQUFJO1FBQ3JEO0lBQ0Y7SUFFQSwwQkFBMEI7SUFDMUJJLHVCQUF1QnhGLE1BQU0sRUFBRXlGLE9BQU8sSUFBSSxFQUFFO1FBQzFDLE1BQU1DLGFBQWFELFFBQVEsSUFBSXJJLE9BQU82QixXQUFXLEdBQUcwRyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsRUFBRSxvQkFBb0I7UUFDdkYsTUFBTS9KLE9BQU8sSUFBSSxDQUFDdEIsRUFBRSxDQUFDTSxPQUFPLENBQUMsQ0FBQzs7Ozs7SUFLOUIsQ0FBQztRQUVELE1BQU04QyxTQUFTOUIsS0FBS0wsR0FBRyxDQUFDeUUsUUFBUTBGO1FBQ2hDLE9BQU9oSSxPQUFPcUUsS0FBSztJQUNyQjtJQUVBLDhCQUE4QjtJQUM5QjZELDJCQUEyQjVGLE1BQU0sRUFBRTtRQUNqQyw2Q0FBNkM7UUFDN0MsTUFBTTZGLE1BQU0sSUFBSXpJO1FBQ2hCLE1BQU0wSSxZQUFZRCxJQUFJRSxNQUFNLElBQUksK0JBQStCO1FBQy9ELE1BQU1DLGVBQWVGLGNBQWMsSUFBSSxJQUFJQSxZQUFZLEdBQUcsc0NBQXNDO1FBQ2hHLE1BQU1HLGNBQWMsSUFBSTdJLEtBQUt5STtRQUM3QkksWUFBWWxILE9BQU8sQ0FBQzhHLElBQUk3RyxPQUFPLEtBQUtnSDtRQUNwQ0MsWUFBWUMsUUFBUSxDQUFDLEdBQUcsR0FBRyxHQUFHO1FBRTlCLE1BQU10SyxPQUFPLElBQUksQ0FBQ3RCLEVBQUUsQ0FBQ00sT0FBTyxDQUFDLENBQUM7Ozs7OztJQU05QixDQUFDO1FBRUQsTUFBTThDLFNBQVM5QixLQUFLTCxHQUFHLENBQUN5RSxRQUFRaUcsWUFBWWhILFdBQVc7UUFDdkQsT0FBT3ZCLE9BQU9xRSxLQUFLO0lBQ3JCO0lBRUEsc0JBQXNCO0lBQ3RCb0UsaUJBQWlCbkcsTUFBTSxFQUFFb0csU0FBUyxFQUFFQyxRQUFRLEVBQUVDLGdCQUFnQixJQUFJLEVBQUVDLFlBQVksUUFBUSxFQUFFQyxZQUFZLEdBQUcsRUFBRTtRQUN6RyxNQUFNeEosY0FBYyxJQUFJLENBQUMxQyxFQUFFLENBQUMwQyxXQUFXLENBQUM7WUFDdEMsOEJBQThCO1lBQzlCLE1BQU0wQixXQUFXLElBQUksQ0FBQzZHLHNCQUFzQixDQUFDdkY7WUFFN0MsMEJBQTBCO1lBQzFCLE1BQU15RyxrQkFBa0IsSUFBSSxDQUFDakIsc0JBQXNCLENBQUN4RjtZQUNwRCxJQUFJeUcsbUJBQW1CL0gsU0FBUzBHLG1CQUFtQixFQUFFO2dCQUNuRCxNQUFNLElBQUlqSSxNQUFNLENBQUMsMkJBQTJCLEVBQUV1QixTQUFTMEcsbUJBQW1CLENBQUMsNkNBQTZDLENBQUM7WUFDM0g7WUFFQSw2Q0FBNkM7WUFDN0MsSUFBSTFHLFNBQVN1RyxzQkFBc0IsR0FBRyxLQUFLb0IsU0FBU0ssTUFBTSxHQUFHaEksU0FBU3VHLHNCQUFzQixFQUFFO2dCQUM1RixNQUFNLElBQUk5SCxNQUFNLENBQUMsWUFBWSxFQUFFa0osU0FBU0ssTUFBTSxDQUFDLHlCQUF5QixFQUFFaEksU0FBU3VHLHNCQUFzQixDQUFDLENBQUMsQ0FBQztZQUM5RztZQUVBLDZCQUE2QjtZQUM3QixJQUFJcUIsaUJBQWlCLENBQUM1SCxTQUFTeUcsaUJBQWlCLEVBQUU7Z0JBQ2hELE1BQU0sSUFBSWhJLE1BQU07WUFDbEI7WUFFQSxzQkFBc0I7WUFDdEIsSUFBSSxDQUFDO2dCQUFDO2dCQUFVO2dCQUFVO2FBQVksQ0FBQ3dKLFFBQVEsQ0FBQ0osWUFBWTtnQkFDMUQsTUFBTSxJQUFJcEosTUFBTTtZQUNsQjtZQUVBLHNCQUFzQjtZQUN0QixJQUFJcUosWUFBWSxLQUFLQSxZQUFZLEtBQUs7Z0JBQ3BDLE1BQU0sSUFBSXJKLE1BQU07WUFDbEI7WUFFQSxlQUFlO1lBQ2YsTUFBTXlKLFlBQVksSUFBSSxDQUFDdE0sRUFBRSxDQUFDTSxPQUFPLENBQUMsQ0FBQzs7O01BR25DLENBQUM7WUFFRCxNQUFNaU0sY0FBY0QsVUFBVS9LLEdBQUcsQ0FDL0JtRSxRQUNBb0csV0FDQUcsV0FDQUYsU0FBU0ssTUFBTSxFQUNmaEksU0FBU3dHLGNBQWMsRUFDdkJzQixXQUNBRjtZQUdGLE1BQU1RLFVBQVVELFlBQVloSixlQUFlO1lBRTNDLDBDQUEwQztZQUMxQyxNQUFNa0osVUFBVSxJQUFJLENBQUN6TSxFQUFFLENBQUNNLE9BQU8sQ0FBQyxDQUFDOzs7TUFHakMsQ0FBQztZQUVEeUwsU0FBU1csT0FBTyxDQUFDQyxDQUFBQTtnQkFDZixNQUFNQyxvQkFBb0IsSUFBSSxDQUFDQywwQkFBMEIsQ0FBQ3pJLFNBQVN3RyxjQUFjLEVBQUVvQjtnQkFDbkZTLFFBQVFsTCxHQUFHLENBQ1RpTCxTQUNBOUcsUUFDQWQsS0FBS0MsU0FBUyxDQUFDOEgsVUFDZnZJLFNBQVN3RyxjQUFjLEVBQ3ZCZ0MsbUJBQ0FWLFdBQ0FGO1lBRUo7WUFFQSxxQ0FBcUM7WUFDckMsSUFBSUEsZUFBZTtnQkFDakIsSUFBSSxDQUFDYyxtQkFBbUIsQ0FBQ3BILFFBQVFzRyxlQUFlLE1BQU1RLFNBQVMsSUFBSU47WUFDckU7WUFFQSxlQUFlO1lBQ2YsSUFBSSxDQUFDOUYsV0FBVyxDQUFDVixRQUFRLGlCQUFpQixDQUFDLGVBQWUsRUFBRW9HLFVBQVUsTUFBTSxFQUFFQyxTQUFTSyxNQUFNLENBQUMsU0FBUyxDQUFDO1lBRXhHLE9BQU9JO1FBQ1Q7UUFFQSxPQUFPOUo7SUFDVDtJQUVBbUssMkJBQTJCRSxZQUFZLEVBQUVmLGFBQWEsRUFBRTtRQUN0RCxJQUFJWSxvQkFBb0JHO1FBRXhCLDJEQUEyRDtRQUMzRCxJQUFJZixlQUFlO1lBQ2pCLE1BQU1ULE1BQU0sSUFBSXpJO1lBQ2hCLE1BQU1rSyxZQUFZLElBQUlsSyxLQUFLa0o7WUFDM0IsTUFBTWlCLFdBQVdELFVBQVVFLE9BQU8sS0FBSzNCLElBQUkyQixPQUFPO1lBQ2xELE1BQU1DLGFBQWFGLFdBQVksUUFBTyxLQUFLLEVBQUM7WUFFNUMsSUFBSUUsY0FBYyxHQUFHO2dCQUNuQlAscUJBQXFCLEdBQUcseUNBQXlDO1lBQ25FLE9BQU8sSUFBSU8sY0FBYyxHQUFHO2dCQUMxQlAscUJBQXFCLEdBQUcsMkNBQTJDO1lBQ3JFO1FBQ0Y7UUFFQSw0REFBNEQ7UUFDNUQsb0RBQW9EO1FBRXBELE9BQU9RLEtBQUtDLEdBQUcsQ0FBQ1QsbUJBQW1CLEtBQUssMEJBQTBCO0lBQ3BFO0lBRUFVLGdCQUFnQjVILFNBQVMsSUFBSSxFQUFFRyxTQUFTLElBQUksRUFBRVIsUUFBUSxFQUFFLEVBQUVDLFNBQVMsQ0FBQyxFQUFFO1FBQ3BFLElBQUk4QixRQUFRLENBQUM7Ozs7O0lBS2IsQ0FBQztRQUNELE1BQU1DLFNBQVMsRUFBRTtRQUVqQixJQUFJM0IsUUFBUTtZQUNWMEIsU0FBUztZQUNUQyxPQUFPckMsSUFBSSxDQUFDVTtRQUNkO1FBRUEsSUFBSUcsUUFBUTtZQUNWdUIsU0FBUztZQUNUQyxPQUFPckMsSUFBSSxDQUFDYTtRQUNkO1FBRUF1QixTQUFTO1FBQ1RDLE9BQU9yQyxJQUFJLENBQUNLLE9BQU9DO1FBRW5CLE1BQU1oRSxPQUFPLElBQUksQ0FBQ3RCLEVBQUUsQ0FBQ00sT0FBTyxDQUFDOEc7UUFDN0IsT0FBTzlGLEtBQUtmLEdBQUcsSUFBSThHO0lBQ3JCO0lBRUFrRyxhQUFhZixPQUFPLEVBQUU7UUFDcEIsTUFBTWxMLE9BQU8sSUFBSSxDQUFDdEIsRUFBRSxDQUFDTSxPQUFPLENBQUMsQ0FBQzs7OztJQUk5QixDQUFDO1FBRUQsT0FBT2dCLEtBQUtmLEdBQUcsQ0FBQ2lNO0lBQ2xCO0lBRUFnQixrQkFBa0JoQixPQUFPLEVBQUUzRyxNQUFNLEVBQUU0SCxjQUFjLElBQUksRUFBRTtRQUNyRCxNQUFNbk0sT0FBTyxJQUFJLENBQUN0QixFQUFFLENBQUNNLE9BQU8sQ0FBQyxDQUFDOzs7O0lBSTlCLENBQUM7UUFFRCxPQUFPZ0IsS0FBS0MsR0FBRyxDQUFDc0UsUUFBUTRILGFBQWFqQjtJQUN2QztJQUVBLG9CQUFvQjtJQUNwQmtCLGVBQWUsRUFBRW5MLFFBQVEsRUFBRW9MLFdBQVcsY0FBYyxFQUFFQyxRQUFRLEVBQUVDLGFBQWEsR0FBRyxFQUFFQyxXQUFXLENBQUMsRUFBRWpJLFNBQVMsUUFBUSxFQUFFLEVBQUU7UUFDbkgsY0FBYztRQUNkLE1BQU0xQyxXQUFXLElBQUksQ0FBQ25ELEVBQUUsQ0FBQ00sT0FBTyxDQUFDO1FBQ2pDLE1BQU1tRCxPQUFPTixTQUFTbEMsR0FBRyxDQUFDc0I7UUFFMUIsSUFBSSxDQUFDa0IsTUFBTTtZQUNULE1BQU0sSUFBSVosTUFBTTtRQUNsQjtRQUVBLGlCQUFpQjtRQUNqQixNQUFNdkIsT0FBTyxJQUFJLENBQUN0QixFQUFFLENBQUNNLE9BQU8sQ0FBQyxDQUFDOzs7SUFHOUIsQ0FBQztRQUVELE1BQU04QyxTQUFTOUIsS0FBS0MsR0FBRyxDQUFDa0MsS0FBS0osRUFBRSxFQUFFc0ssVUFBVUMsVUFBVUUsVUFBVUEsVUFBVUQsWUFBWWhJO1FBQ3JGLE9BQU96QyxPQUFPRyxlQUFlO0lBQy9CO0lBRUF3SyxhQUFhckksU0FBUyxJQUFJLEVBQUU7UUFDMUIsSUFBSTBCLFFBQVEsQ0FBQzs7Ozs7SUFLYixDQUFDO1FBQ0QsTUFBTUMsU0FBUyxFQUFFO1FBRWpCLElBQUkzQixRQUFRO1lBQ1YwQixTQUFTO1lBQ1RDLE9BQU9yQyxJQUFJLENBQUNVO1FBQ2Q7UUFFQTBCLFNBQVM7UUFFVCxNQUFNOUYsT0FBTyxJQUFJLENBQUN0QixFQUFFLENBQUNNLE9BQU8sQ0FBQzhHO1FBQzdCLE9BQU85RixLQUFLZixHQUFHLElBQUk4RztJQUNyQjtJQUVBMkcsa0JBQWtCO1FBQ2hCLE1BQU0xTSxPQUFPLElBQUksQ0FBQ3RCLEVBQUUsQ0FBQ00sT0FBTyxDQUFDLENBQUM7Ozs7Ozs7OztJQVM5QixDQUFDO1FBRUQsT0FBT2dCLEtBQUtMLEdBQUc7SUFDakI7SUFFQWdOLGdCQUFnQkMsS0FBSyxFQUFFckksTUFBTSxFQUFFQyxlQUFlLElBQUksRUFBRXFJLFlBQVksSUFBSSxFQUFFVixjQUFjLElBQUksRUFBRTtRQUN4RixNQUFNbk0sT0FBTyxJQUFJLENBQUN0QixFQUFFLENBQUNNLE9BQU8sQ0FBQyxDQUFDOzs7O0lBSTlCLENBQUM7UUFFRCxPQUFPZ0IsS0FBS0MsR0FBRyxDQUFDc0UsUUFBUUMsY0FBY3FJLFdBQVdWLGFBQWFTO0lBQ2hFO0lBRUFFLGtCQUFrQkYsS0FBSyxFQUFFO1FBQ3ZCLE1BQU01TSxPQUFPLElBQUksQ0FBQ3RCLEVBQUUsQ0FBQ00sT0FBTyxDQUFDLENBQUM7Ozs7SUFJOUIsQ0FBQztRQUVELE9BQU9nQixLQUFLQyxHQUFHLENBQUMyTTtJQUNsQjtJQUVBLHFCQUFxQjtJQUNyQnBCLG9CQUFvQnBILE1BQU0sRUFBRXNHLGFBQWEsRUFBRWtDLFFBQVEsSUFBSSxFQUFFMUIsVUFBVSxJQUFJLEVBQUU2QixrQkFBa0IsRUFBRSxFQUFFbkMsWUFBWSxHQUFHLEVBQUU7UUFDOUcsc0JBQXNCO1FBQ3RCLE1BQU1vQyxZQUFZLElBQUksQ0FBQ0Msc0JBQXNCLENBQUM3SSxRQUFRc0csZUFBZXFDO1FBQ3JFLElBQUlDLFVBQVVsQyxNQUFNLEdBQUcsR0FBRztZQUN4QixNQUFNLElBQUl2SixNQUFNLENBQUMsOEJBQThCLEVBQUVtSixjQUFjLENBQUM7UUFDbEU7UUFFQSxNQUFNMUssT0FBTyxJQUFJLENBQUN0QixFQUFFLENBQUNNLE9BQU8sQ0FBQyxDQUFDOzs7SUFHOUIsQ0FBQztRQUVELE9BQU9nQixLQUFLQyxHQUFHLENBQUNtRSxRQUFRc0csZUFBZXFDLGlCQUFpQm5DLFdBQVdnQyxPQUFPMUI7SUFDNUU7SUFFQStCLHVCQUF1QjdJLE1BQU0sRUFBRXNHLGFBQWEsRUFBRXFDLGVBQWUsRUFBRTtRQUM3RCxNQUFNRyxZQUFZLElBQUkxTCxLQUFLa0o7UUFDM0IsTUFBTXlDLFVBQVUsSUFBSTNMLEtBQUswTCxVQUFVdEIsT0FBTyxLQUFNbUIsa0JBQWtCLEtBQUs7UUFFdkUsTUFBTS9NLE9BQU8sSUFBSSxDQUFDdEIsRUFBRSxDQUFDTSxPQUFPLENBQUMsQ0FBQzs7Ozs7Ozs7SUFROUIsQ0FBQztRQUVELE9BQU9nQixLQUFLZixHQUFHLENBQUNtRixRQUFROEksVUFBVTdKLFdBQVcsSUFBSTZKLFVBQVU3SixXQUFXLElBQ3ZEOEosUUFBUTlKLFdBQVcsSUFBSThKLFFBQVE5SixXQUFXO0lBQzNEO0lBRUErSixpQkFBaUJoSixNQUFNLEVBQUVpSixZQUFZLElBQUksRUFBRUMsVUFBVSxJQUFJLEVBQUU7UUFDekQsSUFBSXhILFFBQVEsQ0FBQzs7Ozs7O0lBTWIsQ0FBQztRQUNELE1BQU1DLFNBQVM7WUFBQzNCO1NBQU87UUFFdkIsSUFBSWlKLFdBQVc7WUFDYnZILFNBQVM7WUFDVEMsT0FBT3JDLElBQUksQ0FBQzJKO1FBQ2Q7UUFFQSxJQUFJQyxTQUFTO1lBQ1h4SCxTQUFTO1lBQ1RDLE9BQU9yQyxJQUFJLENBQUM0SjtRQUNkO1FBRUF4SCxTQUFTO1FBRVQsTUFBTTlGLE9BQU8sSUFBSSxDQUFDdEIsRUFBRSxDQUFDTSxPQUFPLENBQUM4RztRQUM3QixPQUFPOUYsS0FBS2YsR0FBRyxJQUFJOEc7SUFDckI7SUFFQSw4QkFBOEI7SUFDOUJ3SCxrQkFBa0JYLEtBQUssRUFBRVksV0FBVyxFQUFFQyxnQkFBZ0IsS0FBSyxFQUFFO1FBQzNELE1BQU16TixPQUFPLElBQUksQ0FBQ3RCLEVBQUUsQ0FBQ00sT0FBTyxDQUFDLENBQUM7Ozs7SUFJOUIsQ0FBQztRQUVELE1BQU04QyxTQUFTOUIsS0FBS0MsR0FBRyxDQUFDdU4sYUFBYUMsZ0JBQWdCRCxjQUFjLE1BQU1aO1FBRXpFLElBQUlhLGVBQWU7WUFDakIsSUFBSSxDQUFDM0ksV0FBVyxDQUFDLE1BQU0sMkJBQTJCLENBQUMsSUFBSSxFQUFFOEgsTUFBTSxpQkFBaUIsRUFBRVksWUFBWSxDQUFDO1FBQ2pHO1FBRUEsT0FBTzFMO0lBQ1Q7SUFFQTRMLDRCQUE0QjtRQUMxQiwwREFBMEQ7UUFDMUQsTUFBTTFOLE9BQU8sSUFBSSxDQUFDdEIsRUFBRSxDQUFDTSxPQUFPLENBQUMsQ0FBQzs7Ozs7Ozs7SUFROUIsQ0FBQztRQUVELE9BQU9nQixLQUFLQyxHQUFHO0lBQ2pCO0lBRUEwTixnQkFBZ0I7UUFDZCxNQUFNMUgsUUFBUSxDQUFDO1FBRWYsdUJBQXVCO1FBQ3ZCLE1BQU0ySCxhQUFhLElBQUksQ0FBQ2xQLEVBQUUsQ0FBQ00sT0FBTyxDQUFDLENBQUM7Ozs7SUFJcEMsQ0FBQztRQUNEaUgsTUFBTTRILFlBQVksR0FBR0QsV0FBVzNPLEdBQUc7UUFFbkMseUJBQXlCO1FBQ3pCLE1BQU02TyxlQUFlLElBQUksQ0FBQ3BQLEVBQUUsQ0FBQ00sT0FBTyxDQUFDLENBQUM7Ozs7OztJQU10QyxDQUFDO1FBQ0RpSCxNQUFNOEgsY0FBYyxHQUFHRCxhQUFhN08sR0FBRztRQUV2QyxvQkFBb0I7UUFDcEIsTUFBTStPLGVBQWUsSUFBSSxDQUFDdFAsRUFBRSxDQUFDTSxPQUFPLENBQUMsQ0FBQzs7OztJQUl0QyxDQUFDO1FBQ0RpSCxNQUFNZ0ksZUFBZSxHQUFHRCxhQUFhck8sR0FBRyxJQUFJdU8sb0JBQW9CO1FBRWhFLE9BQU9qSTtJQUNUO0lBRUFrSSxRQUFRO1FBQ04sSUFBSSxDQUFDelAsRUFBRSxDQUFDeVAsS0FBSztJQUNmO0lBQ0EsK0JBQStCO0lBQy9CQyxnQkFBZ0IvTixHQUFHLEVBQUU7UUFDbkIsTUFBTUwsT0FBTyxJQUFJLENBQUN0QixFQUFFLENBQUNNLE9BQU8sQ0FBQztRQUM3QixNQUFNNEIsU0FBU1osS0FBS0wsR0FBRyxDQUFDVTtRQUV4QixJQUFJLENBQUNPLFFBQVEsT0FBTztRQUVwQiw0QkFBNEI7UUFDNUIsT0FBUUEsT0FBT3lOLFdBQVc7WUFDeEIsS0FBSztnQkFDSCxPQUFPO29CQUFFLEdBQUd6TixNQUFNO29CQUFFME4sY0FBY0MsU0FBUzNOLE9BQU8wTixZQUFZO2dCQUFFO1lBQ2xFLEtBQUs7Z0JBQ0gsT0FBTztvQkFBRSxHQUFHMU4sTUFBTTtvQkFBRTBOLGNBQWMxTixPQUFPME4sWUFBWSxLQUFLO2dCQUFPO1lBQ25FLEtBQUs7Z0JBQ0gsT0FBTztvQkFBRSxHQUFHMU4sTUFBTTtvQkFBRTBOLGNBQWNoTCxLQUFLa0wsS0FBSyxDQUFDNU4sT0FBTzBOLFlBQVk7Z0JBQUU7WUFDcEU7Z0JBQ0UsT0FBTzFOO1FBQ1g7SUFDRjtJQUVBNk4sZ0JBQWdCcE8sR0FBRyxFQUFFQyxLQUFLLEVBQUVFLE9BQU8sUUFBUSxFQUFFQyxjQUFjLElBQUksRUFBRTtRQUMvRCxNQUFNaU8sY0FBY2xPLFNBQVMsU0FBUzhDLEtBQUtDLFNBQVMsQ0FBQ2pELFNBQVNBLE1BQU1pSCxRQUFRO1FBRTVFLE1BQU12SCxPQUFPLElBQUksQ0FBQ3RCLEVBQUUsQ0FBQ00sT0FBTyxDQUFDLENBQUM7OztJQUc5QixDQUFDO1FBRUQsT0FBT2dCLEtBQUtDLEdBQUcsQ0FBQ0ksS0FBS3FPLGFBQWFsTyxNQUFNQyxhQUFhSjtJQUN2RDtJQUVBc08scUJBQXFCO1FBQ25CLE1BQU0zTyxPQUFPLElBQUksQ0FBQ3RCLEVBQUUsQ0FBQ00sT0FBTyxDQUFDO1FBQzdCLE1BQU00UCxVQUFVNU8sS0FBS2YsR0FBRztRQUV4QixPQUFPMlAsUUFBUUMsR0FBRyxDQUFDak8sQ0FBQUE7WUFDakIsT0FBUUEsT0FBT3lOLFdBQVc7Z0JBQ3hCLEtBQUs7b0JBQ0gsT0FBTzt3QkFBRSxHQUFHek4sTUFBTTt3QkFBRTBOLGNBQWNDLFNBQVMzTixPQUFPME4sWUFBWTtvQkFBRTtnQkFDbEUsS0FBSztvQkFDSCxPQUFPO3dCQUFFLEdBQUcxTixNQUFNO3dCQUFFME4sY0FBYzFOLE9BQU8wTixZQUFZLEtBQUs7b0JBQU87Z0JBQ25FLEtBQUs7b0JBQ0gsT0FBTzt3QkFBRSxHQUFHMU4sTUFBTTt3QkFBRTBOLGNBQWNoTCxLQUFLa0wsS0FBSyxDQUFDNU4sT0FBTzBOLFlBQVk7b0JBQUU7Z0JBQ3BFO29CQUNFLE9BQU8xTjtZQUNYO1FBQ0Y7SUFDRjtJQUVBLDRCQUE0QjtJQUM1QmtPLHFCQUFxQkMsYUFBYSxFQUFFQyxPQUFPLEVBQUVDLE9BQU8sRUFBRTdLLE1BQU0sRUFBRThLLGFBQWEsRUFBRTtRQUMzRSxNQUFNbFAsT0FBTyxJQUFJLENBQUN0QixFQUFFLENBQUNNLE9BQU8sQ0FBQyxDQUFDOzs7SUFHOUIsQ0FBQztRQUVELE9BQU9nQixLQUFLQyxHQUFHLENBQUM4TyxlQUFlQyxTQUFTMUwsS0FBS0MsU0FBUyxDQUFDMEwsVUFBVTdLLFFBQVE4SztJQUMzRTtJQUVBQyx1QkFBdUI1SyxTQUFTLElBQUksRUFBRVIsUUFBUSxFQUFFLEVBQUVDLFNBQVMsQ0FBQyxFQUFFO1FBQzVELElBQUk4QixRQUFRLENBQUM7Ozs7SUFJYixDQUFDO1FBRUQsTUFBTUMsU0FBUyxFQUFFO1FBRWpCLElBQUl4QixRQUFRO1lBQ1Z1QixTQUFTO1lBQ1RDLE9BQU9yQyxJQUFJLENBQUNhO1FBQ2Q7UUFFQXVCLFNBQVM7UUFDVEMsT0FBT3JDLElBQUksQ0FBQ0ssT0FBT0M7UUFFbkIsTUFBTWhFLE9BQU8sSUFBSSxDQUFDdEIsRUFBRSxDQUFDTSxPQUFPLENBQUM4RztRQUM3QixPQUFPOUYsS0FBS2YsR0FBRyxJQUFJOEc7SUFDckI7SUFFQXFKLDRCQUE0QnJOLEVBQUUsRUFBRXdDLE1BQU0sRUFBRTtRQUN0QyxNQUFNdkUsT0FBTyxJQUFJLENBQUN0QixFQUFFLENBQUNNLE9BQU8sQ0FBQyxDQUFDOzs7O0lBSTlCLENBQUM7UUFFRCxPQUFPZ0IsS0FBS0MsR0FBRyxDQUFDc0UsUUFBUXhDO0lBQzFCO0lBRUFzTix3QkFBd0J0TixFQUFFLEVBQUU7UUFDMUIsTUFBTXVOLFVBQVUsSUFBSSxDQUFDNVEsRUFBRSxDQUFDTSxPQUFPLENBQUM7UUFDaEMsTUFBTXVRLE1BQU1ELFFBQVEzUCxHQUFHLENBQUNvQztRQUV4QixJQUFJLENBQUN3TixLQUFLO1lBQ1IsTUFBTSxJQUFJaE8sTUFBTTtRQUNsQjtRQUVBLDJEQUEyRDtRQUMzRCxNQUFNUixhQUFhLElBQUksQ0FBQ3JDLEVBQUUsQ0FBQ00sT0FBTyxDQUFDLENBQUM7OztJQUdwQyxDQUFDO1FBRUQsTUFBTThDLFNBQVNmLFdBQVdkLEdBQUcsQ0FBQ3NQLElBQUlsSCxPQUFPLEVBQUVrSCxJQUFJbEQsUUFBUSxFQUFFa0QsSUFBSWpELFFBQVEsRUFBRSxHQUFHLEdBQUc7UUFFN0Usa0NBQWtDO1FBQ2xDLElBQUksQ0FBQzhDLDJCQUEyQixDQUFDck4sSUFBSTtRQUVyQyxPQUFPRCxPQUFPRyxlQUFlO0lBQy9CO0FBQ0Y7QUFFQSw0Q0FBNEM7QUFDNUMsTUFBTXVOLEtBQUt6UixtQkFBT0EsQ0FBQztBQUNuQixNQUFNMFIsVUFBVXRSLEtBQUtJLElBQUksQ0FBQ0MsUUFBUUMsR0FBRyxJQUFJO0FBQ3pDLElBQUksQ0FBQytRLEdBQUdFLFVBQVUsQ0FBQ0QsVUFBVTtJQUMzQkQsR0FBR0csU0FBUyxDQUFDRixTQUFTO1FBQUVHLFdBQVc7SUFBSztBQUMxQztBQUVBLDRCQUE0QjtBQUM1QixJQUFJQyxhQUFhO0FBRWpCLFNBQVNDO0lBQ1AsSUFBSSxDQUFDRCxZQUFZO1FBQ2ZBLGFBQWEsSUFBSXpSO0lBQ25CO0lBQ0EsT0FBT3lSO0FBQ1Q7QUFFQUUsT0FBT0MsT0FBTyxHQUFHO0lBQUVGO0lBQWExUjtBQUFnQiIsInNvdXJjZXMiOlsid2VicGFjazovL3JlYWRlci1hdXRvLy4vbGliL2RhdGFiYXNlLmpzP2VlYTUiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgRGF0YWJhc2UgPSByZXF1aXJlKCdiZXR0ZXItc3FsaXRlMycpO1xuY29uc3QgYmNyeXB0ID0gcmVxdWlyZSgnYmNyeXB0anMnKTtcbmNvbnN0IHsgdjQ6IHV1aWR2NCB9ID0gcmVxdWlyZSgndXVpZCcpO1xuY29uc3QgcGF0aCA9IHJlcXVpcmUoJ3BhdGgnKTtcblxuY2xhc3MgRGF0YWJhc2VNYW5hZ2VyIHtcbiAgY29uc3RydWN0b3IoKSB7XG4gICAgY29uc3QgZGJQYXRoID0gcGF0aC5qb2luKHByb2Nlc3MuY3dkKCksICdkYXRhJywgJ2FwcC5kYicpO1xuICAgIHRoaXMuZGIgPSBuZXcgRGF0YWJhc2UoZGJQYXRoKTtcbiAgICB0aGlzLmluaXRpYWxpemVUYWJsZXMoKTtcbiAgICB0aGlzLmNyZWF0ZURlZmF1bHRBZG1pbigpO1xuICB9XG5cbiAgaW5pdGlhbGl6ZVRhYmxlcygpIHtcbiAgICAvLyBVc2VycyB0YWJsZVxuICAgIHRoaXMuZGIuZXhlYyhgXG4gICAgICBDUkVBVEUgVEFCTEUgSUYgTk9UIEVYSVNUUyB1c2VycyAoXG4gICAgICAgIGlkIElOVEVHRVIgUFJJTUFSWSBLRVkgQVVUT0lOQ1JFTUVOVCxcbiAgICAgICAgdXNlcm5hbWUgVEVYVCBVTklRVUUgTk9UIE5VTEwsXG4gICAgICAgIHBhc3N3b3JkX2hhc2ggVEVYVCBOT1QgTlVMTCxcbiAgICAgICAgcm9sZSBURVhUIERFRkFVTFQgJ3VzZXInIENIRUNLKHJvbGUgSU4gKCd1c2VyJywgJ2FkbWluJykpLFxuICAgICAgICBsaWNlbnNlX2tleV9pZCBJTlRFR0VSLFxuICAgICAgICBjcmVhdGVkX2F0IERBVEVUSU1FIERFRkFVTFQgQ1VSUkVOVF9USU1FU1RBTVAsXG4gICAgICAgIHVwZGF0ZWRfYXQgREFURVRJTUUgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUCxcbiAgICAgICAgaXNfYWN0aXZlIEJPT0xFQU4gREVGQVVMVCAxLFxuICAgICAgICBsYXN0X2xvZ2luIERBVEVUSU1FLFxuICAgICAgICBGT1JFSUdOIEtFWSAobGljZW5zZV9rZXlfaWQpIFJFRkVSRU5DRVMgbGljZW5zZV9rZXlzIChpZClcbiAgICAgIClcbiAgICBgKTtcblxuICAgIC8vIExpY2Vuc2Uga2V5cyB0YWJsZVxuICAgIHRoaXMuZGIuZXhlYyhgXG4gICAgICBDUkVBVEUgVEFCTEUgSUYgTk9UIEVYSVNUUyBsaWNlbnNlX2tleXMgKFxuICAgICAgICBpZCBJTlRFR0VSIFBSSU1BUlkgS0VZIEFVVE9JTkNSRU1FTlQsXG4gICAgICAgIGtleV9jb2RlIFRFWFQgVU5JUVVFIE5PVCBOVUxMLFxuICAgICAgICBkdXJhdGlvbl9kYXlzIElOVEVHRVIgTk9UIE5VTEwsXG4gICAgICAgIG1heF91c2VzIElOVEVHRVIgTk9UIE5VTEwgREVGQVVMVCAxLFxuICAgICAgICBjdXJyZW50X3VzZXMgSU5URUdFUiBERUZBVUxUIDAsXG4gICAgICAgIGNyZWF0ZWRfYXQgREFURVRJTUUgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUCxcbiAgICAgICAgZXhwaXJlc19hdCBEQVRFVElNRSBOT1QgTlVMTCxcbiAgICAgICAgaXNfYWN0aXZlIEJPT0xFQU4gREVGQVVMVCAxLFxuICAgICAgICBjcmVhdGVkX2J5IElOVEVHRVIsXG4gICAgICAgIGZlYXR1cmVzIFRFWFQgREVGQVVMVCAnW10nLFxuICAgICAgICBGT1JFSUdOIEtFWSAoY3JlYXRlZF9ieSkgUkVGRVJFTkNFUyB1c2VycyAoaWQpXG4gICAgICApXG4gICAgYCk7XG5cbiAgICAvLyBVc2VyIHNlc3Npb25zIHRhYmxlXG4gICAgdGhpcy5kYi5leGVjKGBcbiAgICAgIENSRUFURSBUQUJMRSBJRiBOT1QgRVhJU1RTIHVzZXJfc2Vzc2lvbnMgKFxuICAgICAgICBpZCBJTlRFR0VSIFBSSU1BUlkgS0VZIEFVVE9JTkNSRU1FTlQsXG4gICAgICAgIHVzZXJfaWQgSU5URUdFUiBOT1QgTlVMTCxcbiAgICAgICAgdG9rZW5faGFzaCBURVhUIE5PVCBOVUxMLFxuICAgICAgICBleHBpcmVzX2F0IERBVEVUSU1FIE5PVCBOVUxMLFxuICAgICAgICBjcmVhdGVkX2F0IERBVEVUSU1FIERFRkFVTFQgQ1VSUkVOVF9USU1FU1RBTVAsXG4gICAgICAgIGlwX2FkZHJlc3MgVEVYVCxcbiAgICAgICAgdXNlcl9hZ2VudCBURVhULFxuICAgICAgICBpc19hY3RpdmUgQk9PTEVBTiBERUZBVUxUIDEsXG4gICAgICAgIEZPUkVJR04gS0VZICh1c2VyX2lkKSBSRUZFUkVOQ0VTIHVzZXJzIChpZClcbiAgICAgIClcbiAgICBgKTtcblxuICAgIC8vIEFjdGl2aXR5IGxvZ3MgdGFibGVcbiAgICB0aGlzLmRiLmV4ZWMoYFxuICAgICAgQ1JFQVRFIFRBQkxFIElGIE5PVCBFWElTVFMgYWN0aXZpdHlfbG9ncyAoXG4gICAgICAgIGlkIElOVEVHRVIgUFJJTUFSWSBLRVkgQVVUT0lOQ1JFTUVOVCxcbiAgICAgICAgdXNlcl9pZCBJTlRFR0VSLFxuICAgICAgICBhY3Rpb24gVEVYVCBOT1QgTlVMTCxcbiAgICAgICAgZGV0YWlscyBURVhULFxuICAgICAgICBpcF9hZGRyZXNzIFRFWFQsXG4gICAgICAgIGNyZWF0ZWRfYXQgREFURVRJTUUgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUCxcbiAgICAgICAgRk9SRUlHTiBLRVkgKHVzZXJfaWQpIFJFRkVSRU5DRVMgdXNlcnMgKGlkKVxuICAgICAgKVxuICAgIGApO1xuXG4gICAgLy8gU3lzdGVtIHNldHRpbmdzIHRhYmxlXG4gICAgdGhpcy5kYi5leGVjKGBcbiAgICAgIENSRUFURSBUQUJMRSBJRiBOT1QgRVhJU1RTIHN5c3RlbV9zZXR0aW5ncyAoXG4gICAgICAgIGlkIElOVEVHRVIgUFJJTUFSWSBLRVkgQVVUT0lOQ1JFTUVOVCxcbiAgICAgICAgc2V0dGluZ19rZXkgVEVYVCBVTklRVUUgTk9UIE5VTEwsXG4gICAgICAgIHNldHRpbmdfdmFsdWUgVEVYVCxcbiAgICAgICAgdXBkYXRlZF9hdCBEQVRFVElNRSBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QLFxuICAgICAgICB1cGRhdGVkX2J5IElOVEVHRVIsXG4gICAgICAgIEZPUkVJR04gS0VZICh1cGRhdGVkX2J5KSBSRUZFUkVOQ0VTIHVzZXJzIChpZClcbiAgICAgIClcbiAgICBgKTtcblxuICAgIC8vIEVuY3J5cHRlZCBsb2dpbiBjcmVkZW50aWFscyB0YWJsZVxuICAgIHRoaXMuZGIuZXhlYyhgXG4gICAgICBDUkVBVEUgVEFCTEUgSUYgTk9UIEVYSVNUUyBlbmNyeXB0ZWRfY3JlZGVudGlhbHMgKFxuICAgICAgICBpZCBJTlRFR0VSIFBSSU1BUlkgS0VZIEFVVE9JTkNSRU1FTlQsXG4gICAgICAgIHVzZXJfaWQgSU5URUdFUiBOT1QgTlVMTCxcbiAgICAgICAgbG9naW5fa2V5IFRFWFQgVU5JUVVFIE5PVCBOVUxMLFxuICAgICAgICBsb2dpbl9tZXRob2QgVEVYVCBOT1QgTlVMTCBDSEVDSyhsb2dpbl9tZXRob2QgSU4gKCdub3JtYWwnLCAnbWljcm9zb2Z0JywgJ2dvb2dsZScpKSxcbiAgICAgICAgZW5jcnlwdGVkX3NjaG9vbCBURVhUIE5PVCBOVUxMLFxuICAgICAgICBlbmNyeXB0ZWRfZW1haWwgVEVYVCBOT1QgTlVMTCxcbiAgICAgICAgZW5jcnlwdGVkX3Bhc3N3b3JkIFRFWFQgTk9UIE5VTEwsXG4gICAgICAgIGVuY3J5cHRpb25faXYgVEVYVCBOT1QgTlVMTCxcbiAgICAgICAgY3JlYXRlZF9hdCBEQVRFVElNRSBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QLFxuICAgICAgICB1cGRhdGVkX2F0IERBVEVUSU1FIERFRkFVTFQgQ1VSUkVOVF9USU1FU1RBTVAsXG4gICAgICAgIGlzX2FjdGl2ZSBCT09MRUFOIERFRkFVTFQgMSxcbiAgICAgICAgRk9SRUlHTiBLRVkgKHVzZXJfaWQpIFJFRkVSRU5DRVMgdXNlcnMgKGlkKVxuICAgICAgKVxuICAgIGApO1xuXG4gICAgLy8gTGljZW5zZSBmZWF0dXJlIHNldHRpbmdzIHRhYmxlXG4gICAgdGhpcy5kYi5leGVjKGBcbiAgICAgIENSRUFURSBUQUJMRSBJRiBOT1QgRVhJU1RTIGxpY2Vuc2VfZmVhdHVyZV9zZXR0aW5ncyAoXG4gICAgICAgIGlkIElOVEVHRVIgUFJJTUFSWSBLRVkgQVVUT0lOQ1JFTUVOVCxcbiAgICAgICAgbGljZW5zZV9rZXlfaWQgSU5URUdFUiBOT1QgTlVMTCxcbiAgICAgICAgbWF4X2FjY291bnRzX3Blcl9iYXRjaCBJTlRFR0VSIERFRkFVTFQgMCxcbiAgICAgICAgcHJpb3JpdHlfbGV2ZWwgSU5URUdFUiBERUZBVUxUIDAgQ0hFQ0socHJpb3JpdHlfbGV2ZWwgPj0gMCBBTkQgcHJpb3JpdHlfbGV2ZWwgPD0gMTApLFxuICAgICAgICBzY2hlZHVsaW5nX2FjY2VzcyBCT09MRUFOIERFRkFVTFQgMCxcbiAgICAgICAgbXVsdGlfdXNlcl9hY2Nlc3MgQk9PTEVBTiBERUZBVUxUIDAsXG4gICAgICAgIG1heF9iYXRjaGVzX3Blcl9kYXkgSU5URUdFUiBERUZBVUxUIDEsXG4gICAgICAgIG1heF9zY2hlZHVsZXNfcGVyX3dlZWsgSU5URUdFUiBERUZBVUxUIDEsXG4gICAgICAgIG1heF9tYWluX2pvYnNfcGVyX2RheSBJTlRFR0VSIERFRkFVTFQgNSxcbiAgICAgICAgY3JlYXRlZF9hdCBEQVRFVElNRSBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QLFxuICAgICAgICB1cGRhdGVkX2F0IERBVEVUSU1FIERFRkFVTFQgQ1VSUkVOVF9USU1FU1RBTVAsXG4gICAgICAgIEZPUkVJR04gS0VZIChsaWNlbnNlX2tleV9pZCkgUkVGRVJFTkNFUyBsaWNlbnNlX2tleXMgKGlkKSxcbiAgICAgICAgVU5JUVVFKGxpY2Vuc2Vfa2V5X2lkKVxuICAgICAgKVxuICAgIGApO1xuXG4gICAgLy8gU3lzdGVtIGNvbmZpZ3VyYXRpb24gdGFibGUgZm9yIGFkbWluIHNldHRpbmdzXG4gICAgdGhpcy5kYi5leGVjKGBcbiAgICAgIENSRUFURSBUQUJMRSBJRiBOT1QgRVhJU1RTIHN5c3RlbV9jb25maWcgKFxuICAgICAgICBpZCBJTlRFR0VSIFBSSU1BUlkgS0VZIEFVVE9JTkNSRU1FTlQsXG4gICAgICAgIGNvbmZpZ19rZXkgVEVYVCBVTklRVUUgTk9UIE5VTEwsXG4gICAgICAgIGNvbmZpZ192YWx1ZSBURVhUIE5PVCBOVUxMLFxuICAgICAgICBjb25maWdfdHlwZSBURVhUIERFRkFVTFQgJ3N0cmluZycgQ0hFQ0soY29uZmlnX3R5cGUgSU4gKCdzdHJpbmcnLCAnbnVtYmVyJywgJ2Jvb2xlYW4nLCAnanNvbicpKSxcbiAgICAgICAgZGVzY3JpcHRpb24gVEVYVCxcbiAgICAgICAgY3JlYXRlZF9hdCBEQVRFVElNRSBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QLFxuICAgICAgICB1cGRhdGVkX2F0IERBVEVUSU1FIERFRkFVTFQgQ1VSUkVOVF9USU1FU1RBTVBcbiAgICAgIClcbiAgICBgKTtcblxuICAgIC8vIERlYWQgbGV0dGVyIHF1ZXVlIHRhYmxlXG4gICAgdGhpcy5kYi5leGVjKGBcbiAgICAgIENSRUFURSBUQUJMRSBJRiBOT1QgRVhJU1RTIGRlYWRfbGV0dGVyX3F1ZXVlIChcbiAgICAgICAgaWQgSU5URUdFUiBQUklNQVJZIEtFWSBBVVRPSU5DUkVNRU5ULFxuICAgICAgICBvcmlnaW5hbF9qb2JfaWQgSU5URUdFUixcbiAgICAgICAgam9iX3R5cGUgVEVYVCBOT1QgTlVMTCxcbiAgICAgICAgam9iX2RhdGEgVEVYVCBOT1QgTlVMTCxcbiAgICAgICAgdXNlcl9pZCBJTlRFR0VSIE5PVCBOVUxMLFxuICAgICAgICBmYWlsdXJlX3JlYXNvbiBURVhULFxuICAgICAgICBmYWlsdXJlX2NvdW50IElOVEVHRVIgREVGQVVMVCAxLFxuICAgICAgICBsYXN0X2ZhaWx1cmVfYXQgREFURVRJTUUgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUCxcbiAgICAgICAgY3JlYXRlZF9hdCBEQVRFVElNRSBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QLFxuICAgICAgICBzdGF0dXMgVEVYVCBERUZBVUxUICdmYWlsZWQnIENIRUNLKHN0YXR1cyBJTiAoJ2ZhaWxlZCcsICdpbnZlc3RpZ2F0aW5nJywgJ3Jlc29sdmVkJywgJ2Rpc2NhcmRlZCcpKSxcbiAgICAgICAgRk9SRUlHTiBLRVkgKHVzZXJfaWQpIFJFRkVSRU5DRVMgdXNlcnMgKGlkKSxcbiAgICAgICAgRk9SRUlHTiBLRVkgKG9yaWdpbmFsX2pvYl9pZCkgUkVGRVJFTkNFUyBxdWV1ZV9qb2JzIChpZClcbiAgICAgIClcbiAgICBgKTtcblxuICAgIC8vIFF1ZXVlIGJhdGNoZXMgdGFibGVcbiAgICB0aGlzLmRiLmV4ZWMoYFxuICAgICAgQ1JFQVRFIFRBQkxFIElGIE5PVCBFWElTVFMgcXVldWVfYmF0Y2hlcyAoXG4gICAgICAgIGlkIElOVEVHRVIgUFJJTUFSWSBLRVkgQVVUT0lOQ1JFTUVOVCxcbiAgICAgICAgdXNlcl9pZCBJTlRFR0VSIE5PVCBOVUxMLFxuICAgICAgICBiYXRjaF9uYW1lIFRFWFQsXG4gICAgICAgIGxvZ2luX3R5cGUgVEVYVCBERUZBVUxUICdub3JtYWwnIENIRUNLKGxvZ2luX3R5cGUgSU4gKCdub3JtYWwnLCAnZ29vZ2xlJywgJ21pY3Jvc29mdCcpKSxcbiAgICAgICAgdG90YWxfYWNjb3VudHMgSU5URUdFUiBOT1QgTlVMTCxcbiAgICAgICAgcHJvY2Vzc2VkX2FjY291bnRzIElOVEVHRVIgREVGQVVMVCAwLFxuICAgICAgICBmYWlsZWRfYWNjb3VudHMgSU5URUdFUiBERUZBVUxUIDAsXG4gICAgICAgIHN0YXR1cyBURVhUIERFRkFVTFQgJ3BlbmRpbmcnIENIRUNLKHN0YXR1cyBJTiAoJ3BlbmRpbmcnLCAncHJvY2Vzc2luZycsICdjb21wbGV0ZWQnLCAnZmFpbGVkJywgJ2NhbmNlbGxlZCcpKSxcbiAgICAgICAgcHJpb3JpdHlfbGV2ZWwgSU5URUdFUiBERUZBVUxUIDAsXG4gICAgICAgIHNycF90YXJnZXQgSU5URUdFUiBERUZBVUxUIDEwMCxcbiAgICAgICAgY3JlYXRlZF9hdCBEQVRFVElNRSBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QLFxuICAgICAgICBzdGFydGVkX2F0IERBVEVUSU1FLFxuICAgICAgICBjb21wbGV0ZWRfYXQgREFURVRJTUUsXG4gICAgICAgIHNjaGVkdWxlZF90aW1lIERBVEVUSU1FLFxuICAgICAgICBGT1JFSUdOIEtFWSAodXNlcl9pZCkgUkVGRVJFTkNFUyB1c2VycyAoaWQpXG4gICAgICApXG4gICAgYCk7XG5cbiAgICAvLyBBZGQgc3JwX3RhcmdldCBjb2x1bW4gdG8gcXVldWVfYmF0Y2hlcyBpZiBpdCBkb2Vzbid0IGV4aXN0XG4gICAgdHJ5IHtcbiAgICAgIHRoaXMuZGIuZXhlYyhgQUxURVIgVEFCTEUgcXVldWVfYmF0Y2hlcyBBREQgQ09MVU1OIHNycF90YXJnZXQgSU5URUdFUiBERUZBVUxUIDEwMGApO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAvLyBDb2x1bW4gYWxyZWFkeSBleGlzdHMsIGlnbm9yZSBlcnJvclxuICAgIH1cblxuICAgIC8vIFF1ZXVlIGpvYnMgdGFibGVcbiAgICB0aGlzLmRiLmV4ZWMoYFxuICAgICAgQ1JFQVRFIFRBQkxFIElGIE5PVCBFWElTVFMgcXVldWVfam9icyAoXG4gICAgICAgIGlkIElOVEVHRVIgUFJJTUFSWSBLRVkgQVVUT0lOQ1JFTUVOVCxcbiAgICAgICAgYmF0Y2hfaWQgSU5URUdFUixcbiAgICAgICAgdXNlcl9pZCBJTlRFR0VSIE5PVCBOVUxMLFxuICAgICAgICBqb2JfdHlwZSBURVhUIE5PVCBOVUxMIERFRkFVTFQgJ3NwYXJ4X3JlYWRlcicsXG4gICAgICAgIGpvYl9kYXRhIFRFWFQgTk9UIE5VTEwsXG4gICAgICAgIHN0YXR1cyBURVhUIERFRkFVTFQgJ3F1ZXVlZCcgQ0hFQ0soc3RhdHVzIElOICgncXVldWVkJywgJ3Byb2Nlc3NpbmcnLCAnY29tcGxldGVkJywgJ2ZhaWxlZCcsICdjYW5jZWxsZWQnKSksXG4gICAgICAgIHByaW9yaXR5X2xldmVsIElOVEVHRVIgREVGQVVMVCAwLFxuICAgICAgICBlZmZlY3RpdmVfcHJpb3JpdHkgSU5URUdFUiBERUZBVUxUIDAsXG4gICAgICAgIHNycF90YXJnZXQgSU5URUdFUiBERUZBVUxUIDEwMCxcbiAgICAgICAgc2NoZWR1bGVkX3RpbWUgREFURVRJTUUsXG4gICAgICAgIGNyZWF0ZWRfYXQgREFURVRJTUUgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUCxcbiAgICAgICAgc3RhcnRlZF9hdCBEQVRFVElNRSxcbiAgICAgICAgY29tcGxldGVkX2F0IERBVEVUSU1FLFxuICAgICAgICBlcnJvcl9tZXNzYWdlIFRFWFQsXG4gICAgICAgIHJldHJ5X2NvdW50IElOVEVHRVIgREVGQVVMVCAwLFxuICAgICAgICBtYXhfcmV0cmllcyBJTlRFR0VSIERFRkFVTFQgMyxcbiAgICAgICAgRk9SRUlHTiBLRVkgKGJhdGNoX2lkKSBSRUZFUkVOQ0VTIHF1ZXVlX2JhdGNoZXMgKGlkKSxcbiAgICAgICAgRk9SRUlHTiBLRVkgKHVzZXJfaWQpIFJFRkVSRU5DRVMgdXNlcnMgKGlkKVxuICAgICAgKVxuICAgIGApO1xuXG4gICAgLy8gQWRkIHNycF90YXJnZXQgY29sdW1uIHRvIHF1ZXVlX2pvYnMgaWYgaXQgZG9lc24ndCBleGlzdFxuICAgIHRyeSB7XG4gICAgICB0aGlzLmRiLmV4ZWMoYEFMVEVSIFRBQkxFIHF1ZXVlX2pvYnMgQUREIENPTFVNTiBzcnBfdGFyZ2V0IElOVEVHRVIgREVGQVVMVCAxMDBgKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgLy8gQ29sdW1uIGFscmVhZHkgZXhpc3RzLCBpZ25vcmUgZXJyb3JcbiAgICB9XG5cbiAgICAvLyBRdWV1ZSBzY2hlZHVsZXMgdGFibGUgZm9yIGNvbmZsaWN0IGRldGVjdGlvblxuICAgIHRoaXMuZGIuZXhlYyhgXG4gICAgICBDUkVBVEUgVEFCTEUgSUYgTk9UIEVYSVNUUyBxdWV1ZV9zY2hlZHVsZXMgKFxuICAgICAgICBpZCBJTlRFR0VSIFBSSU1BUlkgS0VZIEFVVE9JTkNSRU1FTlQsXG4gICAgICAgIHVzZXJfaWQgSU5URUdFUiBOT1QgTlVMTCxcbiAgICAgICAgc2NoZWR1bGVkX3RpbWUgREFURVRJTUUgTk9UIE5VTEwsXG4gICAgICAgIGR1cmF0aW9uX21pbnV0ZXMgSU5URUdFUiBERUZBVUxUIDMwLFxuICAgICAgICBzcnBfdGFyZ2V0IElOVEVHRVIgREVGQVVMVCAxMDAsXG4gICAgICAgIGpvYl9pZCBJTlRFR0VSLFxuICAgICAgICBiYXRjaF9pZCBJTlRFR0VSLFxuICAgICAgICBzdGF0dXMgVEVYVCBERUZBVUxUICdzY2hlZHVsZWQnIENIRUNLKHN0YXR1cyBJTiAoJ3NjaGVkdWxlZCcsICdhY3RpdmUnLCAnY29tcGxldGVkJywgJ2NhbmNlbGxlZCcpKSxcbiAgICAgICAgY3JlYXRlZF9hdCBEQVRFVElNRSBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QLFxuICAgICAgICBGT1JFSUdOIEtFWSAodXNlcl9pZCkgUkVGRVJFTkNFUyB1c2VycyAoaWQpLFxuICAgICAgICBGT1JFSUdOIEtFWSAoam9iX2lkKSBSRUZFUkVOQ0VTIHF1ZXVlX2pvYnMgKGlkKSxcbiAgICAgICAgRk9SRUlHTiBLRVkgKGJhdGNoX2lkKSBSRUZFUkVOQ0VTIHF1ZXVlX2JhdGNoZXMgKGlkKVxuICAgICAgKVxuICAgIGApO1xuXG4gICAgLy8gQWRkIHNycF90YXJnZXQgY29sdW1uIGlmIGl0IGRvZXNuJ3QgZXhpc3QgKGZvciBleGlzdGluZyBkYXRhYmFzZXMpXG4gICAgdHJ5IHtcbiAgICAgIHRoaXMuZGIuZXhlYyhgQUxURVIgVEFCTEUgcXVldWVfc2NoZWR1bGVzIEFERCBDT0xVTU4gc3JwX3RhcmdldCBJTlRFR0VSIERFRkFVTFQgMTAwYCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIC8vIENvbHVtbiBhbHJlYWR5IGV4aXN0cywgaWdub3JlIGVycm9yXG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlIGluZGV4ZXMgZm9yIGJldHRlciBwZXJmb3JtYW5jZVxuICAgIHRoaXMuZGIuZXhlYyhgXG4gICAgICBDUkVBVEUgSU5ERVggSUYgTk9UIEVYSVNUUyBpZHhfdXNlcnNfdXNlcm5hbWUgT04gdXNlcnModXNlcm5hbWUpO1xuICAgICAgQ1JFQVRFIElOREVYIElGIE5PVCBFWElTVFMgaWR4X3VzZXJzX2xpY2Vuc2Vfa2V5IE9OIHVzZXJzKGxpY2Vuc2Vfa2V5X2lkKTtcbiAgICAgIENSRUFURSBJTkRFWCBJRiBOT1QgRVhJU1RTIGlkeF9saWNlbnNlX2tleXNfY29kZSBPTiBsaWNlbnNlX2tleXMoa2V5X2NvZGUpO1xuICAgICAgQ1JFQVRFIElOREVYIElGIE5PVCBFWElTVFMgaWR4X2xpY2Vuc2Vfa2V5c19leHBpcmVzIE9OIGxpY2Vuc2Vfa2V5cyhleHBpcmVzX2F0KTtcbiAgICAgIENSRUFURSBJTkRFWCBJRiBOT1QgRVhJU1RTIGlkeF9zZXNzaW9uc190b2tlbiBPTiB1c2VyX3Nlc3Npb25zKHRva2VuX2hhc2gpO1xuICAgICAgQ1JFQVRFIElOREVYIElGIE5PVCBFWElTVFMgaWR4X3Nlc3Npb25zX3VzZXIgT04gdXNlcl9zZXNzaW9ucyh1c2VyX2lkKTtcbiAgICAgIENSRUFURSBJTkRFWCBJRiBOT1QgRVhJU1RTIGlkeF9hY3Rpdml0eV91c2VyIE9OIGFjdGl2aXR5X2xvZ3ModXNlcl9pZCk7XG4gICAgICBDUkVBVEUgSU5ERVggSUYgTk9UIEVYSVNUUyBpZHhfYWN0aXZpdHlfY3JlYXRlZCBPTiBhY3Rpdml0eV9sb2dzKGNyZWF0ZWRfYXQpO1xuICAgICAgQ1JFQVRFIElOREVYIElGIE5PVCBFWElTVFMgaWR4X2VuY3J5cHRlZF9jcmVkZW50aWFsc191c2VyIE9OIGVuY3J5cHRlZF9jcmVkZW50aWFscyh1c2VyX2lkKTtcbiAgICAgIENSRUFURSBJTkRFWCBJRiBOT1QgRVhJU1RTIGlkeF9lbmNyeXB0ZWRfY3JlZGVudGlhbHNfa2V5IE9OIGVuY3J5cHRlZF9jcmVkZW50aWFscyhsb2dpbl9rZXkpO1xuICAgICAgQ1JFQVRFIElOREVYIElGIE5PVCBFWElTVFMgaWR4X2xpY2Vuc2VfZmVhdHVyZXMgT04gbGljZW5zZV9mZWF0dXJlX3NldHRpbmdzKGxpY2Vuc2Vfa2V5X2lkKTtcbiAgICAgIENSRUFURSBJTkRFWCBJRiBOT1QgRVhJU1RTIGlkeF9xdWV1ZV9iYXRjaGVzX3VzZXIgT04gcXVldWVfYmF0Y2hlcyh1c2VyX2lkKTtcbiAgICAgIENSRUFURSBJTkRFWCBJRiBOT1QgRVhJU1RTIGlkeF9xdWV1ZV9iYXRjaGVzX3N0YXR1cyBPTiBxdWV1ZV9iYXRjaGVzKHN0YXR1cyk7XG4gICAgICBDUkVBVEUgSU5ERVggSUYgTk9UIEVYSVNUUyBpZHhfcXVldWVfYmF0Y2hlc19zY2hlZHVsZWQgT04gcXVldWVfYmF0Y2hlcyhzY2hlZHVsZWRfdGltZSk7XG4gICAgICBDUkVBVEUgSU5ERVggSUYgTk9UIEVYSVNUUyBpZHhfcXVldWVfam9ic19iYXRjaCBPTiBxdWV1ZV9qb2JzKGJhdGNoX2lkKTtcbiAgICAgIENSRUFURSBJTkRFWCBJRiBOT1QgRVhJU1RTIGlkeF9xdWV1ZV9qb2JzX3VzZXIgT04gcXVldWVfam9icyh1c2VyX2lkKTtcbiAgICAgIENSRUFURSBJTkRFWCBJRiBOT1QgRVhJU1RTIGlkeF9xdWV1ZV9qb2JzX3N0YXR1cyBPTiBxdWV1ZV9qb2JzKHN0YXR1cyk7XG4gICAgICBDUkVBVEUgSU5ERVggSUYgTk9UIEVYSVNUUyBpZHhfcXVldWVfam9ic19wcmlvcml0eSBPTiBxdWV1ZV9qb2JzKGVmZmVjdGl2ZV9wcmlvcml0eSk7XG4gICAgICBDUkVBVEUgSU5ERVggSUYgTk9UIEVYSVNUUyBpZHhfcXVldWVfam9ic19zY2hlZHVsZWQgT04gcXVldWVfam9icyhzY2hlZHVsZWRfdGltZSk7XG4gICAgICBDUkVBVEUgSU5ERVggSUYgTk9UIEVYSVNUUyBpZHhfcXVldWVfc2NoZWR1bGVzX3VzZXIgT04gcXVldWVfc2NoZWR1bGVzKHVzZXJfaWQpO1xuICAgICAgQ1JFQVRFIElOREVYIElGIE5PVCBFWElTVFMgaWR4X3F1ZXVlX3NjaGVkdWxlc190aW1lIE9OIHF1ZXVlX3NjaGVkdWxlcyhzY2hlZHVsZWRfdGltZSk7XG4gICAgYCk7XG5cbiAgICAvLyBNaWdyYXRpb246IEFkZCBtYXhfYmF0Y2hlc19wZXJfZGF5IGNvbHVtbiBpZiBpdCBkb2Vzbid0IGV4aXN0XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGNvbHVtbnMgPSB0aGlzLmRiLnByZXBhcmUoXCJQUkFHTUEgdGFibGVfaW5mbyhsaWNlbnNlX2ZlYXR1cmVfc2V0dGluZ3MpXCIpLmFsbCgpO1xuICAgICAgY29uc3QgaGFzTWF4QmF0Y2hlc1BlckRheSA9IGNvbHVtbnMuc29tZShjb2wgPT4gY29sLm5hbWUgPT09ICdtYXhfYmF0Y2hlc19wZXJfZGF5Jyk7XG5cbiAgICAgIGlmICghaGFzTWF4QmF0Y2hlc1BlckRheSkge1xuICAgICAgICBjb25zb2xlLmxvZygnQWRkaW5nIG1heF9iYXRjaGVzX3Blcl9kYXkgY29sdW1uIHRvIGxpY2Vuc2VfZmVhdHVyZV9zZXR0aW5ncy4uLicpO1xuICAgICAgICB0aGlzLmRiLmV4ZWMoYEFMVEVSIFRBQkxFIGxpY2Vuc2VfZmVhdHVyZV9zZXR0aW5ncyBBREQgQ09MVU1OIG1heF9iYXRjaGVzX3Blcl9kYXkgSU5URUdFUiBERUZBVUxUIDFgKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignTWlncmF0aW9uIGVycm9yIGZvciBtYXhfYmF0Y2hlc19wZXJfZGF5OicsIGVycm9yKTtcbiAgICB9XG5cbiAgICAvLyBNaWdyYXRpb246IEFkZCBsb2dpbl90eXBlIGNvbHVtbiB0byBxdWV1ZV9iYXRjaGVzIGlmIGl0IGRvZXNuJ3QgZXhpc3RcbiAgICB0cnkge1xuICAgICAgY29uc3QgYmF0Y2hDb2x1bW5zID0gdGhpcy5kYi5wcmVwYXJlKFwiUFJBR01BIHRhYmxlX2luZm8ocXVldWVfYmF0Y2hlcylcIikuYWxsKCk7XG4gICAgICBjb25zdCBoYXNMb2dpblR5cGUgPSBiYXRjaENvbHVtbnMuc29tZShjb2wgPT4gY29sLm5hbWUgPT09ICdsb2dpbl90eXBlJyk7XG5cbiAgICAgIGlmICghaGFzTG9naW5UeXBlKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdBZGRpbmcgbG9naW5fdHlwZSBjb2x1bW4gdG8gcXVldWVfYmF0Y2hlcy4uLicpO1xuICAgICAgICB0aGlzLmRiLmV4ZWMoYEFMVEVSIFRBQkxFIHF1ZXVlX2JhdGNoZXMgQUREIENPTFVNTiBsb2dpbl90eXBlIFRFWFQgREVGQVVMVCAnbm9ybWFsJyBDSEVDSyhsb2dpbl90eXBlIElOICgnbm9ybWFsJywgJ2dvb2dsZScsICdtaWNyb3NvZnQnKSlgKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignTWlncmF0aW9uIGVycm9yIGZvciBsb2dpbl90eXBlOicsIGVycm9yKTtcbiAgICB9XG4gIH1cblxuICBjcmVhdGVEZWZhdWx0QWRtaW4oKSB7XG4gICAgY29uc3QgYWRtaW5FeGlzdHMgPSB0aGlzLmRiLnByZXBhcmUoJ1NFTEVDVCBpZCBGUk9NIHVzZXJzIFdIRVJFIHJvbGUgPSA/IExJTUlUIDEnKS5nZXQoJ2FkbWluJyk7XG5cbiAgICBpZiAoIWFkbWluRXhpc3RzKSB7XG4gICAgICBjb25zdCBoYXNoZWRQYXNzd29yZCA9IGJjcnlwdC5oYXNoU3luYyhwcm9jZXNzLmVudi5ERUZBVUxUX0FETUlOX1BBU1NXT1JELCAxMik7XG4gICAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgICAgSU5TRVJUIElOVE8gdXNlcnMgKHVzZXJuYW1lLCBwYXNzd29yZF9oYXNoLCByb2xlLCBpc19hY3RpdmUpXG4gICAgICAgIFZBTFVFUyAoPywgPywgPywgPylcbiAgICAgIGApO1xuXG4gICAgICBzdG10LnJ1bihwcm9jZXNzLmVudi5ERUZBVUxUX0FETUlOX1VTRVJOQU1FLCBoYXNoZWRQYXNzd29yZCwgJ2FkbWluJywgMSk7XG4gICAgICBjb25zb2xlLmxvZyhgRGVmYXVsdCBhZG1pbiB1c2VyIGNyZWF0ZWQ6ICR7cHJvY2Vzcy5lbnYuREVGQVVMVF9BRE1JTl9VU0VSTkFNRX0vW2hpZGRlbl1gKTtcbiAgICB9XG5cbiAgICAvLyBJbml0aWFsaXplIGRlZmF1bHQgc3lzdGVtIGNvbmZpZ3VyYXRpb25cbiAgICB0aGlzLmluaXRpYWxpemVEZWZhdWx0Q29uZmlnKCk7XG4gIH1cblxuICBpbml0aWFsaXplRGVmYXVsdENvbmZpZygpIHtcbiAgICBjb25zdCBkZWZhdWx0Q29uZmlncyA9IFtcbiAgICAgIHtcbiAgICAgICAga2V5OiAnZGVmYXVsdF9tYXhfbWFpbl9qb2JzX3Blcl9kYXknLFxuICAgICAgICB2YWx1ZTogKHByb2Nlc3MuZW52LkRFRkFVTFRfTUFYX01BSU5fSk9CU19QRVJfREFZIHx8ICc1JyksXG4gICAgICAgIHR5cGU6ICdudW1iZXInLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0RlZmF1bHQgZGFpbHkgbGltaXQgZm9yIG1haW4gYXV0b21hdGlvbiBqb2JzJ1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAga2V5OiAnZGVmYXVsdF9tYXhfYmF0Y2hlc19wZXJfZGF5JyxcbiAgICAgICAgdmFsdWU6IChwcm9jZXNzLmVudi5ERUZBVUxUX01BWF9CQVRDSEVTX1BFUl9EQVkgfHwgJzEnKSxcbiAgICAgICAgdHlwZTogJ251bWJlcicsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnRGVmYXVsdCBkYWlseSBsaW1pdCBmb3IgYmF0Y2ggam9icydcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGtleTogJ2RlZmF1bHRfbWF4X3NjaGVkdWxlc19wZXJfd2VlaycsXG4gICAgICAgIHZhbHVlOiAocHJvY2Vzcy5lbnYuREVGQVVMVF9NQVhfU0NIRURVTEVTX1BFUl9XRUVLIHx8ICcxJyksXG4gICAgICAgIHR5cGU6ICdudW1iZXInLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0RlZmF1bHQgd2Vla2x5IGxpbWl0IGZvciBzY2hlZHVsZWQgam9icydcbiAgICAgIH0sXG4gICAgICB7IGtleTogJ2RlYWRfbGV0dGVyX3F1ZXVlX2VuYWJsZWQnLCB2YWx1ZTogJ3RydWUnLCB0eXBlOiAnYm9vbGVhbicsIGRlc2NyaXB0aW9uOiAnRW5hYmxlIGRlYWQgbGV0dGVyIHF1ZXVlIGZvciBmYWlsZWQgam9icycgfSxcbiAgICAgIHsga2V5OiAnbWF4X3JldHJ5X2F0dGVtcHRzJywgdmFsdWU6ICczJywgdHlwZTogJ251bWJlcicsIGRlc2NyaXB0aW9uOiAnTWF4aW11bSByZXRyeSBhdHRlbXB0cyBiZWZvcmUgbW92aW5nIHRvIGRlYWQgbGV0dGVyIHF1ZXVlJyB9LFxuICAgICAgeyBrZXk6ICdjaXJjdWl0X2JyZWFrZXJfZW5hYmxlZCcsIHZhbHVlOiAndHJ1ZScsIHR5cGU6ICdib29sZWFuJywgZGVzY3JpcHRpb246ICdFbmFibGUgY2lyY3VpdCBicmVha2VyIGZvciBleHRlcm5hbCBBUEkgY2FsbHMnIH1cbiAgICBdO1xuXG4gICAgZm9yIChjb25zdCBjb25maWcgb2YgZGVmYXVsdENvbmZpZ3MpIHtcbiAgICAgIGNvbnN0IGV4aXN0aW5nU3RtdCA9IHRoaXMuZGIucHJlcGFyZSgnU0VMRUNUIGlkIEZST00gc3lzdGVtX2NvbmZpZyBXSEVSRSBjb25maWdfa2V5ID0gPycpO1xuICAgICAgY29uc3QgZXhpc3RpbmcgPSBleGlzdGluZ1N0bXQuZ2V0KGNvbmZpZy5rZXkpO1xuXG4gICAgICBpZiAoIWV4aXN0aW5nKSB7XG4gICAgICAgIGNvbnN0IGluc2VydFN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgICAgIElOU0VSVCBJTlRPIHN5c3RlbV9jb25maWcgKGNvbmZpZ19rZXksIGNvbmZpZ192YWx1ZSwgY29uZmlnX3R5cGUsIGRlc2NyaXB0aW9uKVxuICAgICAgICAgIFZBTFVFUyAoPywgPywgPywgPylcbiAgICAgICAgYCk7XG4gICAgICAgIGluc2VydFN0bXQucnVuKGNvbmZpZy5rZXksIGNvbmZpZy52YWx1ZSwgY29uZmlnLnR5cGUsIGNvbmZpZy5kZXNjcmlwdGlvbik7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLy8gVXNlciBtYW5hZ2VtZW50IG1ldGhvZHNcbiAgY3JlYXRlVXNlcih1c2VybmFtZSwgcGFzc3dvcmQsIGxpY2Vuc2VLZXkpIHtcbiAgICBjb25zdCB0cmFuc2FjdGlvbiA9IHRoaXMuZGIudHJhbnNhY3Rpb24oKCkgPT4ge1xuICAgICAgLy8gVmFsaWRhdGUgbGljZW5zZSBrZXlcbiAgICAgIGNvbnN0IGxpY2Vuc2VTdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgICAgU0VMRUNUIGlkLCBtYXhfdXNlcywgY3VycmVudF91c2VzLCBleHBpcmVzX2F0LCBpc19hY3RpdmUgXG4gICAgICAgIEZST00gbGljZW5zZV9rZXlzIFxuICAgICAgICBXSEVSRSBrZXlfY29kZSA9ID8gQU5EIGlzX2FjdGl2ZSA9IDFcbiAgICAgIGApO1xuICAgICAgY29uc3QgbGljZW5zZSA9IGxpY2Vuc2VTdG10LmdldChsaWNlbnNlS2V5KTtcblxuICAgICAgaWYgKCFsaWNlbnNlKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCBsaWNlbnNlIGtleScpO1xuICAgICAgfVxuXG4gICAgICBpZiAobmV3IERhdGUobGljZW5zZS5leHBpcmVzX2F0KSA8IG5ldyBEYXRlKCkpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdMaWNlbnNlIGtleSBoYXMgZXhwaXJlZCcpO1xuICAgICAgfVxuXG4gICAgICBpZiAobGljZW5zZS5jdXJyZW50X3VzZXMgPj0gbGljZW5zZS5tYXhfdXNlcykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0xpY2Vuc2Uga2V5IGhhcyByZWFjaGVkIG1heGltdW0gdXNlcycpO1xuICAgICAgfVxuXG4gICAgICAvLyBDaGVjayBpZiB1c2VybmFtZSBhbHJlYWR5IGV4aXN0c1xuICAgICAgY29uc3QgdXNlckV4aXN0cyA9IHRoaXMuZGIucHJlcGFyZSgnU0VMRUNUIGlkIEZST00gdXNlcnMgV0hFUkUgdXNlcm5hbWUgPSA/JykuZ2V0KHVzZXJuYW1lKTtcbiAgICAgIGlmICh1c2VyRXhpc3RzKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignVXNlcm5hbWUgYWxyZWFkeSBleGlzdHMnKTtcbiAgICAgIH1cblxuICAgICAgLy8gQ3JlYXRlIHVzZXJcbiAgICAgIGNvbnN0IGhhc2hlZFBhc3N3b3JkID0gYmNyeXB0Lmhhc2hTeW5jKHBhc3N3b3JkLCAxMik7XG4gICAgICBjb25zdCB1c2VyU3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICAgIElOU0VSVCBJTlRPIHVzZXJzICh1c2VybmFtZSwgcGFzc3dvcmRfaGFzaCwgbGljZW5zZV9rZXlfaWQsIHJvbGUpXG4gICAgICAgIFZBTFVFUyAoPywgPywgPywgPylcbiAgICAgIGApO1xuICAgICAgXG4gICAgICBjb25zdCByZXN1bHQgPSB1c2VyU3RtdC5ydW4odXNlcm5hbWUsIGhhc2hlZFBhc3N3b3JkLCBsaWNlbnNlLmlkLCAndXNlcicpO1xuXG4gICAgICAvLyBVcGRhdGUgbGljZW5zZSBrZXkgdXNhZ2VcbiAgICAgIGNvbnN0IHVwZGF0ZUxpY2Vuc2VTdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgICAgVVBEQVRFIGxpY2Vuc2Vfa2V5cyBcbiAgICAgICAgU0VUIGN1cnJlbnRfdXNlcyA9IGN1cnJlbnRfdXNlcyArIDEgXG4gICAgICAgIFdIRVJFIGlkID0gP1xuICAgICAgYCk7XG4gICAgICB1cGRhdGVMaWNlbnNlU3RtdC5ydW4obGljZW5zZS5pZCk7XG5cbiAgICAgIHJldHVybiByZXN1bHQubGFzdEluc2VydFJvd2lkO1xuICAgIH0pO1xuXG4gICAgcmV0dXJuIHRyYW5zYWN0aW9uKCk7XG4gIH1cblxuICBhdXRoZW50aWNhdGVVc2VyKHVzZXJuYW1lLCBwYXNzd29yZCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgU0VMRUNUIHUuKiwgbGsuZXhwaXJlc19hdCBhcyBsaWNlbnNlX2V4cGlyZXMsIGxrLmlzX2FjdGl2ZSBhcyBsaWNlbnNlX2FjdGl2ZVxuICAgICAgRlJPTSB1c2VycyB1XG4gICAgICBMRUZUIEpPSU4gbGljZW5zZV9rZXlzIGxrIE9OIHUubGljZW5zZV9rZXlfaWQgPSBsay5pZFxuICAgICAgV0hFUkUgdS51c2VybmFtZSA9ID8gQU5EIHUuaXNfYWN0aXZlID0gMVxuICAgIGApO1xuICAgIFxuICAgIGNvbnN0IHVzZXIgPSBzdG10LmdldCh1c2VybmFtZSk7XG4gICAgXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgY3JlZGVudGlhbHMnKTtcbiAgICB9XG5cbiAgICBjb25zdCBpc1ZhbGlkUGFzc3dvcmQgPSBiY3J5cHQuY29tcGFyZVN5bmMocGFzc3dvcmQsIHVzZXIucGFzc3dvcmRfaGFzaCk7XG4gICAgaWYgKCFpc1ZhbGlkUGFzc3dvcmQpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCBjcmVkZW50aWFscycpO1xuICAgIH1cblxuICAgIC8vIENoZWNrIGxpY2Vuc2UgdmFsaWRpdHkgZm9yIG5vbi1hZG1pbiB1c2Vyc1xuICAgIGlmICh1c2VyLnJvbGUgIT09ICdhZG1pbicpIHtcbiAgICAgIGlmICghdXNlci5saWNlbnNlX2FjdGl2ZSB8fCBuZXcgRGF0ZSh1c2VyLmxpY2Vuc2VfZXhwaXJlcykgPCBuZXcgRGF0ZSgpKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignTGljZW5zZSBoYXMgZXhwaXJlZCcpO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFVwZGF0ZSBsYXN0IGxvZ2luXG4gICAgY29uc3QgdXBkYXRlU3RtdCA9IHRoaXMuZGIucHJlcGFyZSgnVVBEQVRFIHVzZXJzIFNFVCBsYXN0X2xvZ2luID0gQ1VSUkVOVF9USU1FU1RBTVAgV0hFUkUgaWQgPSA/Jyk7XG4gICAgdXBkYXRlU3RtdC5ydW4odXNlci5pZCk7XG5cbiAgICAvLyBSZW1vdmUgc2Vuc2l0aXZlIGRhdGFcbiAgICBkZWxldGUgdXNlci5wYXNzd29yZF9oYXNoO1xuICAgIHJldHVybiB1c2VyO1xuICB9XG5cbiAgLy8gTGljZW5zZSBrZXkgbWFuYWdlbWVudCBtZXRob2RzXG4gIGNyZWF0ZUxpY2Vuc2VLZXkoZHVyYXRpb25EYXlzLCBtYXhVc2VzID0gMSwgZmVhdHVyZXMgPSBbXSwgY3JlYXRlZEJ5ID0gbnVsbCkge1xuICAgIGNvbnN0IGtleUNvZGUgPSB0aGlzLmdlbmVyYXRlTGljZW5zZUtleSgpO1xuICAgIGNvbnN0IGV4cGlyZXNBdCA9IG5ldyBEYXRlKCk7XG4gICAgZXhwaXJlc0F0LnNldERhdGUoZXhwaXJlc0F0LmdldERhdGUoKSArIGR1cmF0aW9uRGF5cyk7XG5cbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIElOU0VSVCBJTlRPIGxpY2Vuc2Vfa2V5cyAoa2V5X2NvZGUsIGR1cmF0aW9uX2RheXMsIG1heF91c2VzLCBleHBpcmVzX2F0LCBjcmVhdGVkX2J5LCBmZWF0dXJlcylcbiAgICAgIFZBTFVFUyAoPywgPywgPywgPywgPywgPylcbiAgICBgKTtcblxuICAgIGNvbnN0IHJlc3VsdCA9IHN0bXQucnVuKFxuICAgICAga2V5Q29kZSxcbiAgICAgIGR1cmF0aW9uRGF5cyxcbiAgICAgIG1heFVzZXMsXG4gICAgICBleHBpcmVzQXQudG9JU09TdHJpbmcoKSxcbiAgICAgIGNyZWF0ZWRCeSxcbiAgICAgIEpTT04uc3RyaW5naWZ5KGZlYXR1cmVzKVxuICAgICk7XG5cbiAgICByZXR1cm4ge1xuICAgICAgaWQ6IHJlc3VsdC5sYXN0SW5zZXJ0Um93aWQsXG4gICAgICBrZXlDb2RlLFxuICAgICAgZHVyYXRpb25EYXlzLFxuICAgICAgbWF4VXNlcyxcbiAgICAgIGV4cGlyZXNBdDogZXhwaXJlc0F0LnRvSVNPU3RyaW5nKCksXG4gICAgICBmZWF0dXJlc1xuICAgIH07XG4gIH1cblxuICBnZW5lcmF0ZUxpY2Vuc2VLZXkoKSB7XG4gICAgY29uc3Qgc2VnbWVudHMgPSBbXTtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IDQ7IGkrKykge1xuICAgICAgc2VnbWVudHMucHVzaCh1dWlkdjQoKS5yZXBsYWNlKC8tL2csICcnKS5zdWJzdHJpbmcoMCwgOCkudG9VcHBlckNhc2UoKSk7XG4gICAgfVxuICAgIHJldHVybiBgU1JYLSR7c2VnbWVudHMuam9pbignLScpfWA7XG4gIH1cblxuICBnZXRMaWNlbnNlS2V5cyhsaW1pdCA9IDUwLCBvZmZzZXQgPSAwKSB7XG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1QgXG4gICAgICAgIGxrLiosXG4gICAgICAgIHUudXNlcm5hbWUgYXMgY3JlYXRlZF9ieV91c2VybmFtZSxcbiAgICAgICAgQ09VTlQodXNlcnMuaWQpIGFzIHVzZXJzX2NvdW50XG4gICAgICBGUk9NIGxpY2Vuc2Vfa2V5cyBsa1xuICAgICAgTEVGVCBKT0lOIHVzZXJzIHUgT04gbGsuY3JlYXRlZF9ieSA9IHUuaWRcbiAgICAgIExFRlQgSk9JTiB1c2VycyBPTiB1c2Vycy5saWNlbnNlX2tleV9pZCA9IGxrLmlkXG4gICAgICBHUk9VUCBCWSBsay5pZFxuICAgICAgT1JERVIgQlkgbGsuY3JlYXRlZF9hdCBERVNDXG4gICAgICBMSU1JVCA/IE9GRlNFVCA/XG4gICAgYCk7XG5cbiAgICByZXR1cm4gc3RtdC5hbGwobGltaXQsIG9mZnNldCk7XG4gIH1cblxuICBkZWFjdGl2YXRlTGljZW5zZUtleShrZXlJZCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoJ1VQREFURSBsaWNlbnNlX2tleXMgU0VUIGlzX2FjdGl2ZSA9IDAgV0hFUkUgaWQgPSA/Jyk7XG4gICAgcmV0dXJuIHN0bXQucnVuKGtleUlkKTtcbiAgfVxuXG4gIC8vIEdldCBkZXRhaWxlZCBsaWNlbnNlIGluZm9ybWF0aW9uIGZvciBhIHVzZXJcbiAgZ2V0VXNlckxpY2Vuc2VTdGF0dXModXNlcklkKSB7XG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1QgXG4gICAgICAgIHUuaWQgYXMgdXNlcl9pZCxcbiAgICAgICAgdS51c2VybmFtZSxcbiAgICAgICAgbGsuaWQgYXMgbGljZW5zZV9pZCxcbiAgICAgICAgbGsua2V5X2NvZGUsXG4gICAgICAgIGxrLm1heF91c2VzLFxuICAgICAgICBsay5jdXJyZW50X3VzZXMsXG4gICAgICAgIGxrLmV4cGlyZXNfYXQsXG4gICAgICAgIGxrLmlzX2FjdGl2ZSxcbiAgICAgICAgQ0FTRSBcbiAgICAgICAgICBXSEVOIGxrLmV4cGlyZXNfYXQgPD0gZGF0ZXRpbWUoJ25vdycpIFRIRU4gJ2V4cGlyZWQnXG4gICAgICAgICAgV0hFTiBsay5jdXJyZW50X3VzZXMgPj0gbGsubWF4X3VzZXMgVEhFTiAnbWF4ZWRfb3V0J1xuICAgICAgICAgIFdIRU4gbGsuaXNfYWN0aXZlID0gMCBUSEVOICdpbmFjdGl2ZSdcbiAgICAgICAgICBFTFNFICd2YWxpZCdcbiAgICAgICAgRU5EIGFzIGxpY2Vuc2Vfc3RhdHVzXG4gICAgICBGUk9NIHVzZXJzIHVcbiAgICAgIExFRlQgSk9JTiBsaWNlbnNlX2tleXMgbGsgT04gdS5saWNlbnNlX2tleV9pZCA9IGxrLmlkXG4gICAgICBXSEVSRSB1LmlkID0gP1xuICAgIGApO1xuICAgIFxuICAgIHJldHVybiBzdG10LmdldCh1c2VySWQpO1xuICB9XG5cbiAgLy8gVmFsaWRhdGUgYSBsaWNlbnNlIGtleSBmb3IgcmVuZXdhbCAoY2hlY2sgaWYgaXQncyB2YWxpZCBhbmQgaGFzIGF2YWlsYWJsZSB1c2VzKVxuICB2YWxpZGF0ZUxpY2Vuc2VGb3JSZW5ld2FsKGxpY2Vuc2VLZXkpIHtcbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIFNFTEVDVCBcbiAgICAgICAgaWQsXG4gICAgICAgIGtleV9jb2RlLFxuICAgICAgICBtYXhfdXNlcyxcbiAgICAgICAgY3VycmVudF91c2VzLFxuICAgICAgICBleHBpcmVzX2F0LFxuICAgICAgICBpc19hY3RpdmUsXG4gICAgICAgIENBU0UgXG4gICAgICAgICAgV0hFTiBleHBpcmVzX2F0IDw9IGRhdGV0aW1lKCdub3cnKSBUSEVOICdleHBpcmVkJ1xuICAgICAgICAgIFdIRU4gY3VycmVudF91c2VzID49IG1heF91c2VzIFRIRU4gJ21heGVkX291dCdcbiAgICAgICAgICBXSEVOIGlzX2FjdGl2ZSA9IDAgVEhFTiAnaW5hY3RpdmUnXG4gICAgICAgICAgRUxTRSAndmFsaWQnXG4gICAgICAgIEVORCBhcyBzdGF0dXNcbiAgICAgIEZST00gbGljZW5zZV9rZXlzIFxuICAgICAgV0hFUkUga2V5X2NvZGUgPSA/XG4gICAgYCk7XG4gICAgXG4gICAgY29uc3QgbGljZW5zZSA9IHN0bXQuZ2V0KGxpY2Vuc2VLZXkpO1xuICAgIFxuICAgIGlmICghbGljZW5zZSkge1xuICAgICAgcmV0dXJuIHsgdmFsaWQ6IGZhbHNlLCBlcnJvcjogJ0xpY2Vuc2Uga2V5IG5vdCBmb3VuZCcgfTtcbiAgICB9XG4gICAgXG4gICAgaWYgKGxpY2Vuc2Uuc3RhdHVzICE9PSAndmFsaWQnKSB7XG4gICAgICBsZXQgZXJyb3JNZXNzYWdlID0gJ0xpY2Vuc2Uga2V5IGlzIG5vdCB2YWxpZCc7XG4gICAgICBzd2l0Y2ggKGxpY2Vuc2Uuc3RhdHVzKSB7XG4gICAgICAgIGNhc2UgJ2V4cGlyZWQnOlxuICAgICAgICAgIGVycm9yTWVzc2FnZSA9ICdMaWNlbnNlIGtleSBoYXMgZXhwaXJlZCc7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ21heGVkX291dCc6XG4gICAgICAgICAgZXJyb3JNZXNzYWdlID0gJ0xpY2Vuc2Uga2V5IGhhcyByZWFjaGVkIG1heGltdW0gdXNlcyc7XG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIGNhc2UgJ2luYWN0aXZlJzpcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSAnTGljZW5zZSBrZXkgaXMgaW5hY3RpdmUnO1xuICAgICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHsgdmFsaWQ6IGZhbHNlLCBlcnJvcjogZXJyb3JNZXNzYWdlIH07XG4gICAgfVxuICAgIFxuICAgIHJldHVybiB7IHZhbGlkOiB0cnVlLCBsaWNlbnNlIH07XG4gIH1cblxuICAvLyBSZW5ldyB1c2VyJ3MgbGljZW5zZSB3aXRoIGEgbmV3IGxpY2Vuc2Uga2V5XG4gIHJlbmV3VXNlckxpY2Vuc2UodXNlcklkLCBuZXdMaWNlbnNlS2V5KSB7XG4gICAgY29uc3QgdHJhbnNhY3Rpb24gPSB0aGlzLmRiLnRyYW5zYWN0aW9uKCgpID0+IHtcbiAgICAgIC8vIEZpcnN0IHZhbGlkYXRlIHRoZSBuZXcgbGljZW5zZSBrZXlcbiAgICAgIGNvbnN0IHZhbGlkYXRpb24gPSB0aGlzLnZhbGlkYXRlTGljZW5zZUZvclJlbmV3YWwobmV3TGljZW5zZUtleSk7XG4gICAgICBpZiAoIXZhbGlkYXRpb24udmFsaWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKHZhbGlkYXRpb24uZXJyb3IpO1xuICAgICAgfVxuICAgICAgXG4gICAgICBjb25zdCBuZXdMaWNlbnNlID0gdmFsaWRhdGlvbi5saWNlbnNlO1xuICAgICAgXG4gICAgICAvLyBVcGRhdGUgdXNlcidzIGxpY2Vuc2Vfa2V5X2lkIHRvIHRoZSBuZXcgbGljZW5zZVxuICAgICAgY29uc3QgdXBkYXRlVXNlclN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgICBVUERBVEUgdXNlcnMgXG4gICAgICAgIFNFVCBsaWNlbnNlX2tleV9pZCA9ID8sIHVwZGF0ZWRfYXQgPSBDVVJSRU5UX1RJTUVTVEFNUCBcbiAgICAgICAgV0hFUkUgaWQgPSA/XG4gICAgICBgKTtcbiAgICAgIHVwZGF0ZVVzZXJTdG10LnJ1bihuZXdMaWNlbnNlLmlkLCB1c2VySWQpO1xuICAgICAgXG4gICAgICAvLyBJbmNyZW1lbnQgdGhlIG5ldyBsaWNlbnNlJ3MgY3VycmVudF91c2VzXG4gICAgICBjb25zdCB1cGRhdGVMaWNlbnNlU3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICAgIFVQREFURSBsaWNlbnNlX2tleXMgXG4gICAgICAgIFNFVCBjdXJyZW50X3VzZXMgPSBjdXJyZW50X3VzZXMgKyAxIFxuICAgICAgICBXSEVSRSBpZCA9ID9cbiAgICAgIGApO1xuICAgICAgdXBkYXRlTGljZW5zZVN0bXQucnVuKG5ld0xpY2Vuc2UuaWQpO1xuICAgICAgXG4gICAgICAvLyBMb2cgdGhlIHJlbmV3YWwgYWN0aXZpdHlcbiAgICAgIHRoaXMubG9nQWN0aXZpdHkodXNlcklkLCAnTElDRU5TRV9SRU5FV0VEJywgYExpY2Vuc2UgcmVuZXdlZCB3aXRoIGtleTogJHtuZXdMaWNlbnNlS2V5fWApO1xuICAgICAgXG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICBuZXdMaWNlbnNlSWQ6IG5ld0xpY2Vuc2UuaWQsXG4gICAgICAgIG5ld0xpY2Vuc2VLZXk6IG5ld0xpY2Vuc2VLZXksXG4gICAgICAgIGV4cGlyZXNBdDogbmV3TGljZW5zZS5leHBpcmVzX2F0LFxuICAgICAgICBtYXhVc2VzOiBuZXdMaWNlbnNlLm1heF91c2VzLFxuICAgICAgICBjdXJyZW50VXNlczogbmV3TGljZW5zZS5jdXJyZW50X3VzZXMgKyAxXG4gICAgICB9O1xuICAgIH0pO1xuICAgIFxuICAgIHJldHVybiB0cmFuc2FjdGlvbigpO1xuICB9XG5cbiAgLy8gU2Vzc2lvbiBtYW5hZ2VtZW50XG4gIGNyZWF0ZVNlc3Npb24odXNlcklkLCB0b2tlbkhhc2gsIGV4cGlyZXNBdCwgaXBBZGRyZXNzID0gbnVsbCwgdXNlckFnZW50ID0gbnVsbCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgSU5TRVJUIElOVE8gdXNlcl9zZXNzaW9ucyAodXNlcl9pZCwgdG9rZW5faGFzaCwgZXhwaXJlc19hdCwgaXBfYWRkcmVzcywgdXNlcl9hZ2VudClcbiAgICAgIFZBTFVFUyAoPywgPywgPywgPywgPylcbiAgICBgKTtcblxuICAgIHJldHVybiBzdG10LnJ1bih1c2VySWQsIHRva2VuSGFzaCwgZXhwaXJlc0F0LCBpcEFkZHJlc3MsIHVzZXJBZ2VudCk7XG4gIH1cblxuICB2YWxpZGF0ZVNlc3Npb24odG9rZW5IYXNoKSB7XG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1Qgcy4qLCB1LnVzZXJuYW1lLCB1LnJvbGUsIHUuaXNfYWN0aXZlIGFzIHVzZXJfYWN0aXZlXG4gICAgICBGUk9NIHVzZXJfc2Vzc2lvbnMgc1xuICAgICAgSk9JTiB1c2VycyB1IE9OIHMudXNlcl9pZCA9IHUuaWRcbiAgICAgIFdIRVJFIHMudG9rZW5faGFzaCA9ID8gQU5EIHMuaXNfYWN0aXZlID0gMSBBTkQgcy5leHBpcmVzX2F0ID4gZGF0ZXRpbWUoJ25vdycpXG4gICAgYCk7XG5cbiAgICByZXR1cm4gc3RtdC5nZXQodG9rZW5IYXNoKTtcbiAgfVxuXG4gIGludmFsaWRhdGVTZXNzaW9uKHRva2VuSGFzaCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoJ1VQREFURSB1c2VyX3Nlc3Npb25zIFNFVCBpc19hY3RpdmUgPSAwIFdIRVJFIHRva2VuX2hhc2ggPSA/Jyk7XG4gICAgcmV0dXJuIHN0bXQucnVuKHRva2VuSGFzaCk7XG4gIH1cblxuICBpbnZhbGlkYXRlQWxsVXNlclNlc3Npb25zKHVzZXJJZCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoJ1VQREFURSB1c2VyX3Nlc3Npb25zIFNFVCBpc19hY3RpdmUgPSAwIFdIRVJFIHVzZXJfaWQgPSA/Jyk7XG4gICAgcmV0dXJuIHN0bXQucnVuKHVzZXJJZCk7XG4gIH1cblxuICAvLyBBY3Rpdml0eSBsb2dnaW5nXG4gIGxvZ0FjdGl2aXR5KHVzZXJJZCwgYWN0aW9uLCBkZXRhaWxzID0gbnVsbCwgaXBBZGRyZXNzID0gbnVsbCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgSU5TRVJUIElOVE8gYWN0aXZpdHlfbG9ncyAodXNlcl9pZCwgYWN0aW9uLCBkZXRhaWxzLCBpcF9hZGRyZXNzKVxuICAgICAgVkFMVUVTICg/LCA/LCA/LCA/KVxuICAgIGApO1xuXG4gICAgcmV0dXJuIHN0bXQucnVuKHVzZXJJZCwgYWN0aW9uLCBkZXRhaWxzLCBpcEFkZHJlc3MpO1xuICB9XG5cbiAgLy8gQ2xlYW51cCBleHBpcmVkIGtleXMgb2xkZXIgdGhhbiAzMCBkYXlzXG4gIGNsZWFudXBFeHBpcmVkS2V5cygpIHtcbiAgICB0cnkge1xuICAgICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICAgIERFTEVURSBGUk9NIGxpY2Vuc2Vfa2V5c1xuICAgICAgICBXSEVSRSBkYXRldGltZShleHBpcmVzX2F0KSA8IGRhdGV0aW1lKCdub3cnLCAnLTMwIGRheXMnKVxuICAgICAgYCk7XG4gICAgICBjb25zdCByZXN1bHQgPSBzdG10LnJ1bigpO1xuXG4gICAgICBpZiAocmVzdWx0LmNoYW5nZXMgPiAwKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKGDwn6e5IENsZWFuZWQgdXAgJHtyZXN1bHQuY2hhbmdlc30gZXhwaXJlZCBsaWNlbnNlIGtleXMgb2xkZXIgdGhhbiAzMCBkYXlzYCk7XG4gICAgICAgIHRoaXMubG9nQWN0aXZpdHkobnVsbCwgJ1NZU1RFTV9DTEVBTlVQJywgYFJlbW92ZWQgJHtyZXN1bHQuY2hhbmdlc30gZXhwaXJlZCBrZXlzIG9sZGVyIHRoYW4gMzAgZGF5c2ApO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gcmVzdWx0LmNoYW5nZXM7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNsZWFuaW5nIHVwIGV4cGlyZWQga2V5czonLCBlcnJvcik7XG4gICAgICByZXR1cm4gMDtcbiAgICB9XG4gIH1cblxuICBnZXRBY3Rpdml0eUxvZ3ModXNlcklkID0gbnVsbCwgbGltaXQgPSAxMDAsIG9mZnNldCA9IDApIHtcbiAgICBsZXQgcXVlcnkgPSBgXG4gICAgICBTRUxFQ1QgXG4gICAgICAgIGFsLiosXG4gICAgICAgIHUudXNlcm5hbWVcbiAgICAgIEZST00gYWN0aXZpdHlfbG9ncyBhbFxuICAgICAgTEVGVCBKT0lOIHVzZXJzIHUgT04gYWwudXNlcl9pZCA9IHUuaWRcbiAgICBgO1xuICAgIFxuICAgIGNvbnN0IHBhcmFtcyA9IFtdO1xuICAgIFxuICAgIGlmICh1c2VySWQpIHtcbiAgICAgIHF1ZXJ5ICs9ICcgV0hFUkUgYWwudXNlcl9pZCA9ID8nO1xuICAgICAgcGFyYW1zLnB1c2godXNlcklkKTtcbiAgICB9XG4gICAgXG4gICAgcXVlcnkgKz0gJyBPUkRFUiBCWSBhbC5jcmVhdGVkX2F0IERFU0MgTElNSVQgPyBPRkZTRVQgPyc7XG4gICAgcGFyYW1zLnB1c2gobGltaXQsIG9mZnNldCk7XG5cbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKHF1ZXJ5KTtcbiAgICByZXR1cm4gc3RtdC5hbGwoLi4ucGFyYW1zKTtcbiAgfVxuXG4gIC8vIEFuYWx5dGljcyBtZXRob2RzXG4gIGdldFN5c3RlbVN0YXRzKCkge1xuICAgIGNvbnN0IHN0YXRzID0ge307XG5cbiAgICAvLyBUb3RhbCB1c2Vyc1xuICAgIHN0YXRzLnRvdGFsVXNlcnMgPSB0aGlzLmRiLnByZXBhcmUoJ1NFTEVDVCBDT1VOVCgqKSBhcyBjb3VudCBGUk9NIHVzZXJzIFdIRVJFIHJvbGUgPSBcXCd1c2VyXFwnJykuZ2V0KCkuY291bnQ7XG4gICAgXG4gICAgLy8gQWN0aXZlIHVzZXJzIChsb2dnZWQgaW4gd2l0aGluIGxhc3QgMzAgZGF5cylcbiAgICBzdGF0cy5hY3RpdmVVc2VycyA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1QgQ09VTlQoKikgYXMgY291bnQgRlJPTSB1c2VycyBcbiAgICAgIFdIRVJFIHJvbGUgPSAndXNlcicgQU5EIGxhc3RfbG9naW4gPiBkYXRldGltZSgnbm93JywgJy0zMCBkYXlzJylcbiAgICBgKS5nZXQoKS5jb3VudDtcblxuICAgIC8vIFRvdGFsIGxpY2Vuc2Uga2V5c1xuICAgIHN0YXRzLnRvdGFsTGljZW5zZUtleXMgPSB0aGlzLmRiLnByZXBhcmUoJ1NFTEVDVCBDT1VOVCgqKSBhcyBjb3VudCBGUk9NIGxpY2Vuc2Vfa2V5cycpLmdldCgpLmNvdW50O1xuICAgIFxuICAgIC8vIEFjdGl2ZSBsaWNlbnNlIGtleXNcbiAgICBzdGF0cy5hY3RpdmVMaWNlbnNlS2V5cyA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1QgQ09VTlQoKikgYXMgY291bnQgRlJPTSBsaWNlbnNlX2tleXMgXG4gICAgICBXSEVSRSBpc19hY3RpdmUgPSAxIEFORCBleHBpcmVzX2F0ID4gZGF0ZXRpbWUoJ25vdycpXG4gICAgYCkuZ2V0KCkuY291bnQ7XG5cbiAgICAvLyBFeHBpcmVkIGxpY2Vuc2Uga2V5c1xuICAgIHN0YXRzLmV4cGlyZWRMaWNlbnNlS2V5cyA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1QgQ09VTlQoKikgYXMgY291bnQgRlJPTSBsaWNlbnNlX2tleXMgXG4gICAgICBXSEVSRSBleHBpcmVzX2F0IDw9IGRhdGV0aW1lKCdub3cnKVxuICAgIGApLmdldCgpLmNvdW50O1xuXG4gICAgLy8gUmVjZW50IGFjdGl2aXR5IChsYXN0IDI0IGhvdXJzKVxuICAgIHN0YXRzLnJlY2VudEFjdGl2aXR5ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIFNFTEVDVCBDT1VOVCgqKSBhcyBjb3VudCBGUk9NIGFjdGl2aXR5X2xvZ3MgXG4gICAgICBXSEVSRSBjcmVhdGVkX2F0ID4gZGF0ZXRpbWUoJ25vdycsICctMSBkYXknKVxuICAgIGApLmdldCgpLmNvdW50O1xuXG4gICAgcmV0dXJuIHN0YXRzO1xuICB9XG5cbiAgLy8gVXNlciBtYW5hZ2VtZW50IGZvciBhZG1pblxuICBnZXRVc2VycyhsaW1pdCA9IDUwLCBvZmZzZXQgPSAwKSB7XG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1QgXG4gICAgICAgIHUuaWQsXG4gICAgICAgIHUudXNlcm5hbWUsXG4gICAgICAgIHUucm9sZSxcbiAgICAgICAgdS5jcmVhdGVkX2F0LFxuICAgICAgICB1Lmxhc3RfbG9naW4sXG4gICAgICAgIHUuaXNfYWN0aXZlLFxuICAgICAgICBsay5rZXlfY29kZSxcbiAgICAgICAgbGsuZXhwaXJlc19hdCBhcyBsaWNlbnNlX2V4cGlyZXNcbiAgICAgIEZST00gdXNlcnMgdVxuICAgICAgTEVGVCBKT0lOIGxpY2Vuc2Vfa2V5cyBsayBPTiB1LmxpY2Vuc2Vfa2V5X2lkID0gbGsuaWRcbiAgICAgIE9SREVSIEJZIHUuY3JlYXRlZF9hdCBERVNDXG4gICAgICBMSU1JVCA/IE9GRlNFVCA/XG4gICAgYCk7XG5cbiAgICByZXR1cm4gc3RtdC5hbGwobGltaXQsIG9mZnNldCk7XG4gIH1cblxuICB0b2dnbGVVc2VyU3RhdHVzKHVzZXJJZCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoJ1VQREFURSB1c2VycyBTRVQgaXNfYWN0aXZlID0gTk9UIGlzX2FjdGl2ZSBXSEVSRSBpZCA9ID8nKTtcbiAgICByZXR1cm4gc3RtdC5ydW4odXNlcklkKTtcbiAgfVxuXG4gIC8vIENsZWFudXAgbWV0aG9kc1xuICBjbGVhbnVwRXhwaXJlZFNlc3Npb25zKCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoJ0RFTEVURSBGUk9NIHVzZXJfc2Vzc2lvbnMgV0hFUkUgZXhwaXJlc19hdCA8PSBkYXRldGltZShcIm5vd1wiKScpO1xuICAgIHJldHVybiBzdG10LnJ1bigpO1xuICB9XG5cbiAgY2xlYW51cE9sZExvZ3MoZGF5c1RvS2VlcCA9IDkwKSB7XG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBERUxFVEUgRlJPTSBhY3Rpdml0eV9sb2dzIFxuICAgICAgV0hFUkUgY3JlYXRlZF9hdCA8PSBkYXRldGltZSgnbm93JywgJy0ke2RheXNUb0tlZXB9IGRheXMnKVxuICAgIGApO1xuICAgIHJldHVybiBzdG10LnJ1bigpO1xuICB9XG5cbiAgLy8gRW5jcnlwdGVkIGNyZWRlbnRpYWxzIG1ldGhvZHNcbiAgc2F2ZUVuY3J5cHRlZENyZWRlbnRpYWxzKHVzZXJJZCwgbG9naW5NZXRob2QsIHNjaG9vbCwgZW1haWwsIHBhc3N3b3JkKSB7XG4gICAgY29uc3QgY3J5cHRvID0gcmVxdWlyZSgnY3J5cHRvJyk7XG4gICAgXG4gICAgLy8gQ2hlY2sgZm9yIHJlcXVpcmVkIGVudmlyb25tZW50IHZhcmlhYmxlXG4gICAgY29uc3Qgc2VjcmV0ID0gcHJvY2Vzcy5lbnYuQ1JFREVOVElBTF9FTkNSWVBUSU9OX1NFQ1JFVDtcbiAgICBpZiAoIXNlY3JldCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdDUkVERU5USUFMX0VOQ1JZUFRJT05fU0VDUkVUIGVudmlyb25tZW50IHZhcmlhYmxlIGlzIHJlcXVpcmVkJyk7XG4gICAgfVxuICAgIFxuICAgIC8vIEdlbmVyYXRlIGEgdW5pcXVlIGxvZ2luIGtleVxuICAgIGNvbnN0IGxvZ2luS2V5ID0gJ1NMSy0nICsgY3J5cHRvLnJhbmRvbUJ5dGVzKDgpLnRvU3RyaW5nKCdoZXgnKS50b1VwcGVyQ2FzZSgpO1xuICAgIFxuICAgIC8vIENyZWF0ZSBlbmNyeXB0aW9uIElWXG4gICAgY29uc3QgaXYgPSBjcnlwdG8ucmFuZG9tQnl0ZXMoMTYpO1xuICAgIFxuICAgIC8vIERlcml2ZSBrZXkgdXNpbmcgc3RhYmxlIHNlY3JldCArIHVzZXJJZCBhcyBzYWx0IGZvciB1c2VyIGlzb2xhdGlvblxuICAgIGNvbnN0IGtleSA9IGNyeXB0by5zY3J5cHRTeW5jKHNlY3JldCwgU3RyaW5nKHVzZXJJZCksIDMyKTtcbiAgICBcbiAgICAvLyBFbmNyeXB0IHNjaG9vbCwgZW1haWwgYW5kIHBhc3N3b3JkXG4gICAgY29uc3QgY2lwaGVyMSA9IGNyeXB0by5jcmVhdGVDaXBoZXJpdignYWVzLTI1Ni1jYmMnLCBrZXksIGl2KTtcbiAgICBjb25zdCBlbmNyeXB0ZWRTY2hvb2wgPSBjaXBoZXIxLnVwZGF0ZShzY2hvb2wsICd1dGY4JywgJ2hleCcpICsgY2lwaGVyMS5maW5hbCgnaGV4Jyk7XG4gICAgXG4gICAgY29uc3QgY2lwaGVyMiA9IGNyeXB0by5jcmVhdGVDaXBoZXJpdignYWVzLTI1Ni1jYmMnLCBrZXksIGl2KTtcbiAgICBjb25zdCBlbmNyeXB0ZWRFbWFpbCA9IGNpcGhlcjIudXBkYXRlKGVtYWlsLCAndXRmOCcsICdoZXgnKSArIGNpcGhlcjIuZmluYWwoJ2hleCcpO1xuICAgIFxuICAgIGNvbnN0IGNpcGhlcjMgPSBjcnlwdG8uY3JlYXRlQ2lwaGVyaXYoJ2Flcy0yNTYtY2JjJywga2V5LCBpdik7XG4gICAgY29uc3QgZW5jcnlwdGVkUGFzc3dvcmQgPSBjaXBoZXIzLnVwZGF0ZShwYXNzd29yZCwgJ3V0ZjgnLCAnaGV4JykgKyBjaXBoZXIzLmZpbmFsKCdoZXgnKTtcbiAgICBcbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIElOU0VSVCBJTlRPIGVuY3J5cHRlZF9jcmVkZW50aWFscyAodXNlcl9pZCwgbG9naW5fa2V5LCBsb2dpbl9tZXRob2QsIGVuY3J5cHRlZF9zY2hvb2wsIGVuY3J5cHRlZF9lbWFpbCwgZW5jcnlwdGVkX3Bhc3N3b3JkLCBlbmNyeXB0aW9uX2l2KVxuICAgICAgVkFMVUVTICg/LCA/LCA/LCA/LCA/LCA/LCA/KVxuICAgIGApO1xuICAgIFxuICAgIHN0bXQucnVuKHVzZXJJZCwgbG9naW5LZXksIGxvZ2luTWV0aG9kLCBlbmNyeXB0ZWRTY2hvb2wsIGVuY3J5cHRlZEVtYWlsLCBlbmNyeXB0ZWRQYXNzd29yZCwgaXYudG9TdHJpbmcoJ2hleCcpKTtcbiAgICBcbiAgICByZXR1cm4gbG9naW5LZXk7XG4gIH1cblxuICBnZXRFbmNyeXB0ZWRDcmVkZW50aWFscyhsb2dpbktleSkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgU0VMRUNUICogRlJPTSBlbmNyeXB0ZWRfY3JlZGVudGlhbHMgXG4gICAgICBXSEVSRSBsb2dpbl9rZXkgPSA/IEFORCBpc19hY3RpdmUgPSAxXG4gICAgYCk7XG4gICAgXG4gICAgY29uc3QgcmVzdWx0ID0gc3RtdC5nZXQobG9naW5LZXkpO1xuICAgIGlmICghcmVzdWx0KSByZXR1cm4gbnVsbDtcbiAgICBcbiAgICBjb25zdCBjcnlwdG8gPSByZXF1aXJlKCdjcnlwdG8nKTtcbiAgICBcbiAgICB0cnkge1xuICAgICAgLy8gQ2hlY2sgZm9yIHJlcXVpcmVkIGVudmlyb25tZW50IHZhcmlhYmxlXG4gICAgICBjb25zdCBzZWNyZXQgPSBwcm9jZXNzLmVudi5DUkVERU5USUFMX0VOQ1JZUFRJT05fU0VDUkVUO1xuICAgICAgaWYgKCFzZWNyZXQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdDUkVERU5USUFMX0VOQ1JZUFRJT05fU0VDUkVUIGVudmlyb25tZW50IHZhcmlhYmxlIGlzIHJlcXVpcmVkJyk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIERlcml2ZSBrZXkgdXNpbmcgc3RhYmxlIHNlY3JldCArIHVzZXJJZCBhcyBzYWx0IChzYW1lIGFzIGR1cmluZyBlbmNyeXB0aW9uKVxuICAgICAgY29uc3Qga2V5ID0gY3J5cHRvLnNjcnlwdFN5bmMoc2VjcmV0LCBTdHJpbmcocmVzdWx0LnVzZXJfaWQpLCAzMik7XG4gICAgICBjb25zdCBpdiA9IEJ1ZmZlci5mcm9tKHJlc3VsdC5lbmNyeXB0aW9uX2l2LCAnaGV4Jyk7XG5cbiAgICAgIC8vIERlY3J5cHQgc2Nob29sLCBlbWFpbCBhbmQgcGFzc3dvcmRcbiAgICAgIGNvbnN0IGRlY2lwaGVyMSA9IGNyeXB0by5jcmVhdGVEZWNpcGhlcml2KCdhZXMtMjU2LWNiYycsIGtleSwgaXYpO1xuICAgICAgY29uc3Qgc2Nob29sID0gcmVzdWx0LmVuY3J5cHRlZF9zY2hvb2wgPyBkZWNpcGhlcjEudXBkYXRlKHJlc3VsdC5lbmNyeXB0ZWRfc2Nob29sLCAnaGV4JywgJ3V0ZjgnKSArIGRlY2lwaGVyMS5maW5hbCgndXRmOCcpIDogbnVsbDtcbiAgICAgIFxuICAgICAgY29uc3QgZGVjaXBoZXIyID0gY3J5cHRvLmNyZWF0ZURlY2lwaGVyaXYoJ2Flcy0yNTYtY2JjJywga2V5LCBpdik7XG4gICAgICBjb25zdCBlbWFpbCA9IHJlc3VsdC5lbmNyeXB0ZWRfZW1haWwgPyBkZWNpcGhlcjIudXBkYXRlKHJlc3VsdC5lbmNyeXB0ZWRfZW1haWwsICdoZXgnLCAndXRmOCcpICsgZGVjaXBoZXIyLmZpbmFsKCd1dGY4JykgOiBudWxsO1xuICAgICAgXG4gICAgICBjb25zdCBkZWNpcGhlcjMgPSBjcnlwdG8uY3JlYXRlRGVjaXBoZXJpdignYWVzLTI1Ni1jYmMnLCBrZXksIGl2KTtcbiAgICAgIGNvbnN0IHBhc3N3b3JkID0gcmVzdWx0LmVuY3J5cHRlZF9wYXNzd29yZCA/IGRlY2lwaGVyMy51cGRhdGUocmVzdWx0LmVuY3J5cHRlZF9wYXNzd29yZCwgJ2hleCcsICd1dGY4JykgKyBkZWNpcGhlcjMuZmluYWwoJ3V0ZjgnKSA6IG51bGw7XG4gICAgICBcbiAgICAgIHJldHVybiB7XG4gICAgICAgIGxvZ2luTWV0aG9kOiByZXN1bHQubG9naW5fbWV0aG9kLFxuICAgICAgICBzY2hvb2wsXG4gICAgICAgIGVtYWlsLFxuICAgICAgICBwYXNzd29yZCxcbiAgICAgICAgdXNlcklkOiByZXN1bHQudXNlcl9pZFxuICAgICAgfTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGRlY3J5cHQgY3JlZGVudGlhbHM6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICB9XG5cbiAgZ2V0VXNlckNyZWRlbnRpYWxzKHVzZXJJZCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgU0VMRUNUIGxvZ2luX2tleSwgbG9naW5fbWV0aG9kLCBjcmVhdGVkX2F0IEZST00gZW5jcnlwdGVkX2NyZWRlbnRpYWxzIFxuICAgICAgV0hFUkUgdXNlcl9pZCA9ID8gQU5EIGlzX2FjdGl2ZSA9IDFcbiAgICAgIE9SREVSIEJZIGNyZWF0ZWRfYXQgREVTQ1xuICAgIGApO1xuICAgIFxuICAgIHJldHVybiBzdG10LmFsbCh1c2VySWQpO1xuICB9XG5cbiAgZGVhY3RpdmF0ZUNyZWRlbnRpYWxzKGxvZ2luS2V5KSB7XG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZSgnVVBEQVRFIGVuY3J5cHRlZF9jcmVkZW50aWFscyBTRVQgaXNfYWN0aXZlID0gMCBXSEVSRSBsb2dpbl9rZXkgPSA/Jyk7XG4gICAgcmV0dXJuIHN0bXQucnVuKGxvZ2luS2V5KTtcbiAgfVxuXG4gIC8vIExpY2Vuc2UgRmVhdHVyZSBTZXR0aW5ncyBNZXRob2RzXG4gIHNldExpY2Vuc2VGZWF0dXJlcyhsaWNlbnNlS2V5SWQsIGZlYXR1cmVzKSB7XG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBJTlNFUlQgT1IgUkVQTEFDRSBJTlRPIGxpY2Vuc2VfZmVhdHVyZV9zZXR0aW5nc1xuICAgICAgKGxpY2Vuc2Vfa2V5X2lkLCBtYXhfYWNjb3VudHNfcGVyX2JhdGNoLCBwcmlvcml0eV9sZXZlbCwgc2NoZWR1bGluZ19hY2Nlc3MsIG1heF9iYXRjaGVzX3Blcl9kYXksIHVwZGF0ZWRfYXQpXG4gICAgICBWQUxVRVMgKD8sID8sID8sID8sID8sIENVUlJFTlRfVElNRVNUQU1QKVxuICAgIGApO1xuXG4gICAgcmV0dXJuIHN0bXQucnVuKFxuICAgICAgbGljZW5zZUtleUlkLFxuICAgICAgZmVhdHVyZXMubWF4X2FjY291bnRzX3Blcl9iYXRjaCB8fCAwLFxuICAgICAgZmVhdHVyZXMucHJpb3JpdHlfbGV2ZWwgfHwgMCxcbiAgICAgIGZlYXR1cmVzLnNjaGVkdWxpbmdfYWNjZXNzID8gMSA6IDAsXG4gICAgICBmZWF0dXJlcy5tYXhfYmF0Y2hlc19wZXJfZGF5IHx8IDFcbiAgICApO1xuICB9XG5cbiAgZ2V0TGljZW5zZUZlYXR1cmVzKGxpY2Vuc2VLZXlJZCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgU0VMRUNUICogRlJPTSBsaWNlbnNlX2ZlYXR1cmVfc2V0dGluZ3MgV0hFUkUgbGljZW5zZV9rZXlfaWQgPSA/XG4gICAgYCk7XG5cbiAgICBjb25zdCByZXN1bHQgPSBzdG10LmdldChsaWNlbnNlS2V5SWQpO1xuICAgIGlmICghcmVzdWx0KSB7XG4gICAgICAvLyBSZXR1cm4gZGVmYXVsdCBmZWF0dXJlcyBpZiBub25lIHNldFxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgbWF4X2FjY291bnRzX3Blcl9iYXRjaDogMCxcbiAgICAgICAgcHJpb3JpdHlfbGV2ZWw6IDAsXG4gICAgICAgIHNjaGVkdWxpbmdfYWNjZXNzOiBmYWxzZSxcbiAgICAgICAgbWF4X2JhdGNoZXNfcGVyX2RheTogMVxuICAgICAgfTtcbiAgICB9XG5cbiAgICByZXR1cm4ge1xuICAgICAgbWF4X2FjY291bnRzX3Blcl9iYXRjaDogcmVzdWx0Lm1heF9hY2NvdW50c19wZXJfYmF0Y2gsXG4gICAgICBwcmlvcml0eV9sZXZlbDogcmVzdWx0LnByaW9yaXR5X2xldmVsLFxuICAgICAgc2NoZWR1bGluZ19hY2Nlc3M6IEJvb2xlYW4ocmVzdWx0LnNjaGVkdWxpbmdfYWNjZXNzKSxcbiAgICAgIG1heF9iYXRjaGVzX3Blcl9kYXk6IHJlc3VsdC5tYXhfYmF0Y2hlc19wZXJfZGF5IHx8IDFcbiAgICB9O1xuICB9XG5cbiAgZ2V0VXNlckxpY2Vuc2VGZWF0dXJlcyh1c2VySWQpIHtcbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIFNFTEVDVCBsZnMuKiBGUk9NIGxpY2Vuc2VfZmVhdHVyZV9zZXR0aW5ncyBsZnNcbiAgICAgIEpPSU4gdXNlcnMgdSBPTiB1LmxpY2Vuc2Vfa2V5X2lkID0gbGZzLmxpY2Vuc2Vfa2V5X2lkXG4gICAgICBXSEVSRSB1LmlkID0gP1xuICAgIGApO1xuXG4gICAgY29uc3QgcmVzdWx0ID0gc3RtdC5nZXQodXNlcklkKTtcbiAgICBpZiAoIXJlc3VsdCkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgbWF4X2FjY291bnRzX3Blcl9iYXRjaDogMCxcbiAgICAgICAgcHJpb3JpdHlfbGV2ZWw6IDAsXG4gICAgICAgIHNjaGVkdWxpbmdfYWNjZXNzOiBmYWxzZSxcbiAgICAgICAgbWF4X2JhdGNoZXNfcGVyX2RheTogMVxuICAgICAgfTtcbiAgICB9XG5cbiAgICByZXR1cm4ge1xuICAgICAgbWF4X2FjY291bnRzX3Blcl9iYXRjaDogcmVzdWx0Lm1heF9hY2NvdW50c19wZXJfYmF0Y2gsXG4gICAgICBwcmlvcml0eV9sZXZlbDogcmVzdWx0LnByaW9yaXR5X2xldmVsLFxuICAgICAgc2NoZWR1bGluZ19hY2Nlc3M6IEJvb2xlYW4ocmVzdWx0LnNjaGVkdWxpbmdfYWNjZXNzKSxcbiAgICAgIG1heF9iYXRjaGVzX3Blcl9kYXk6IHJlc3VsdC5tYXhfYmF0Y2hlc19wZXJfZGF5IHx8IDFcbiAgICB9O1xuICB9XG5cbiAgLy8gRGFpbHkgYmF0Y2ggY291bnQgY2hlY2tcbiAgZ2V0VXNlckRhaWx5QmF0Y2hDb3VudCh1c2VySWQsIGRhdGUgPSBudWxsKSB7XG4gICAgY29uc3QgdGFyZ2V0RGF0ZSA9IGRhdGUgfHwgbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF07IC8vIFlZWVktTU0tREQgZm9ybWF0XG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1QgQ09VTlQoKikgYXMgY291bnRcbiAgICAgIEZST00gcXVldWVfYmF0Y2hlc1xuICAgICAgV0hFUkUgdXNlcl9pZCA9ID9cbiAgICAgIEFORCBEQVRFKGNyZWF0ZWRfYXQpID0gP1xuICAgIGApO1xuXG4gICAgY29uc3QgcmVzdWx0ID0gc3RtdC5nZXQodXNlcklkLCB0YXJnZXREYXRlKTtcbiAgICByZXR1cm4gcmVzdWx0LmNvdW50O1xuICB9XG5cbiAgLy8gV2Vla2x5IHNjaGVkdWxlIGNvdW50IGNoZWNrXG4gIGdldFVzZXJXZWVrbHlTY2hlZHVsZUNvdW50KHVzZXJJZCkge1xuICAgIC8vIEdldCB0aGUgc3RhcnQgb2YgdGhlIGN1cnJlbnQgd2VlayAoTW9uZGF5KVxuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG4gICAgY29uc3QgZGF5T2ZXZWVrID0gbm93LmdldERheSgpOyAvLyAwID0gU3VuZGF5LCAxID0gTW9uZGF5LCBldGMuXG4gICAgY29uc3QgZGF5c1RvTW9uZGF5ID0gZGF5T2ZXZWVrID09PSAwID8gNiA6IGRheU9mV2VlayAtIDE7IC8vIElmIFN1bmRheSwgZ28gYmFjayA2IGRheXMgdG8gTW9uZGF5XG4gICAgY29uc3Qgc3RhcnRPZldlZWsgPSBuZXcgRGF0ZShub3cpO1xuICAgIHN0YXJ0T2ZXZWVrLnNldERhdGUobm93LmdldERhdGUoKSAtIGRheXNUb01vbmRheSk7XG4gICAgc3RhcnRPZldlZWsuc2V0SG91cnMoMCwgMCwgMCwgMCk7XG5cbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIFNFTEVDVCBDT1VOVCgqKSBhcyBjb3VudFxuICAgICAgRlJPTSBxdWV1ZV9zY2hlZHVsZXNcbiAgICAgIFdIRVJFIHVzZXJfaWQgPSA/XG4gICAgICBBTkQgY3JlYXRlZF9hdCA+PSA/XG4gICAgICBBTkQgc3RhdHVzICE9ICdjYW5jZWxsZWQnXG4gICAgYCk7XG5cbiAgICBjb25zdCByZXN1bHQgPSBzdG10LmdldCh1c2VySWQsIHN0YXJ0T2ZXZWVrLnRvSVNPU3RyaW5nKCkpO1xuICAgIHJldHVybiByZXN1bHQuY291bnQ7XG4gIH1cblxuICAvLyBRdWV1ZSBCYXRjaCBNZXRob2RzXG4gIGNyZWF0ZVF1ZXVlQmF0Y2godXNlcklkLCBiYXRjaE5hbWUsIGFjY291bnRzLCBzY2hlZHVsZWRUaW1lID0gbnVsbCwgbG9naW5UeXBlID0gJ25vcm1hbCcsIHNycFRhcmdldCA9IDEwMCkge1xuICAgIGNvbnN0IHRyYW5zYWN0aW9uID0gdGhpcy5kYi50cmFuc2FjdGlvbigoKSA9PiB7XG4gICAgICAvLyBHZXQgdXNlcidzIGxpY2Vuc2UgZmVhdHVyZXNcbiAgICAgIGNvbnN0IGZlYXR1cmVzID0gdGhpcy5nZXRVc2VyTGljZW5zZUZlYXR1cmVzKHVzZXJJZCk7XG5cbiAgICAgIC8vIENoZWNrIGRhaWx5IGJhdGNoIGxpbWl0XG4gICAgICBjb25zdCBkYWlseUJhdGNoQ291bnQgPSB0aGlzLmdldFVzZXJEYWlseUJhdGNoQ291bnQodXNlcklkKTtcbiAgICAgIGlmIChkYWlseUJhdGNoQ291bnQgPj0gZmVhdHVyZXMubWF4X2JhdGNoZXNfcGVyX2RheSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYERhaWx5IGJhdGNoIGxpbWl0IHJlYWNoZWQgKCR7ZmVhdHVyZXMubWF4X2JhdGNoZXNfcGVyX2RheX0gYmF0Y2hlcyBwZXIgZGF5KS4gUGxlYXNlIHRyeSBhZ2FpbiB0b21vcnJvdy5gKTtcbiAgICAgIH1cblxuICAgICAgLy8gVmFsaWRhdGUgYmF0Y2ggc2l6ZSBhZ2FpbnN0IGxpY2Vuc2UgbGltaXRzXG4gICAgICBpZiAoZmVhdHVyZXMubWF4X2FjY291bnRzX3Blcl9iYXRjaCA+IDAgJiYgYWNjb3VudHMubGVuZ3RoID4gZmVhdHVyZXMubWF4X2FjY291bnRzX3Blcl9iYXRjaCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEJhdGNoIHNpemUgKCR7YWNjb3VudHMubGVuZ3RofSkgZXhjZWVkcyBsaWNlbnNlIGxpbWl0ICgke2ZlYXR1cmVzLm1heF9hY2NvdW50c19wZXJfYmF0Y2h9KWApO1xuICAgICAgfVxuXG4gICAgICAvLyBWYWxpZGF0ZSBzY2hlZHVsaW5nIGFjY2Vzc1xuICAgICAgaWYgKHNjaGVkdWxlZFRpbWUgJiYgIWZlYXR1cmVzLnNjaGVkdWxpbmdfYWNjZXNzKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignU2NoZWR1bGluZyBhY2Nlc3Mgbm90IGF2YWlsYWJsZSBmb3IgdGhpcyBsaWNlbnNlJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIFZhbGlkYXRlIGxvZ2luIHR5cGVcbiAgICAgIGlmICghWydub3JtYWwnLCAnZ29vZ2xlJywgJ21pY3Jvc29mdCddLmluY2x1ZGVzKGxvZ2luVHlwZSkpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIGxvZ2luIHR5cGUgc3BlY2lmaWVkJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIFZhbGlkYXRlIFNSUCB0YXJnZXRcbiAgICAgIGlmIChzcnBUYXJnZXQgPCAxIHx8IHNycFRhcmdldCA+IDQwMCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1NSUCB0YXJnZXQgbXVzdCBiZSBiZXR3ZWVuIDEgYW5kIDQwMCcpO1xuICAgICAgfVxuXG4gICAgICAvLyBDcmVhdGUgYmF0Y2hcbiAgICAgIGNvbnN0IGJhdGNoU3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICAgIElOU0VSVCBJTlRPIHF1ZXVlX2JhdGNoZXMgKHVzZXJfaWQsIGJhdGNoX25hbWUsIGxvZ2luX3R5cGUsIHRvdGFsX2FjY291bnRzLCBwcmlvcml0eV9sZXZlbCwgc3JwX3RhcmdldCwgc2NoZWR1bGVkX3RpbWUpXG4gICAgICAgIFZBTFVFUyAoPywgPywgPywgPywgPywgPywgPylcbiAgICAgIGApO1xuXG4gICAgICBjb25zdCBiYXRjaFJlc3VsdCA9IGJhdGNoU3RtdC5ydW4oXG4gICAgICAgIHVzZXJJZCxcbiAgICAgICAgYmF0Y2hOYW1lLFxuICAgICAgICBsb2dpblR5cGUsXG4gICAgICAgIGFjY291bnRzLmxlbmd0aCxcbiAgICAgICAgZmVhdHVyZXMucHJpb3JpdHlfbGV2ZWwsXG4gICAgICAgIHNycFRhcmdldCxcbiAgICAgICAgc2NoZWR1bGVkVGltZVxuICAgICAgKTtcbiAgICAgIFxuICAgICAgY29uc3QgYmF0Y2hJZCA9IGJhdGNoUmVzdWx0Lmxhc3RJbnNlcnRSb3dpZDtcbiAgICAgIFxuICAgICAgLy8gQ3JlYXRlIGluZGl2aWR1YWwgam9icyBmb3IgZWFjaCBhY2NvdW50XG4gICAgICBjb25zdCBqb2JTdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgICAgSU5TRVJUIElOVE8gcXVldWVfam9icyAoYmF0Y2hfaWQsIHVzZXJfaWQsIGpvYl9kYXRhLCBwcmlvcml0eV9sZXZlbCwgZWZmZWN0aXZlX3ByaW9yaXR5LCBzcnBfdGFyZ2V0LCBzY2hlZHVsZWRfdGltZSlcbiAgICAgICAgVkFMVUVTICg/LCA/LCA/LCA/LCA/LCA/LCA/KVxuICAgICAgYCk7XG4gICAgICBcbiAgICAgIGFjY291bnRzLmZvckVhY2goYWNjb3VudCA9PiB7XG4gICAgICAgIGNvbnN0IGVmZmVjdGl2ZVByaW9yaXR5ID0gdGhpcy5jYWxjdWxhdGVFZmZlY3RpdmVQcmlvcml0eShmZWF0dXJlcy5wcmlvcml0eV9sZXZlbCwgc2NoZWR1bGVkVGltZSk7XG4gICAgICAgIGpvYlN0bXQucnVuKFxuICAgICAgICAgIGJhdGNoSWQsXG4gICAgICAgICAgdXNlcklkLFxuICAgICAgICAgIEpTT04uc3RyaW5naWZ5KGFjY291bnQpLFxuICAgICAgICAgIGZlYXR1cmVzLnByaW9yaXR5X2xldmVsLFxuICAgICAgICAgIGVmZmVjdGl2ZVByaW9yaXR5LFxuICAgICAgICAgIHNycFRhcmdldCxcbiAgICAgICAgICBzY2hlZHVsZWRUaW1lXG4gICAgICAgICk7XG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgLy8gQ3JlYXRlIHNjaGVkdWxlIGVudHJ5IGlmIHNjaGVkdWxlZFxuICAgICAgaWYgKHNjaGVkdWxlZFRpbWUpIHtcbiAgICAgICAgdGhpcy5jcmVhdGVTY2hlZHVsZUVudHJ5KHVzZXJJZCwgc2NoZWR1bGVkVGltZSwgbnVsbCwgYmF0Y2hJZCwgMzAsIHNycFRhcmdldCk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIExvZyBhY3Rpdml0eVxuICAgICAgdGhpcy5sb2dBY3Rpdml0eSh1c2VySWQsICdCQVRDSF9DUkVBVEVEJywgYENyZWF0ZWQgYmF0Y2g6ICR7YmF0Y2hOYW1lfSB3aXRoICR7YWNjb3VudHMubGVuZ3RofSBhY2NvdW50c2ApO1xuICAgICAgXG4gICAgICByZXR1cm4gYmF0Y2hJZDtcbiAgICB9KTtcbiAgICBcbiAgICByZXR1cm4gdHJhbnNhY3Rpb24oKTtcbiAgfVxuXG4gIGNhbGN1bGF0ZUVmZmVjdGl2ZVByaW9yaXR5KGJhc2VQcmlvcml0eSwgc2NoZWR1bGVkVGltZSkge1xuICAgIGxldCBlZmZlY3RpdmVQcmlvcml0eSA9IGJhc2VQcmlvcml0eTtcbiAgICBcbiAgICAvLyBCb29zdCBwcmlvcml0eSBmb3Igc2NoZWR1bGVkIGpvYnMgYXBwcm9hY2hpbmcgdGhlaXIgdGltZVxuICAgIGlmIChzY2hlZHVsZWRUaW1lKSB7XG4gICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuICAgICAgY29uc3Qgc2NoZWR1bGVkID0gbmV3IERhdGUoc2NoZWR1bGVkVGltZSk7XG4gICAgICBjb25zdCB0aW1lRGlmZiA9IHNjaGVkdWxlZC5nZXRUaW1lKCkgLSBub3cuZ2V0VGltZSgpO1xuICAgICAgY29uc3QgaG91cnNVbnRpbCA9IHRpbWVEaWZmIC8gKDEwMDAgKiA2MCAqIDYwKTtcbiAgICAgIFxuICAgICAgaWYgKGhvdXJzVW50aWwgPD0gMSkge1xuICAgICAgICBlZmZlY3RpdmVQcmlvcml0eSArPSA1OyAvLyBIaWdoIGJvb3N0IGZvciBqb2JzIGR1ZSB3aXRoaW4gYW4gaG91clxuICAgICAgfSBlbHNlIGlmIChob3Vyc1VudGlsIDw9IDYpIHtcbiAgICAgICAgZWZmZWN0aXZlUHJpb3JpdHkgKz0gMjsgLy8gTWVkaXVtIGJvb3N0IGZvciBqb2JzIGR1ZSB3aXRoaW4gNiBob3Vyc1xuICAgICAgfVxuICAgIH1cbiAgICBcbiAgICAvLyBBcHBseSBzdGFydmF0aW9uIHByZXZlbnRpb24gKGJvb3N0IHByaW9yaXR5IGZvciBvbGQgam9icylcbiAgICAvLyBUaGlzIHdvdWxkIGJlIGltcGxlbWVudGVkIGluIGEgYmFja2dyb3VuZCBwcm9jZXNzXG4gICAgXG4gICAgcmV0dXJuIE1hdGgubWluKGVmZmVjdGl2ZVByaW9yaXR5LCAxMCk7IC8vIENhcCBhdCBtYXhpbXVtIHByaW9yaXR5XG4gIH1cblxuICBnZXRRdWV1ZUJhdGNoZXModXNlcklkID0gbnVsbCwgc3RhdHVzID0gbnVsbCwgbGltaXQgPSA1MCwgb2Zmc2V0ID0gMCkge1xuICAgIGxldCBxdWVyeSA9IGBcbiAgICAgIFNFTEVDVCBxYi4qLCB1LnVzZXJuYW1lXG4gICAgICBGUk9NIHF1ZXVlX2JhdGNoZXMgcWJcbiAgICAgIEpPSU4gdXNlcnMgdSBPTiBxYi51c2VyX2lkID0gdS5pZFxuICAgICAgV0hFUkUgMT0xXG4gICAgYDtcbiAgICBjb25zdCBwYXJhbXMgPSBbXTtcbiAgICBcbiAgICBpZiAodXNlcklkKSB7XG4gICAgICBxdWVyeSArPSAnIEFORCBxYi51c2VyX2lkID0gPyc7XG4gICAgICBwYXJhbXMucHVzaCh1c2VySWQpO1xuICAgIH1cbiAgICBcbiAgICBpZiAoc3RhdHVzKSB7XG4gICAgICBxdWVyeSArPSAnIEFORCBxYi5zdGF0dXMgPSA/JztcbiAgICAgIHBhcmFtcy5wdXNoKHN0YXR1cyk7XG4gICAgfVxuICAgIFxuICAgIHF1ZXJ5ICs9ICcgT1JERVIgQlkgcWIuY3JlYXRlZF9hdCBERVNDIExJTUlUID8gT0ZGU0VUID8nO1xuICAgIHBhcmFtcy5wdXNoKGxpbWl0LCBvZmZzZXQpO1xuICAgIFxuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUocXVlcnkpO1xuICAgIHJldHVybiBzdG10LmFsbCguLi5wYXJhbXMpO1xuICB9XG5cbiAgZ2V0QmF0Y2hKb2JzKGJhdGNoSWQpIHtcbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIFNFTEVDVCAqIEZST00gcXVldWVfam9icyBcbiAgICAgIFdIRVJFIGJhdGNoX2lkID0gPyBcbiAgICAgIE9SREVSIEJZIGVmZmVjdGl2ZV9wcmlvcml0eSBERVNDLCBjcmVhdGVkX2F0IEFTQ1xuICAgIGApO1xuICAgIFxuICAgIHJldHVybiBzdG10LmFsbChiYXRjaElkKTtcbiAgfVxuXG4gIHVwZGF0ZUJhdGNoU3RhdHVzKGJhdGNoSWQsIHN0YXR1cywgY29tcGxldGVkQXQgPSBudWxsKSB7XG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBVUERBVEUgcXVldWVfYmF0Y2hlc1xuICAgICAgU0VUIHN0YXR1cyA9ID8sIGNvbXBsZXRlZF9hdCA9ID9cbiAgICAgIFdIRVJFIGlkID0gP1xuICAgIGApO1xuXG4gICAgcmV0dXJuIHN0bXQucnVuKHN0YXR1cywgY29tcGxldGVkQXQsIGJhdGNoSWQpO1xuICB9XG5cbiAgLy8gUXVldWUgSm9iIE1ldGhvZHNcbiAgY3JlYXRlUXVldWVKb2IoeyB1c2VybmFtZSwgam9iX3R5cGUgPSAnc3BhcnhfcmVhZGVyJywgam9iX2RhdGEsIHNycF90YXJnZXQgPSAxMDAsIHByaW9yaXR5ID0gMCwgc3RhdHVzID0gJ3F1ZXVlZCcgfSkge1xuICAgIC8vIEdldCB1c2VyIElEXG4gICAgY29uc3QgdXNlclN0bXQgPSB0aGlzLmRiLnByZXBhcmUoJ1NFTEVDVCBpZCBGUk9NIHVzZXJzIFdIRVJFIHVzZXJuYW1lID0gPycpO1xuICAgIGNvbnN0IHVzZXIgPSB1c2VyU3RtdC5nZXQodXNlcm5hbWUpO1xuXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbm90IGZvdW5kJyk7XG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlIHRoZSBqb2JcbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIElOU0VSVCBJTlRPIHF1ZXVlX2pvYnMgKHVzZXJfaWQsIGpvYl90eXBlLCBqb2JfZGF0YSwgcHJpb3JpdHlfbGV2ZWwsIGVmZmVjdGl2ZV9wcmlvcml0eSwgc3JwX3RhcmdldCwgc3RhdHVzKVxuICAgICAgVkFMVUVTICg/LCA/LCA/LCA/LCA/LCA/LCA/KVxuICAgIGApO1xuXG4gICAgY29uc3QgcmVzdWx0ID0gc3RtdC5ydW4odXNlci5pZCwgam9iX3R5cGUsIGpvYl9kYXRhLCBwcmlvcml0eSwgcHJpb3JpdHksIHNycF90YXJnZXQsIHN0YXR1cyk7XG4gICAgcmV0dXJuIHJlc3VsdC5sYXN0SW5zZXJ0Um93aWQ7XG4gIH1cblxuICBnZXRRdWV1ZUpvYnModXNlcklkID0gbnVsbCkge1xuICAgIGxldCBxdWVyeSA9IGBcbiAgICAgIFNFTEVDVCBxai4qLCBxYi5iYXRjaF9uYW1lLCB1LnVzZXJuYW1lXG4gICAgICBGUk9NIHF1ZXVlX2pvYnMgcWpcbiAgICAgIExFRlQgSk9JTiBxdWV1ZV9iYXRjaGVzIHFiIE9OIHFqLmJhdGNoX2lkID0gcWIuaWRcbiAgICAgIEpPSU4gdXNlcnMgdSBPTiBxai51c2VyX2lkID0gdS5pZFxuICAgIGA7XG4gICAgY29uc3QgcGFyYW1zID0gW107XG5cbiAgICBpZiAodXNlcklkKSB7XG4gICAgICBxdWVyeSArPSAnIFdIRVJFIHFqLnVzZXJfaWQgPSA/JztcbiAgICAgIHBhcmFtcy5wdXNoKHVzZXJJZCk7XG4gICAgfVxuXG4gICAgcXVlcnkgKz0gJyBPUkRFUiBCWSBxai5jcmVhdGVkX2F0IERFU0MnO1xuXG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShxdWVyeSk7XG4gICAgcmV0dXJuIHN0bXQuYWxsKC4uLnBhcmFtcyk7XG4gIH1cblxuICBnZXROZXh0UXVldWVKb2IoKSB7XG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1QgcWouKiwgcWIuYmF0Y2hfbmFtZSwgdS51c2VybmFtZVxuICAgICAgRlJPTSBxdWV1ZV9qb2JzIHFqXG4gICAgICBMRUZUIEpPSU4gcXVldWVfYmF0Y2hlcyBxYiBPTiBxai5iYXRjaF9pZCA9IHFiLmlkXG4gICAgICBKT0lOIHVzZXJzIHUgT04gcWoudXNlcl9pZCA9IHUuaWRcbiAgICAgIFdIRVJFIHFqLnN0YXR1cyA9ICdxdWV1ZWQnXG4gICAgICBBTkQgKHFqLnNjaGVkdWxlZF90aW1lIElTIE5VTEwgT1IgcWouc2NoZWR1bGVkX3RpbWUgPD0gZGF0ZXRpbWUoJ25vdycpKVxuICAgICAgT1JERVIgQlkgcWouZWZmZWN0aXZlX3ByaW9yaXR5IERFU0MsIHFqLmNyZWF0ZWRfYXQgQVNDXG4gICAgICBMSU1JVCAxXG4gICAgYCk7XG5cbiAgICByZXR1cm4gc3RtdC5nZXQoKTtcbiAgfVxuXG4gIHVwZGF0ZUpvYlN0YXR1cyhqb2JJZCwgc3RhdHVzLCBlcnJvck1lc3NhZ2UgPSBudWxsLCBzdGFydGVkQXQgPSBudWxsLCBjb21wbGV0ZWRBdCA9IG51bGwpIHtcbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIFVQREFURSBxdWV1ZV9qb2JzIFxuICAgICAgU0VUIHN0YXR1cyA9ID8sIGVycm9yX21lc3NhZ2UgPSA/LCBzdGFydGVkX2F0ID0gPywgY29tcGxldGVkX2F0ID0gP1xuICAgICAgV0hFUkUgaWQgPSA/XG4gICAgYCk7XG4gICAgXG4gICAgcmV0dXJuIHN0bXQucnVuKHN0YXR1cywgZXJyb3JNZXNzYWdlLCBzdGFydGVkQXQsIGNvbXBsZXRlZEF0LCBqb2JJZCk7XG4gIH1cblxuICBpbmNyZW1lbnRKb2JSZXRyeShqb2JJZCkge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgVVBEQVRFIHF1ZXVlX2pvYnMgXG4gICAgICBTRVQgcmV0cnlfY291bnQgPSByZXRyeV9jb3VudCArIDEsIHN0YXR1cyA9ICdxdWV1ZWQnXG4gICAgICBXSEVSRSBpZCA9ID8gQU5EIHJldHJ5X2NvdW50IDwgbWF4X3JldHJpZXNcbiAgICBgKTtcbiAgICBcbiAgICByZXR1cm4gc3RtdC5ydW4oam9iSWQpO1xuICB9XG5cbiAgLy8gU2NoZWR1bGluZyBNZXRob2RzXG4gIGNyZWF0ZVNjaGVkdWxlRW50cnkodXNlcklkLCBzY2hlZHVsZWRUaW1lLCBqb2JJZCA9IG51bGwsIGJhdGNoSWQgPSBudWxsLCBkdXJhdGlvbk1pbnV0ZXMgPSAzMCwgc3JwVGFyZ2V0ID0gMTAwKSB7XG4gICAgLy8gQ2hlY2sgZm9yIGNvbmZsaWN0c1xuICAgIGNvbnN0IGNvbmZsaWN0cyA9IHRoaXMuY2hlY2tTY2hlZHVsZUNvbmZsaWN0cyh1c2VySWQsIHNjaGVkdWxlZFRpbWUsIGR1cmF0aW9uTWludXRlcyk7XG4gICAgaWYgKGNvbmZsaWN0cy5sZW5ndGggPiAwKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFNjaGVkdWxlIGNvbmZsaWN0IGRldGVjdGVkIGF0ICR7c2NoZWR1bGVkVGltZX1gKTtcbiAgICB9XG5cbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIElOU0VSVCBJTlRPIHF1ZXVlX3NjaGVkdWxlcyAodXNlcl9pZCwgc2NoZWR1bGVkX3RpbWUsIGR1cmF0aW9uX21pbnV0ZXMsIHNycF90YXJnZXQsIGpvYl9pZCwgYmF0Y2hfaWQpXG4gICAgICBWQUxVRVMgKD8sID8sID8sID8sID8sID8pXG4gICAgYCk7XG5cbiAgICByZXR1cm4gc3RtdC5ydW4odXNlcklkLCBzY2hlZHVsZWRUaW1lLCBkdXJhdGlvbk1pbnV0ZXMsIHNycFRhcmdldCwgam9iSWQsIGJhdGNoSWQpO1xuICB9XG5cbiAgY2hlY2tTY2hlZHVsZUNvbmZsaWN0cyh1c2VySWQsIHNjaGVkdWxlZFRpbWUsIGR1cmF0aW9uTWludXRlcykge1xuICAgIGNvbnN0IHN0YXJ0VGltZSA9IG5ldyBEYXRlKHNjaGVkdWxlZFRpbWUpO1xuICAgIGNvbnN0IGVuZFRpbWUgPSBuZXcgRGF0ZShzdGFydFRpbWUuZ2V0VGltZSgpICsgKGR1cmF0aW9uTWludXRlcyAqIDYwICogMTAwMCkpO1xuICAgIFxuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgU0VMRUNUICogRlJPTSBxdWV1ZV9zY2hlZHVsZXNcbiAgICAgIFdIRVJFIHVzZXJfaWQgPSA/IFxuICAgICAgQU5EIHN0YXR1cyBJTiAoJ3NjaGVkdWxlZCcsICdhY3RpdmUnKVxuICAgICAgQU5EIChcbiAgICAgICAgKHNjaGVkdWxlZF90aW1lIDw9ID8gQU5EIGRhdGV0aW1lKHNjaGVkdWxlZF90aW1lLCAnKycgfHwgZHVyYXRpb25fbWludXRlcyB8fCAnIG1pbnV0ZXMnKSA+ID8pIE9SXG4gICAgICAgIChzY2hlZHVsZWRfdGltZSA8ID8gQU5EIGRhdGV0aW1lKHNjaGVkdWxlZF90aW1lLCAnKycgfHwgZHVyYXRpb25fbWludXRlcyB8fCAnIG1pbnV0ZXMnKSA+PSA/KVxuICAgICAgKVxuICAgIGApO1xuICAgIFxuICAgIHJldHVybiBzdG10LmFsbCh1c2VySWQsIHN0YXJ0VGltZS50b0lTT1N0cmluZygpLCBzdGFydFRpbWUudG9JU09TdHJpbmcoKSwgXG4gICAgICAgICAgICAgICAgICAgZW5kVGltZS50b0lTT1N0cmluZygpLCBlbmRUaW1lLnRvSVNPU3RyaW5nKCkpO1xuICB9XG5cbiAgZ2V0VXNlclNjaGVkdWxlcyh1c2VySWQsIHN0YXJ0RGF0ZSA9IG51bGwsIGVuZERhdGUgPSBudWxsKSB7XG4gICAgbGV0IHF1ZXJ5ID0gYFxuICAgICAgU0VMRUNUIHFzLiosIHFqLmpvYl90eXBlLCBxYi5iYXRjaF9uYW1lXG4gICAgICBGUk9NIHF1ZXVlX3NjaGVkdWxlcyBxc1xuICAgICAgTEVGVCBKT0lOIHF1ZXVlX2pvYnMgcWogT04gcXMuam9iX2lkID0gcWouaWRcbiAgICAgIExFRlQgSk9JTiBxdWV1ZV9iYXRjaGVzIHFiIE9OIHFzLmJhdGNoX2lkID0gcWIuaWRcbiAgICAgIFdIRVJFIHFzLnVzZXJfaWQgPSA/XG4gICAgYDtcbiAgICBjb25zdCBwYXJhbXMgPSBbdXNlcklkXTtcbiAgICBcbiAgICBpZiAoc3RhcnREYXRlKSB7XG4gICAgICBxdWVyeSArPSAnIEFORCBxcy5zY2hlZHVsZWRfdGltZSA+PSA/JztcbiAgICAgIHBhcmFtcy5wdXNoKHN0YXJ0RGF0ZSk7XG4gICAgfVxuICAgIFxuICAgIGlmIChlbmREYXRlKSB7XG4gICAgICBxdWVyeSArPSAnIEFORCBxcy5zY2hlZHVsZWRfdGltZSA8PSA/JztcbiAgICAgIHBhcmFtcy5wdXNoKGVuZERhdGUpO1xuICAgIH1cbiAgICBcbiAgICBxdWVyeSArPSAnIE9SREVSIEJZIHFzLnNjaGVkdWxlZF90aW1lIEFTQyc7XG4gICAgXG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShxdWVyeSk7XG4gICAgcmV0dXJuIHN0bXQuYWxsKC4uLnBhcmFtcyk7XG4gIH1cblxuICAvLyBQcmlvcml0eSBNYW5hZ2VtZW50IE1ldGhvZHNcbiAgdXBkYXRlSm9iUHJpb3JpdHkoam9iSWQsIG5ld1ByaW9yaXR5LCBhZG1pbk92ZXJyaWRlID0gZmFsc2UpIHtcbiAgICBjb25zdCBzdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIFVQREFURSBxdWV1ZV9qb2JzIFxuICAgICAgU0VUIGVmZmVjdGl2ZV9wcmlvcml0eSA9ID8sIHByaW9yaXR5X2xldmVsID0gP1xuICAgICAgV0hFUkUgaWQgPSA/XG4gICAgYCk7XG4gICAgXG4gICAgY29uc3QgcmVzdWx0ID0gc3RtdC5ydW4obmV3UHJpb3JpdHksIGFkbWluT3ZlcnJpZGUgPyBuZXdQcmlvcml0eSA6IG51bGwsIGpvYklkKTtcbiAgICBcbiAgICBpZiAoYWRtaW5PdmVycmlkZSkge1xuICAgICAgdGhpcy5sb2dBY3Rpdml0eShudWxsLCAnQURNSU5fUFJJT1JJVFlfT1ZFUlJJREUnLCBgSm9iICR7am9iSWR9IHByaW9yaXR5IHNldCB0byAke25ld1ByaW9yaXR5fWApO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gcmVzdWx0O1xuICB9XG5cbiAgYXBwbHlTdGFydmF0aW9uUHJldmVudGlvbigpIHtcbiAgICAvLyBCb29zdCBwcmlvcml0eSBmb3Igam9icyB0aGF0IGhhdmUgYmVlbiB3YWl0aW5nIHRvbyBsb25nXG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBVUERBVEUgcXVldWVfam9icyBcbiAgICAgIFNFVCBlZmZlY3RpdmVfcHJpb3JpdHkgPSBDQVNFIFxuICAgICAgICBXSEVOIGRhdGV0aW1lKCdub3cnKSA+IGRhdGV0aW1lKGNyZWF0ZWRfYXQsICcrMiBob3VycycpIFRIRU4gTUlOKGVmZmVjdGl2ZV9wcmlvcml0eSArIDIsIDEwKVxuICAgICAgICBXSEVOIGRhdGV0aW1lKCdub3cnKSA+IGRhdGV0aW1lKGNyZWF0ZWRfYXQsICcrMSBob3VyJykgVEhFTiBNSU4oZWZmZWN0aXZlX3ByaW9yaXR5ICsgMSwgMTApXG4gICAgICAgIEVMU0UgZWZmZWN0aXZlX3ByaW9yaXR5XG4gICAgICBFTkRcbiAgICAgIFdIRVJFIHN0YXR1cyA9ICdxdWV1ZWQnXG4gICAgYCk7XG4gICAgXG4gICAgcmV0dXJuIHN0bXQucnVuKCk7XG4gIH1cblxuICBnZXRRdWV1ZVN0YXRzKCkge1xuICAgIGNvbnN0IHN0YXRzID0ge307XG4gICAgXG4gICAgLy8gVG90YWwgam9icyBieSBzdGF0dXNcbiAgICBjb25zdCBzdGF0dXNTdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIFNFTEVDVCBzdGF0dXMsIENPVU5UKCopIGFzIGNvdW50IFxuICAgICAgRlJPTSBxdWV1ZV9qb2JzIFxuICAgICAgR1JPVVAgQlkgc3RhdHVzXG4gICAgYCk7XG4gICAgc3RhdHMuam9ic0J5U3RhdHVzID0gc3RhdHVzU3RtdC5hbGwoKTtcbiAgICBcbiAgICAvLyBKb2JzIGJ5IHByaW9yaXR5IGxldmVsXG4gICAgY29uc3QgcHJpb3JpdHlTdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIFNFTEVDVCBlZmZlY3RpdmVfcHJpb3JpdHksIENPVU5UKCopIGFzIGNvdW50IFxuICAgICAgRlJPTSBxdWV1ZV9qb2JzIFxuICAgICAgV0hFUkUgc3RhdHVzID0gJ3F1ZXVlZCdcbiAgICAgIEdST1VQIEJZIGVmZmVjdGl2ZV9wcmlvcml0eVxuICAgICAgT1JERVIgQlkgZWZmZWN0aXZlX3ByaW9yaXR5IERFU0NcbiAgICBgKTtcbiAgICBzdGF0cy5qb2JzQnlQcmlvcml0eSA9IHByaW9yaXR5U3RtdC5hbGwoKTtcbiAgICBcbiAgICAvLyBBdmVyYWdlIHdhaXQgdGltZVxuICAgIGNvbnN0IHdhaXRUaW1lU3RtdCA9IHRoaXMuZGIucHJlcGFyZShgXG4gICAgICBTRUxFQ1QgQVZHKGp1bGlhbmRheShzdGFydGVkX2F0KSAtIGp1bGlhbmRheShjcmVhdGVkX2F0KSkgKiAyNCAqIDYwIGFzIGF2Z193YWl0X21pbnV0ZXNcbiAgICAgIEZST00gcXVldWVfam9icyBcbiAgICAgIFdIRVJFIHN0YXJ0ZWRfYXQgSVMgTk9UIE5VTExcbiAgICBgKTtcbiAgICBzdGF0cy5hdmVyYWdlV2FpdFRpbWUgPSB3YWl0VGltZVN0bXQuZ2V0KCk/LmF2Z193YWl0X21pbnV0ZXMgfHwgMDtcbiAgICBcbiAgICByZXR1cm4gc3RhdHM7XG4gIH1cblxuICBjbG9zZSgpIHtcbiAgICB0aGlzLmRiLmNsb3NlKCk7XG4gIH1cbiAgLy8gU3lzdGVtIENvbmZpZ3VyYXRpb24gTWV0aG9kc1xuICBnZXRTeXN0ZW1Db25maWcoa2V5KSB7XG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZSgnU0VMRUNUICogRlJPTSBzeXN0ZW1fY29uZmlnIFdIRVJFIGNvbmZpZ19rZXkgPSA/Jyk7XG4gICAgY29uc3QgY29uZmlnID0gc3RtdC5nZXQoa2V5KTtcblxuICAgIGlmICghY29uZmlnKSByZXR1cm4gbnVsbDtcblxuICAgIC8vIFBhcnNlIHZhbHVlIGJhc2VkIG9uIHR5cGVcbiAgICBzd2l0Y2ggKGNvbmZpZy5jb25maWdfdHlwZSkge1xuICAgICAgY2FzZSAnbnVtYmVyJzpcbiAgICAgICAgcmV0dXJuIHsgLi4uY29uZmlnLCBjb25maWdfdmFsdWU6IHBhcnNlSW50KGNvbmZpZy5jb25maWdfdmFsdWUpIH07XG4gICAgICBjYXNlICdib29sZWFuJzpcbiAgICAgICAgcmV0dXJuIHsgLi4uY29uZmlnLCBjb25maWdfdmFsdWU6IGNvbmZpZy5jb25maWdfdmFsdWUgPT09ICd0cnVlJyB9O1xuICAgICAgY2FzZSAnanNvbic6XG4gICAgICAgIHJldHVybiB7IC4uLmNvbmZpZywgY29uZmlnX3ZhbHVlOiBKU09OLnBhcnNlKGNvbmZpZy5jb25maWdfdmFsdWUpIH07XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gY29uZmlnO1xuICAgIH1cbiAgfVxuXG4gIHNldFN5c3RlbUNvbmZpZyhrZXksIHZhbHVlLCB0eXBlID0gJ3N0cmluZycsIGRlc2NyaXB0aW9uID0gbnVsbCkge1xuICAgIGNvbnN0IHN0cmluZ1ZhbHVlID0gdHlwZSA9PT0gJ2pzb24nID8gSlNPTi5zdHJpbmdpZnkodmFsdWUpIDogdmFsdWUudG9TdHJpbmcoKTtcblxuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgSU5TRVJUIE9SIFJFUExBQ0UgSU5UTyBzeXN0ZW1fY29uZmlnIChjb25maWdfa2V5LCBjb25maWdfdmFsdWUsIGNvbmZpZ190eXBlLCBkZXNjcmlwdGlvbiwgdXBkYXRlZF9hdClcbiAgICAgIFZBTFVFUyAoPywgPywgPywgQ09BTEVTQ0UoPywgKFNFTEVDVCBkZXNjcmlwdGlvbiBGUk9NIHN5c3RlbV9jb25maWcgV0hFUkUgY29uZmlnX2tleSA9ID8pKSwgQ1VSUkVOVF9USU1FU1RBTVApXG4gICAgYCk7XG5cbiAgICByZXR1cm4gc3RtdC5ydW4oa2V5LCBzdHJpbmdWYWx1ZSwgdHlwZSwgZGVzY3JpcHRpb24sIGtleSk7XG4gIH1cblxuICBnZXRBbGxTeXN0ZW1Db25maWcoKSB7XG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZSgnU0VMRUNUICogRlJPTSBzeXN0ZW1fY29uZmlnIE9SREVSIEJZIGNvbmZpZ19rZXknKTtcbiAgICBjb25zdCBjb25maWdzID0gc3RtdC5hbGwoKTtcblxuICAgIHJldHVybiBjb25maWdzLm1hcChjb25maWcgPT4ge1xuICAgICAgc3dpdGNoIChjb25maWcuY29uZmlnX3R5cGUpIHtcbiAgICAgICAgY2FzZSAnbnVtYmVyJzpcbiAgICAgICAgICByZXR1cm4geyAuLi5jb25maWcsIGNvbmZpZ192YWx1ZTogcGFyc2VJbnQoY29uZmlnLmNvbmZpZ192YWx1ZSkgfTtcbiAgICAgICAgY2FzZSAnYm9vbGVhbic6XG4gICAgICAgICAgcmV0dXJuIHsgLi4uY29uZmlnLCBjb25maWdfdmFsdWU6IGNvbmZpZy5jb25maWdfdmFsdWUgPT09ICd0cnVlJyB9O1xuICAgICAgICBjYXNlICdqc29uJzpcbiAgICAgICAgICByZXR1cm4geyAuLi5jb25maWcsIGNvbmZpZ192YWx1ZTogSlNPTi5wYXJzZShjb25maWcuY29uZmlnX3ZhbHVlKSB9O1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIHJldHVybiBjb25maWc7XG4gICAgICB9XG4gICAgfSk7XG4gIH1cblxuICAvLyBEZWFkIExldHRlciBRdWV1ZSBNZXRob2RzXG4gIGFkZFRvRGVhZExldHRlclF1ZXVlKG9yaWdpbmFsSm9iSWQsIGpvYlR5cGUsIGpvYkRhdGEsIHVzZXJJZCwgZmFpbHVyZVJlYXNvbikge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgSU5TRVJUIElOVE8gZGVhZF9sZXR0ZXJfcXVldWUgKG9yaWdpbmFsX2pvYl9pZCwgam9iX3R5cGUsIGpvYl9kYXRhLCB1c2VyX2lkLCBmYWlsdXJlX3JlYXNvbilcbiAgICAgIFZBTFVFUyAoPywgPywgPywgPywgPylcbiAgICBgKTtcblxuICAgIHJldHVybiBzdG10LnJ1bihvcmlnaW5hbEpvYklkLCBqb2JUeXBlLCBKU09OLnN0cmluZ2lmeShqb2JEYXRhKSwgdXNlcklkLCBmYWlsdXJlUmVhc29uKTtcbiAgfVxuXG4gIGdldERlYWRMZXR0ZXJRdWV1ZUpvYnMoc3RhdHVzID0gbnVsbCwgbGltaXQgPSA1MCwgb2Zmc2V0ID0gMCkge1xuICAgIGxldCBxdWVyeSA9IGBcbiAgICAgIFNFTEVDVCBkbHEuKiwgdS51c2VybmFtZVxuICAgICAgRlJPTSBkZWFkX2xldHRlcl9xdWV1ZSBkbHFcbiAgICAgIEpPSU4gdXNlcnMgdSBPTiBkbHEudXNlcl9pZCA9IHUuaWRcbiAgICBgO1xuXG4gICAgY29uc3QgcGFyYW1zID0gW107XG5cbiAgICBpZiAoc3RhdHVzKSB7XG4gICAgICBxdWVyeSArPSAnIFdIRVJFIGRscS5zdGF0dXMgPSA/JztcbiAgICAgIHBhcmFtcy5wdXNoKHN0YXR1cyk7XG4gICAgfVxuXG4gICAgcXVlcnkgKz0gJyBPUkRFUiBCWSBkbHEuY3JlYXRlZF9hdCBERVNDIExJTUlUID8gT0ZGU0VUID8nO1xuICAgIHBhcmFtcy5wdXNoKGxpbWl0LCBvZmZzZXQpO1xuXG4gICAgY29uc3Qgc3RtdCA9IHRoaXMuZGIucHJlcGFyZShxdWVyeSk7XG4gICAgcmV0dXJuIHN0bXQuYWxsKC4uLnBhcmFtcyk7XG4gIH1cblxuICB1cGRhdGVEZWFkTGV0dGVyUXVldWVTdGF0dXMoaWQsIHN0YXR1cykge1xuICAgIGNvbnN0IHN0bXQgPSB0aGlzLmRiLnByZXBhcmUoYFxuICAgICAgVVBEQVRFIGRlYWRfbGV0dGVyX3F1ZXVlXG4gICAgICBTRVQgc3RhdHVzID0gPywgdXBkYXRlZF9hdCA9IENVUlJFTlRfVElNRVNUQU1QXG4gICAgICBXSEVSRSBpZCA9ID9cbiAgICBgKTtcblxuICAgIHJldHVybiBzdG10LnJ1bihzdGF0dXMsIGlkKTtcbiAgfVxuXG4gIHJldHJ5RGVhZExldHRlclF1ZXVlSm9iKGlkKSB7XG4gICAgY29uc3QgZ2V0U3RtdCA9IHRoaXMuZGIucHJlcGFyZSgnU0VMRUNUICogRlJPTSBkZWFkX2xldHRlcl9xdWV1ZSBXSEVSRSBpZCA9ID8nKTtcbiAgICBjb25zdCBqb2IgPSBnZXRTdG10LmdldChpZCk7XG5cbiAgICBpZiAoIWpvYikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdEZWFkIGxldHRlciBxdWV1ZSBqb2Igbm90IGZvdW5kJyk7XG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlIG5ldyBqb2IgaW4gcXVldWVfam9icyB1c2luZyB0aGUgb3JpZ2luYWwgam9iIGRhdGFcbiAgICBjb25zdCBpbnNlcnRTdG10ID0gdGhpcy5kYi5wcmVwYXJlKGBcbiAgICAgIElOU0VSVCBJTlRPIHF1ZXVlX2pvYnMgKHVzZXJfaWQsIGpvYl90eXBlLCBqb2JfZGF0YSwgcHJpb3JpdHksIGVmZmVjdGl2ZV9wcmlvcml0eSwgc3JwX3RhcmdldCwgc3RhdHVzKVxuICAgICAgVkFMVUVTICg/LCA/LCA/LCAwLCAwLCAxMDAsICdxdWV1ZWQnKVxuICAgIGApO1xuXG4gICAgY29uc3QgcmVzdWx0ID0gaW5zZXJ0U3RtdC5ydW4oam9iLnVzZXJfaWQsIGpvYi5qb2JfdHlwZSwgam9iLmpvYl9kYXRhLCAwLCAwLCAxMDApO1xuXG4gICAgLy8gVXBkYXRlIGRlYWQgbGV0dGVyIHF1ZXVlIHN0YXR1c1xuICAgIHRoaXMudXBkYXRlRGVhZExldHRlclF1ZXVlU3RhdHVzKGlkLCAncmVzb2x2ZWQnKTtcblxuICAgIHJldHVybiByZXN1bHQubGFzdEluc2VydFJvd2lkO1xuICB9XG59XG5cbi8vIENyZWF0ZSBkYXRhIGRpcmVjdG9yeSBpZiBpdCBkb2Vzbid0IGV4aXN0XG5jb25zdCBmcyA9IHJlcXVpcmUoJ2ZzJyk7XG5jb25zdCBkYXRhRGlyID0gcGF0aC5qb2luKHByb2Nlc3MuY3dkKCksICdkYXRhJyk7XG5pZiAoIWZzLmV4aXN0c1N5bmMoZGF0YURpcikpIHtcbiAgZnMubWtkaXJTeW5jKGRhdGFEaXIsIHsgcmVjdXJzaXZlOiB0cnVlIH0pO1xufVxuXG4vLyBFeHBvcnQgc2luZ2xldG9uIGluc3RhbmNlXG5sZXQgZGJJbnN0YW5jZSA9IG51bGw7XG5cbmZ1bmN0aW9uIGdldERhdGFiYXNlKCkge1xuICBpZiAoIWRiSW5zdGFuY2UpIHtcbiAgICBkYkluc3RhbmNlID0gbmV3IERhdGFiYXNlTWFuYWdlcigpO1xuICB9XG4gIHJldHVybiBkYkluc3RhbmNlO1xufVxuXG5tb2R1bGUuZXhwb3J0cyA9IHsgZ2V0RGF0YWJhc2UsIERhdGFiYXNlTWFuYWdlciB9O1xuIl0sIm5hbWVzIjpbIkRhdGFiYXNlIiwicmVxdWlyZSIsImJjcnlwdCIsInY0IiwidXVpZHY0IiwicGF0aCIsIkRhdGFiYXNlTWFuYWdlciIsImNvbnN0cnVjdG9yIiwiZGJQYXRoIiwiam9pbiIsInByb2Nlc3MiLCJjd2QiLCJkYiIsImluaXRpYWxpemVUYWJsZXMiLCJjcmVhdGVEZWZhdWx0QWRtaW4iLCJleGVjIiwiZXJyb3IiLCJjb2x1bW5zIiwicHJlcGFyZSIsImFsbCIsImhhc01heEJhdGNoZXNQZXJEYXkiLCJzb21lIiwiY29sIiwibmFtZSIsImNvbnNvbGUiLCJsb2ciLCJiYXRjaENvbHVtbnMiLCJoYXNMb2dpblR5cGUiLCJhZG1pbkV4aXN0cyIsImdldCIsImhhc2hlZFBhc3N3b3JkIiwiaGFzaFN5bmMiLCJlbnYiLCJERUZBVUxUX0FETUlOX1BBU1NXT1JEIiwic3RtdCIsInJ1biIsIkRFRkFVTFRfQURNSU5fVVNFUk5BTUUiLCJpbml0aWFsaXplRGVmYXVsdENvbmZpZyIsImRlZmF1bHRDb25maWdzIiwia2V5IiwidmFsdWUiLCJERUZBVUxUX01BWF9NQUlOX0pPQlNfUEVSX0RBWSIsInR5cGUiLCJkZXNjcmlwdGlvbiIsIkRFRkFVTFRfTUFYX0JBVENIRVNfUEVSX0RBWSIsIkRFRkFVTFRfTUFYX1NDSEVEVUxFU19QRVJfV0VFSyIsImNvbmZpZyIsImV4aXN0aW5nU3RtdCIsImV4aXN0aW5nIiwiaW5zZXJ0U3RtdCIsImNyZWF0ZVVzZXIiLCJ1c2VybmFtZSIsInBhc3N3b3JkIiwibGljZW5zZUtleSIsInRyYW5zYWN0aW9uIiwibGljZW5zZVN0bXQiLCJsaWNlbnNlIiwiRXJyb3IiLCJEYXRlIiwiZXhwaXJlc19hdCIsImN1cnJlbnRfdXNlcyIsIm1heF91c2VzIiwidXNlckV4aXN0cyIsInVzZXJTdG10IiwicmVzdWx0IiwiaWQiLCJ1cGRhdGVMaWNlbnNlU3RtdCIsImxhc3RJbnNlcnRSb3dpZCIsImF1dGhlbnRpY2F0ZVVzZXIiLCJ1c2VyIiwiaXNWYWxpZFBhc3N3b3JkIiwiY29tcGFyZVN5bmMiLCJwYXNzd29yZF9oYXNoIiwicm9sZSIsImxpY2Vuc2VfYWN0aXZlIiwibGljZW5zZV9leHBpcmVzIiwidXBkYXRlU3RtdCIsImNyZWF0ZUxpY2Vuc2VLZXkiLCJkdXJhdGlvbkRheXMiLCJtYXhVc2VzIiwiZmVhdHVyZXMiLCJjcmVhdGVkQnkiLCJrZXlDb2RlIiwiZ2VuZXJhdGVMaWNlbnNlS2V5IiwiZXhwaXJlc0F0Iiwic2V0RGF0ZSIsImdldERhdGUiLCJ0b0lTT1N0cmluZyIsIkpTT04iLCJzdHJpbmdpZnkiLCJzZWdtZW50cyIsImkiLCJwdXNoIiwicmVwbGFjZSIsInN1YnN0cmluZyIsInRvVXBwZXJDYXNlIiwiZ2V0TGljZW5zZUtleXMiLCJsaW1pdCIsIm9mZnNldCIsImRlYWN0aXZhdGVMaWNlbnNlS2V5Iiwia2V5SWQiLCJnZXRVc2VyTGljZW5zZVN0YXR1cyIsInVzZXJJZCIsInZhbGlkYXRlTGljZW5zZUZvclJlbmV3YWwiLCJ2YWxpZCIsInN0YXR1cyIsImVycm9yTWVzc2FnZSIsInJlbmV3VXNlckxpY2Vuc2UiLCJuZXdMaWNlbnNlS2V5IiwidmFsaWRhdGlvbiIsIm5ld0xpY2Vuc2UiLCJ1cGRhdGVVc2VyU3RtdCIsImxvZ0FjdGl2aXR5Iiwic3VjY2VzcyIsIm5ld0xpY2Vuc2VJZCIsImN1cnJlbnRVc2VzIiwiY3JlYXRlU2Vzc2lvbiIsInRva2VuSGFzaCIsImlwQWRkcmVzcyIsInVzZXJBZ2VudCIsInZhbGlkYXRlU2Vzc2lvbiIsImludmFsaWRhdGVTZXNzaW9uIiwiaW52YWxpZGF0ZUFsbFVzZXJTZXNzaW9ucyIsImFjdGlvbiIsImRldGFpbHMiLCJjbGVhbnVwRXhwaXJlZEtleXMiLCJjaGFuZ2VzIiwiZ2V0QWN0aXZpdHlMb2dzIiwicXVlcnkiLCJwYXJhbXMiLCJnZXRTeXN0ZW1TdGF0cyIsInN0YXRzIiwidG90YWxVc2VycyIsImNvdW50IiwiYWN0aXZlVXNlcnMiLCJ0b3RhbExpY2Vuc2VLZXlzIiwiYWN0aXZlTGljZW5zZUtleXMiLCJleHBpcmVkTGljZW5zZUtleXMiLCJyZWNlbnRBY3Rpdml0eSIsImdldFVzZXJzIiwidG9nZ2xlVXNlclN0YXR1cyIsImNsZWFudXBFeHBpcmVkU2Vzc2lvbnMiLCJjbGVhbnVwT2xkTG9ncyIsImRheXNUb0tlZXAiLCJzYXZlRW5jcnlwdGVkQ3JlZGVudGlhbHMiLCJsb2dpbk1ldGhvZCIsInNjaG9vbCIsImVtYWlsIiwiY3J5cHRvIiwic2VjcmV0IiwiQ1JFREVOVElBTF9FTkNSWVBUSU9OX1NFQ1JFVCIsImxvZ2luS2V5IiwicmFuZG9tQnl0ZXMiLCJ0b1N0cmluZyIsIml2Iiwic2NyeXB0U3luYyIsIlN0cmluZyIsImNpcGhlcjEiLCJjcmVhdGVDaXBoZXJpdiIsImVuY3J5cHRlZFNjaG9vbCIsInVwZGF0ZSIsImZpbmFsIiwiY2lwaGVyMiIsImVuY3J5cHRlZEVtYWlsIiwiY2lwaGVyMyIsImVuY3J5cHRlZFBhc3N3b3JkIiwiZ2V0RW5jcnlwdGVkQ3JlZGVudGlhbHMiLCJ1c2VyX2lkIiwiQnVmZmVyIiwiZnJvbSIsImVuY3J5cHRpb25faXYiLCJkZWNpcGhlcjEiLCJjcmVhdGVEZWNpcGhlcml2IiwiZW5jcnlwdGVkX3NjaG9vbCIsImRlY2lwaGVyMiIsImVuY3J5cHRlZF9lbWFpbCIsImRlY2lwaGVyMyIsImVuY3J5cHRlZF9wYXNzd29yZCIsImxvZ2luX21ldGhvZCIsImdldFVzZXJDcmVkZW50aWFscyIsImRlYWN0aXZhdGVDcmVkZW50aWFscyIsInNldExpY2Vuc2VGZWF0dXJlcyIsImxpY2Vuc2VLZXlJZCIsIm1heF9hY2NvdW50c19wZXJfYmF0Y2giLCJwcmlvcml0eV9sZXZlbCIsInNjaGVkdWxpbmdfYWNjZXNzIiwibWF4X2JhdGNoZXNfcGVyX2RheSIsImdldExpY2Vuc2VGZWF0dXJlcyIsIkJvb2xlYW4iLCJnZXRVc2VyTGljZW5zZUZlYXR1cmVzIiwiZ2V0VXNlckRhaWx5QmF0Y2hDb3VudCIsImRhdGUiLCJ0YXJnZXREYXRlIiwic3BsaXQiLCJnZXRVc2VyV2Vla2x5U2NoZWR1bGVDb3VudCIsIm5vdyIsImRheU9mV2VlayIsImdldERheSIsImRheXNUb01vbmRheSIsInN0YXJ0T2ZXZWVrIiwic2V0SG91cnMiLCJjcmVhdGVRdWV1ZUJhdGNoIiwiYmF0Y2hOYW1lIiwiYWNjb3VudHMiLCJzY2hlZHVsZWRUaW1lIiwibG9naW5UeXBlIiwic3JwVGFyZ2V0IiwiZGFpbHlCYXRjaENvdW50IiwibGVuZ3RoIiwiaW5jbHVkZXMiLCJiYXRjaFN0bXQiLCJiYXRjaFJlc3VsdCIsImJhdGNoSWQiLCJqb2JTdG10IiwiZm9yRWFjaCIsImFjY291bnQiLCJlZmZlY3RpdmVQcmlvcml0eSIsImNhbGN1bGF0ZUVmZmVjdGl2ZVByaW9yaXR5IiwiY3JlYXRlU2NoZWR1bGVFbnRyeSIsImJhc2VQcmlvcml0eSIsInNjaGVkdWxlZCIsInRpbWVEaWZmIiwiZ2V0VGltZSIsImhvdXJzVW50aWwiLCJNYXRoIiwibWluIiwiZ2V0UXVldWVCYXRjaGVzIiwiZ2V0QmF0Y2hKb2JzIiwidXBkYXRlQmF0Y2hTdGF0dXMiLCJjb21wbGV0ZWRBdCIsImNyZWF0ZVF1ZXVlSm9iIiwiam9iX3R5cGUiLCJqb2JfZGF0YSIsInNycF90YXJnZXQiLCJwcmlvcml0eSIsImdldFF1ZXVlSm9icyIsImdldE5leHRRdWV1ZUpvYiIsInVwZGF0ZUpvYlN0YXR1cyIsImpvYklkIiwic3RhcnRlZEF0IiwiaW5jcmVtZW50Sm9iUmV0cnkiLCJkdXJhdGlvbk1pbnV0ZXMiLCJjb25mbGljdHMiLCJjaGVja1NjaGVkdWxlQ29uZmxpY3RzIiwic3RhcnRUaW1lIiwiZW5kVGltZSIsImdldFVzZXJTY2hlZHVsZXMiLCJzdGFydERhdGUiLCJlbmREYXRlIiwidXBkYXRlSm9iUHJpb3JpdHkiLCJuZXdQcmlvcml0eSIsImFkbWluT3ZlcnJpZGUiLCJhcHBseVN0YXJ2YXRpb25QcmV2ZW50aW9uIiwiZ2V0UXVldWVTdGF0cyIsInN0YXR1c1N0bXQiLCJqb2JzQnlTdGF0dXMiLCJwcmlvcml0eVN0bXQiLCJqb2JzQnlQcmlvcml0eSIsIndhaXRUaW1lU3RtdCIsImF2ZXJhZ2VXYWl0VGltZSIsImF2Z193YWl0X21pbnV0ZXMiLCJjbG9zZSIsImdldFN5c3RlbUNvbmZpZyIsImNvbmZpZ190eXBlIiwiY29uZmlnX3ZhbHVlIiwicGFyc2VJbnQiLCJwYXJzZSIsInNldFN5c3RlbUNvbmZpZyIsInN0cmluZ1ZhbHVlIiwiZ2V0QWxsU3lzdGVtQ29uZmlnIiwiY29uZmlncyIsIm1hcCIsImFkZFRvRGVhZExldHRlclF1ZXVlIiwib3JpZ2luYWxKb2JJZCIsImpvYlR5cGUiLCJqb2JEYXRhIiwiZmFpbHVyZVJlYXNvbiIsImdldERlYWRMZXR0ZXJRdWV1ZUpvYnMiLCJ1cGRhdGVEZWFkTGV0dGVyUXVldWVTdGF0dXMiLCJyZXRyeURlYWRMZXR0ZXJRdWV1ZUpvYiIsImdldFN0bXQiLCJqb2IiLCJmcyIsImRhdGFEaXIiLCJleGlzdHNTeW5jIiwibWtkaXJTeW5jIiwicmVjdXJzaXZlIiwiZGJJbnN0YW5jZSIsImdldERhdGFiYXNlIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/next","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/uuid","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fqueue%2Fjobs%2Froute&page=%2Fapi%2Fqueue%2Fjobs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fqueue%2Fjobs%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();