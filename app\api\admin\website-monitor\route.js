import { NextResponse } from 'next/server';
import { getWebhookManager } from '../../../../lib/webhook';

// GET - Get website monitoring status
export async function GET(request) {
  try {
    const webhook = getWebhookManager();
    
    const status = await webhook.checkWebsiteStatus();
    
    return NextResponse.json({
      success: true,
      monitoring: {
        isActive: webhook.isMonitoring,
        website: webhook.websiteUrl,
        lastStatus: webhook.lastWebsiteStatus,
        currentStatus: status
      }
    });
  } catch (error) {
    console.error('Website monitor status error:', error);
    return NextResponse.json({
      error: 'Failed to get monitoring status',
      details: error.message
    }, { status: 500 });
  }
}

// POST - Control website monitoring
export async function POST(request) {
  try {
    const { action, intervalMinutes } = await request.json();
    const webhook = getWebhookManager();
    
    switch (action) {
      case 'start':
        await webhook.startWebsiteMonitoring(intervalMinutes || 5);
        return NextResponse.json({
          success: true,
          message: `Website monitoring started (checking every ${intervalMinutes || 5} minutes)`
        });
        
      case 'stop':
        await webhook.stopWebsiteMonitoring();
        return NextResponse.json({
          success: true,
          message: 'Website monitoring stopped'
        });
        
      case 'check':
        const status = await webhook.checkWebsiteStatus();
        await webhook.sendWebsiteStatusUpdate(status, webhook.lastWebsiteStatus);
        return NextResponse.json({
          success: true,
          message: 'Manual website check performed',
          status
        });
        
      case 'test_error':
        // Test error reporting
        const testError = new Error('This is a test error for Discord webhook');
        testError.stack = 'Test stack trace\n  at testFunction (test.js:1:1)';
        await webhook.sendErrorReport('Test Error Report', testError, {
          test: true,
          timestamp: new Date().toISOString(),
          source: 'manual_test'
        });
        return NextResponse.json({
          success: true,
          message: 'Test error report sent to Discord'
        });
        
      default:
        return NextResponse.json({
          error: 'Invalid action. Use: start, stop, check, or test_error'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Website monitor control error:', error);
    return NextResponse.json({
      error: 'Failed to control monitoring',
      details: error.message
    }, { status: 500 });
  }
}
