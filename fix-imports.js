const fs = require('fs');
const path = require('path');

// List of all files that need to be fixed
const filesToFix = [
  'app/api/admin/addons/route.js',
  'app/api/admin/analytics/route.js',
  'app/api/admin/daily-limits/route.js',
  'app/api/admin/dead-letter-queue/route.js',
  'app/api/admin/keys/route.js',
  'app/api/admin/license-config/route.js',
  'app/api/admin/queue-config/route.js',
  'app/api/admin/queue-health/route.js',
  'app/api/admin/users/route.js',
  'app/api/auth/login/route.js',
  'app/api/auth/logout/route.js',
  'app/api/auth/register/route.js',
  'app/api/auth/renew-license/route.js',
  'app/api/auth/validate/route.js',
  'app/api/credentials/get/route.js',
  'app/api/credentials/list/route.js',
  'app/api/credentials/save/route.js',
  'app/api/queue/batch/route.js',
  'app/api/queue/jobs/route.js',
  'app/api/queue/position/route.js',
  'app/api/queue/priority-levels/route.js',
  'app/api/queue/schedule/route.js',
  'app/api/queue/status/route.js',
  'app/api/queue/jobs/[id]/route.js'
];

// Import patterns to replace
const importReplacements = [
  {
    pattern: /import\s*{\s*getAuthManager\s*}\s*from\s*['"][^'"]*lib\/auth['"];?/g,
    replacement: "const { getAuthManager } = require('../../../../lib/auth');"
  },
  {
    pattern: /import\s*{\s*getDatabase\s*}\s*from\s*['"][^'"]*lib\/database['"];?/g,
    replacement: "const { getDatabase } = require('../../../../lib/database');"
  },
  {
    pattern: /import\s*{\s*getWebhookManager\s*}\s*from\s*['"][^'"]*lib\/webhook['"];?/g,
    replacement: "const { getWebhookManager } = require('../../../../lib/webhook');"
  },
  {
    pattern: /import\s*{\s*getQueueProcessor\s*}\s*from\s*['"][^'"]*lib\/queueProcessor['"];?/g,
    replacement: "const { getQueueProcessor } = require('../../../../lib/queueProcessor');"
  },
  {
    pattern: /import\s*QueueMiddleware\s*from\s*['"][^'"]*lib\/queueMiddleware['"];?/g,
    replacement: "const QueueMiddleware = require('../../../../lib/queueMiddleware');"
  }
];

function fixFile(filePath) {
  try {
    const fullPath = path.join(__dirname, filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return;
    }
    
    let content = fs.readFileSync(fullPath, 'utf8');
    let modified = false;
    
    // Apply each replacement
    importReplacements.forEach(({ pattern, replacement }) => {
      if (pattern.test(content)) {
        content = content.replace(pattern, replacement);
        modified = true;
      }
    });
    
    if (modified) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
    } else {
      console.log(`ℹ️  No changes needed: ${filePath}`);
    }
    
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
  }
}

console.log('🔧 Fixing import statements in API routes...\n');

filesToFix.forEach(fixFile);

console.log('\n🎉 Import fix process completed!');
