import { NextResponse } from 'next/server';
const { getDatabase } = require('../../../../lib/database');
const { getAuthManager } = require('../../../../lib/auth');

export async function GET(request) {
  try {
    const auth = getAuthManager();
    
    // Check authentication and admin role
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const session = auth.validateSession(token);

    if (session.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const db = getDatabase();
    
    // Get system statistics
    const stats = db.getSystemStats();
    
    // Get recent activity logs
    const recentActivity = db.getActivityLogs(null, 20, 0);

    return NextResponse.json({
      success: true,
      stats,
      recentActivity
    });

  } catch (error) {
    console.error('Get analytics error:', error);
    return NextResponse.json(
      { success: false, error: error.message },
      { status: error.message === 'Invalid session' ? 401 : 500 }
    );
  }
}