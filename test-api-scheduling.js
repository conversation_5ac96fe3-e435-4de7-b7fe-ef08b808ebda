const fetch = require('node-fetch');
const { getDatabase } = require('./lib/database');

async function testApiScheduling() {
  console.log('🌐 Testing API Scheduling Endpoints...\n');
  
  try {
    const db = getDatabase();
    
    // Find the test user
    const testUser = db.db.prepare(`
      SELECT * FROM users 
      WHERE username LIKE 'queuetest_%' 
      ORDER BY created_at DESC 
      LIMIT 1
    `).get();
    
    if (!testUser) {
      console.log('❌ No test user found. Run test-queue-system.js first.');
      return;
    }
    
    console.log(`📋 Testing with user: ${testUser.username}`);
    
    // Create a session for the test user
    console.log('\n1. Creating test session...');
    const token = db.createSession(testUser.id);
    console.log(`   Session token created: ${token.substring(0, 20)}...`);
    
    // Test the queue status API
    console.log('\n2. Testing /api/queue/status endpoint...');
    try {
      const response = await fetch('http://localhost:3001/api/queue/status?detailed=true', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('   ✅ API call successful');
        console.log(`   Response license_features:`, data.license_features);
        
        if (data.license_features && data.license_features.scheduling_access) {
          console.log('   ✅ scheduling_access is true in API response');
        } else {
          console.log('   ❌ scheduling_access is false or missing in API response');
        }
      } else {
        const error = await response.json();
        console.log(`   ❌ API call failed: ${response.status} - ${error.error}`);
      }
    } catch (error) {
      console.log(`   ❌ API call error: ${error.message}`);
    }
    
    // Test the schedule API
    console.log('\n3. Testing /api/queue/schedule endpoint...');
    try {
      const response = await fetch('http://localhost:3001/api/queue/schedule', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('   ✅ Schedule API call successful');
        console.log(`   License info:`, data.license_info);
        
        if (data.license_info && data.license_info.scheduling_access) {
          console.log('   ✅ scheduling_access is true in schedule API response');
        } else {
          console.log('   ❌ scheduling_access is false or missing in schedule API response');
        }
      } else {
        const error = await response.json();
        console.log(`   ❌ Schedule API call failed: ${response.status} - ${error.error}`);
      }
    } catch (error) {
      console.log(`   ❌ Schedule API call error: ${error.message}`);
    }
    
    console.log('\n🎉 API scheduling test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

if (require.main === module) {
  testApiScheduling();
}

module.exports = { testApiScheduling };
