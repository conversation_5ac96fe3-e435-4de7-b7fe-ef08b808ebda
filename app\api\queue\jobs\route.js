import { NextResponse } from 'next/server';
const { getDatabase } = require('../../../../lib/database');
const { getAuthManager } = require('../../../../lib/auth');

// Middleware function for authentication
async function withMiddleware(request, handler) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Missing or invalid authorization header' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const authManager = getAuthManager();
    const user = await authManager.verifyToken(token);
    
    if (!user) {
      return NextResponse.json({ error: 'Invalid or expired token' }, { status: 401 });
    }

    return await handler(request, user);
  } catch (error) {
    console.error('Middleware error:', error);
    return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
  }
}

// POST /api/queue/jobs - Create a new queue job
export async function POST(request) {
  return withMiddleware(request, async (request, user) => {
    try {
      const body = await request.json();
      const { job_type, job_data, srp_target = 100, priority = 0 } = body;

      // Validate required fields
      if (!job_type || !job_data) {
        return NextResponse.json({ 
          success: false, 
          error: 'Missing required fields: job_type and job_data' 
        }, { status: 400 });
      }

      // Validate job_data has required fields
      const { school, email, password, login_type, bookTitle } = job_data;
      if (!school || !email || !password || !login_type || !bookTitle) {
        return NextResponse.json({ 
          success: false, 
          error: 'Missing required job_data fields: school, email, password, login_type, bookTitle' 
        }, { status: 400 });
      }

      // Validate SRP target
      if (srp_target < 1 || srp_target > 400) {
        return NextResponse.json({ 
          success: false, 
          error: 'SRP target must be between 1 and 400' 
        }, { status: 400 });
      }

      const db = getDatabase();

      // Check daily main job limit using system config
      const mainJobLimitConfig = db.getSystemConfig('default_max_main_jobs_per_day');
      const maxMainJobsPerDay = mainJobLimitConfig ? mainJobLimitConfig.config_value : 5;

      const today = new Date().toISOString().split('T')[0];
      const mainJobCountStmt = db.db.prepare(`
        SELECT COUNT(*) as count
        FROM queue_jobs
        WHERE user_id = ? AND DATE(created_at) = ? AND batch_id IS NULL AND schedule_id IS NULL
      `);
      const mainJobCount = mainJobCountStmt.get(user.id, today);

      if (mainJobCount.count >= maxMainJobsPerDay) {
        return NextResponse.json({
          success: false,
          error: 'Daily main job limit reached',
          current_count: mainJobCount.count,
          max_allowed: maxMainJobsPerDay,
          message: `You have reached your daily limit of ${maxMainJobsPerDay} main automation job${maxMainJobsPerDay > 1 ? 's' : ''}. Please try again tomorrow.`
        }, { status: 429 });
      }

      // Create the queue job
      const jobId = db.createQueueJob({
        username: user.username,
        job_type,
        job_data: JSON.stringify(job_data),
        srp_target,
        priority,
        status: 'queued'
      });

      console.log(`✅ Queue job created: ID ${jobId} for user ${user.username}`);

      return NextResponse.json({ 
        success: true, 
        job_id: jobId,
        message: 'Job added to queue successfully'
      });

    } catch (error) {
      console.error('Error creating queue job:', error);
      return NextResponse.json({ 
        success: false, 
        error: 'Failed to create queue job' 
      }, { status: 500 });
    }
  });
}

// GET /api/queue/jobs - Get user's queue jobs
export async function GET(request) {
  return withMiddleware(request, async (request, user) => {
    try {
      const db = getDatabase();
      const jobs = db.getQueueJobs().filter(job => job.username === user.username);

      return NextResponse.json({ 
        success: true, 
        jobs: jobs.map(job => ({
          id: job.id,
          job_type: job.job_type,
          status: job.status,
          priority: job.priority,
          srp_target: job.srp_target,
          created_at: job.created_at,
          started_at: job.started_at,
          completed_at: job.completed_at,
          retry_count: job.retry_count,
          max_retries: job.max_retries
        }))
      });

    } catch (error) {
      console.error('Error fetching queue jobs:', error);
      return NextResponse.json({ 
        success: false, 
        error: 'Failed to fetch queue jobs' 
      }, { status: 500 });
    }
  });
}
