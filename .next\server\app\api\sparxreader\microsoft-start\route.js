"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/sparxreader/microsoft-start/route";
exports.ids = ["app/api/sparxreader/microsoft-start/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "playwright":
/*!*****************************!*\
  !*** external "playwright" ***!
  \*****************************/
/***/ ((module) => {

module.exports = import("playwright");;

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute&page=%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute&page=%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var D_reader_auto_main_app_api_sparxreader_microsoft_start_route_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./app/api/sparxreader/microsoft-start/route.js */ \"(rsc)/./app/api/sparxreader/microsoft-start/route.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_reader_auto_main_app_api_sparxreader_microsoft_start_route_js__WEBPACK_IMPORTED_MODULE_2__]);\nD_reader_auto_main_app_api_sparxreader_microsoft_start_route_js__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/sparxreader/microsoft-start/route\",\n        pathname: \"/api/sparxreader/microsoft-start\",\n        filename: \"route\",\n        bundlePath: \"app/api/sparxreader/microsoft-start/route\"\n    },\n    resolvedPagePath: \"D:\\\\reader-auto-main\\\\app\\\\api\\\\sparxreader\\\\microsoft-start\\\\route.js\",\n    nextConfigOutput,\n    userland: D_reader_auto_main_app_api_sparxreader_microsoft_start_route_js__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/sparxreader/microsoft-start/route\";\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute&page=%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/sparxreader/browser-context.js":
/*!************************************************!*\
  !*** ./app/api/sparxreader/browser-context.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearGlobalBrowser: () => (/* binding */ clearGlobalBrowser),\n/* harmony export */   getGlobalBrowser: () => (/* binding */ getGlobalBrowser),\n/* harmony export */   getGlobalPage: () => (/* binding */ getGlobalPage),\n/* harmony export */   setGlobalBrowser: () => (/* binding */ setGlobalBrowser)\n/* harmony export */ });\n// Global browser context storage using globalThis for persistence\nif (!globalThis.sparxBrowserContext) {\n    globalThis.sparxBrowserContext = {\n        browser: null,\n        page: null\n    };\n}\nfunction setGlobalBrowser(browser, page) {\n    globalThis.sparxBrowserContext.browser = browser;\n    globalThis.sparxBrowserContext.page = page;\n    console.log(\"Browser context set:\", !!browser, !!page);\n}\nfunction getGlobalBrowser() {\n    return globalThis.sparxBrowserContext.browser;\n}\nfunction getGlobalPage() {\n    return globalThis.sparxBrowserContext.page;\n}\nfunction clearGlobalBrowser() {\n    globalThis.sparxBrowserContext.browser = null;\n    globalThis.sparxBrowserContext.page = null;\n    console.log(\"Browser context cleared\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/sparxreader/browser-context.js\n");

/***/ }),

/***/ "(rsc)/./app/api/sparxreader/microsoft-start/route.js":
/*!******************************************************!*\
  !*** ./app/api/sparxreader/microsoft-start/route.js ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var playwright__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! playwright */ \"playwright\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _browser_context_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../browser-context.js */ \"(rsc)/./app/api/sparxreader/browser-context.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([playwright__WEBPACK_IMPORTED_MODULE_1__]);\nplaywright__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nasync function POST(request) {\n    try {\n        const { url, targetSrp, credentials } = await request.json();\n        const extensionPath = path__WEBPACK_IMPORTED_MODULE_2___default().resolve(process.cwd(), \"Sparxext reader\");\n        try {\n            // Always create new browser session for first try\n            const existingBrowser = (0,_browser_context_js__WEBPACK_IMPORTED_MODULE_3__.getGlobalBrowser)();\n            // Close any existing browser session\n            if (existingBrowser) {\n                try {\n                    await existingBrowser.close();\n                    console.log(\"Closed existing browser session\");\n                } catch (error) {\n                    console.log(\"Error closing existing browser:\", error.message);\n                }\n            }\n            // Create new browser session\n            console.log(\"Creating new browser session...\");\n            const browser = await playwright__WEBPACK_IMPORTED_MODULE_1__.chromium.launchPersistentContext(\"\", {\n                headless: false,\n                args: [\n                    `--disable-extensions-except=${extensionPath}`,\n                    `--load-extension=${extensionPath}`,\n                    \"--no-sandbox\",\n                    \"--disable-setuid-sandbox\"\n                ]\n            });\n            const page = await browser.newPage();\n            // Store globally for use in other endpoints\n            (0,_browser_context_js__WEBPACK_IMPORTED_MODULE_3__.setGlobalBrowser)(browser, page);\n            console.log(\"Navigating to Sparx Learning...\");\n            await page.goto(url, {\n                timeout: 15000\n            });\n            console.log(\"Selecting school\");\n            try {\n                // Use school from credentials\n                if (!credentials || !credentials.school) {\n                    throw new Error(\"School name is required\");\n                }\n                await page.type('input[type=\"text\"], input[type=\"search\"], input', credentials.school);\n                await page.press('input[type=\"text\"], input[type=\"search\"], input', \"Enter\");\n                await page.waitForTimeout(1000);\n                try {\n                    await page.click('button:has-text(\"Continue\")', {\n                        timeout: 5000\n                    });\n                } catch (error) {\n                    console.log(\"Continue button not found, proceeding anyway\");\n                }\n                await page.waitForTimeout(2000);\n                try {\n                    await page.click(\"div#cookiescript_accept\", {\n                        timeout: 5000\n                    });\n                } catch (error) {\n                    console.log(\"Cookie accept button not found, proceeding anyway\");\n                }\n            } catch (schoolError) {\n                console.log(\"School selection error:\", schoolError.message);\n            }\n            await page.waitForTimeout(3000);\n            // Click the Microsoft SSO login button\n            const microsoftButton = page.locator('div.sso-login-button[onclick*=\"oauth2/login\"]');\n            if (await microsoftButton.count() > 0) {\n                console.log(\"Found Microsoft login button, clicking...\");\n                await microsoftButton.click();\n            } else {\n                console.log(\"Microsoft login button not found, trying alternative selectors...\");\n                const altSelectors = [\n                    'a[href*=\"microsoftonline.com\"]',\n                    'button:has-text(\"Microsoft\")',\n                    '[data-provider=\"microsoft\"]',\n                    \".microsoft-login\"\n                ];\n                let found = false;\n                for (const selector of altSelectors){\n                    const element = page.locator(selector);\n                    if (await element.count() > 0) {\n                        console.log(`Found Microsoft login with selector: ${selector}`);\n                        await element.click();\n                        found = true;\n                        break;\n                    }\n                }\n                if (!found) {\n                    throw new Error(\"Microsoft login button not found\");\n                }\n            }\n            await page.waitForTimeout(3000);\n            // Input email address\n            console.log(\"Inputting email address...\");\n            // Use credentials from request\n            if (!credentials || !credentials.email || !credentials.password) {\n                throw new Error(\"Login credentials are required\");\n            }\n            await page.type('input[type=\"email\"], input[name=\"loginfmt\"], input[placeholder*=\"email\"]', credentials.email);\n            // Wait and click Next button\n            await page.waitForTimeout(1000);\n            console.log(\"Clicking Next button...\");\n            await page.click('button:has-text(\"Next\"), input[type=\"submit\"][value=\"Next\"], button[id*=\"next\"]');\n            // Wait and input password\n            await page.waitForTimeout(4000);\n            console.log(\"Inputting password...\");\n            await page.type('input[type=\"password\"], input[name=\"passwd\"], input[placeholder*=\"password\"]', credentials.password);\n            // Wait and click Sign in button\n            await page.waitForTimeout(1000);\n            console.log(\"Clicking Sign in button...\");\n            await page.click('button:has-text(\"Sign in\"), input[type=\"submit\"][value=\"Sign in\"], button[id*=\"signin\"]');\n            // Wait and click No button\n            await page.waitForTimeout(2000);\n            console.log(\"Clicking No button...\");\n            await page.click('button:has-text(\"No\"), input[type=\"button\"][value=\"No\"], button[id*=\"no\"]');\n            // Wait for login completion and navigate to library\n            console.log(\"Waiting for login completion...\");\n            await page.waitForTimeout(5000);\n            console.log(\"Navigating to Sparx Reader library page...\");\n            await page.goto(\"https://reader.sparx-learning.com/library\", {\n                waitUntil: \"domcontentloaded\",\n                timeout: 30000\n            });\n            await page.waitForTimeout(5000);\n            // Extract user's total SRP using enhanced function from start route\n            console.log(\"Extracting user total SRP...\");\n            let userTotalSrp = null;\n            // Try to get user total SRP with multiple attempts\n            for(let attempt = 0; attempt < 5; attempt++){\n                try {\n                    userTotalSrp = await page.evaluate(()=>{\n                        // Get the user's total SRP\n                        const userTotalSrpElement = document.querySelector(\".sr_92b39de6\");\n                        return userTotalSrpElement ? userTotalSrpElement.textContent.replace(/[^\\d,]/g, \"\").replace(\",\", \"\") : null;\n                    });\n                    if (userTotalSrp) {\n                        console.log(`User Total SRP extracted on attempt ${attempt + 1}: ${userTotalSrp}`);\n                        break;\n                    }\n                    // If we didn't get the info, wait a short time and try again\n                    if (attempt < 4) {\n                        await page.waitForTimeout(200);\n                    }\n                } catch (error) {\n                    console.log(`SRP extraction attempt ${attempt + 1} failed:`, error.message);\n                    if (attempt < 4) {\n                        await page.waitForTimeout(200);\n                    }\n                }\n            }\n            // Store initial SRP info everywhere im pretty sure and in browser localStorage\n            global.sessionSrpInfo = {\n                initialUserTotalSrp: userTotalSrp,\n                targetSrpNeeded: targetSrp || null\n            };\n            // Store target SRP and initial SRP in browser localStorage so extension can do its thang\n            if (targetSrp) {\n                await page.evaluate(({ target, initial })=>{\n                    localStorage.setItem(\"targetSrp\", target.toString());\n                    localStorage.setItem(\"initialSrp\", initial || \"0\");\n                }, {\n                    target: targetSrp,\n                    initial: userTotalSrp\n                });\n                console.log(`Target SRP stored in localStorage: ${targetSrp}`);\n                console.log(`Initial SRP stored in localStorage: ${userTotalSrp}`);\n            }\n            console.log(\"Microsoft login setup complete.\");\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                currentSrp: userTotalSrp || \"0\",\n                bookTitle: \"Microsoft Login - Ready\",\n                message: \"Microsoft login initiated successfully\"\n            });\n        } catch (playwrightError) {\n            if (playwrightError.message.includes(\"Executable doesn't exist\")) {\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    success: false,\n                    error: \"Playwright browsers not installed. Please run 'npx playwright install chromium' in your terminal.\",\n                    needsPlaywright: true\n                }, {\n                    status: 500\n                });\n            } else {\n                throw playwrightError;\n            }\n        }\n    } catch (error) {\n        console.error(\"Error opening Sparx Reader with extension:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/sparxreader/microsoft-start/route.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute&page=%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();