"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/sparxreader/microsoft-start/route";
exports.ids = ["app/api/sparxreader/microsoft-start/route"];
exports.modules = {

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("better-sqlite3");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "playwright":
/*!*****************************!*\
  !*** external "playwright" ***!
  \*****************************/
/***/ ((module) => {

module.exports = import("playwright");;

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute&page=%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute&page=%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var D_reader_auto_main_app_api_sparxreader_microsoft_start_route_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./app/api/sparxreader/microsoft-start/route.js */ \"(rsc)/./app/api/sparxreader/microsoft-start/route.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([D_reader_auto_main_app_api_sparxreader_microsoft_start_route_js__WEBPACK_IMPORTED_MODULE_2__]);\nD_reader_auto_main_app_api_sparxreader_microsoft_start_route_js__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/sparxreader/microsoft-start/route\",\n        pathname: \"/api/sparxreader/microsoft-start\",\n        filename: \"route\",\n        bundlePath: \"app/api/sparxreader/microsoft-start/route\"\n    },\n    resolvedPagePath: \"D:\\\\reader-auto-main\\\\app\\\\api\\\\sparxreader\\\\microsoft-start\\\\route.js\",\n    nextConfigOutput,\n    userland: D_reader_auto_main_app_api_sparxreader_microsoft_start_route_js__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/sparxreader/microsoft-start/route\";\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute&page=%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/sparxreader/browser-context.js":
/*!************************************************!*\
  !*** ./app/api/sparxreader/browser-context.js ***!
  \************************************************/
/***/ ((module) => {

eval("// Global browser context storage using globalThis for persistence\n\nif (!globalThis.sparxBrowserContext) {\n    globalThis.sparxBrowserContext = {\n        browser: null,\n        page: null\n    };\n}\nfunction setGlobalBrowser(browser, page) {\n    globalThis.sparxBrowserContext.browser = browser;\n    globalThis.sparxBrowserContext.page = page;\n    console.log(\"Browser context set:\", !!browser, !!page);\n}\nfunction getGlobalBrowser() {\n    return globalThis.sparxBrowserContext.browser;\n}\nfunction getGlobalPage() {\n    return globalThis.sparxBrowserContext.page;\n}\nfunction clearGlobalBrowser() {\n    globalThis.sparxBrowserContext.browser = null;\n    globalThis.sparxBrowserContext.page = null;\n    console.log(\"Browser context cleared\");\n}\nmodule.exports = {\n    setGlobalBrowser,\n    getGlobalBrowser,\n    getGlobalPage,\n    clearGlobalBrowser\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/sparxreader/browser-context.js\n");

/***/ }),

/***/ "(rsc)/./app/api/sparxreader/microsoft-start/route.js":
/*!******************************************************!*\
  !*** ./app/api/sparxreader/microsoft-start/route.js ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var playwright__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! playwright */ \"playwright\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([playwright__WEBPACK_IMPORTED_MODULE_1__]);\nplaywright__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst { setGlobalBrowser, getGlobalBrowser, getGlobalPage, clearGlobalBrowser } = __webpack_require__(/*! ../browser-context.js */ \"(rsc)/./app/api/sparxreader/browser-context.js\");\nconst { getAuthManager } = __webpack_require__(/*! ../../../../lib/auth */ \"(rsc)/./lib/auth.js\");\nconst { getDatabase } = __webpack_require__(/*! ../../../../lib/database */ \"(rsc)/./lib/database.js\");\n// Authentication and validation middleware\nasync function withAuth(request, handler) {\n    try {\n        const auth = getAuthManager();\n        // Get token from header\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"No token provided\"\n            }, {\n                status: 401\n            });\n        }\n        const token = authHeader.substring(7);\n        const session = auth.validateSession(token);\n        if (!session) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid session\"\n            }, {\n                status: 401\n            });\n        }\n        // Check daily main job limit\n        const db = getDatabase();\n        const mainJobLimitConfig = db.getSystemConfig(\"default_max_main_jobs_per_day\");\n        const maxMainJobsPerDay = mainJobLimitConfig ? mainJobLimitConfig.config_value : 5;\n        const today = new Date().toISOString().split(\"T\")[0];\n        const mainJobCountStmt = db.db.prepare(`\n      SELECT COUNT(*) as count\n      FROM queue_jobs\n      WHERE user_id = ? AND DATE(created_at) = ? AND batch_id IS NULL\n    `);\n        const mainJobCount = mainJobCountStmt.get(session.id, today);\n        if (mainJobCount.count >= maxMainJobsPerDay) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Daily main job limit reached\",\n                current_count: mainJobCount.count,\n                max_allowed: maxMainJobsPerDay,\n                message: `You have reached your daily limit of ${maxMainJobsPerDay} main automation job${maxMainJobsPerDay > 1 ? \"s\" : \"\"}. Please try again tomorrow.`\n            }, {\n                status: 429\n            });\n        }\n        return handler(request, session);\n    } catch (error) {\n        console.error(\"Auth error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Authentication failed\"\n        }, {\n            status: 401\n        });\n    }\n}\nasync function POST(request) {\n    return withAuth(request, async (request, user)=>{\n        try {\n            const { url, targetSrp, credentials } = await request.json();\n            const extensionPath = path__WEBPACK_IMPORTED_MODULE_2___default().resolve(process.cwd(), \"Sparxext reader\");\n            try {\n                // Always create new browser session for first try\n                const existingBrowser = getGlobalBrowser();\n                // Close any existing browser session\n                if (existingBrowser) {\n                    try {\n                        await existingBrowser.close();\n                        console.log(\"Closed existing browser session\");\n                    } catch (error) {\n                        console.log(\"Error closing existing browser:\", error.message);\n                    }\n                }\n                // Create new browser session\n                console.log(\"Creating new browser session...\");\n                const browser = await playwright__WEBPACK_IMPORTED_MODULE_1__.chromium.launchPersistentContext(\"\", {\n                    headless: false,\n                    args: [\n                        `--disable-extensions-except=${extensionPath}`,\n                        `--load-extension=${extensionPath}`,\n                        \"--no-sandbox\",\n                        \"--disable-setuid-sandbox\"\n                    ]\n                });\n                const page = await browser.newPage();\n                // Store globally for use in other endpoints\n                setGlobalBrowser(browser, page);\n                console.log(\"Navigating to Sparx Learning...\");\n                await page.goto(url, {\n                    timeout: 15000\n                });\n                console.log(\"Selecting school\");\n                try {\n                    // Use school from credentials\n                    if (!credentials || !credentials.school) {\n                        throw new Error(\"School name is required\");\n                    }\n                    await page.type('input[type=\"text\"], input[type=\"search\"], input', credentials.school);\n                    await page.press('input[type=\"text\"], input[type=\"search\"], input', \"Enter\");\n                    await page.waitForTimeout(1000);\n                    try {\n                        await page.click('button:has-text(\"Continue\")', {\n                            timeout: 5000\n                        });\n                    } catch (error) {\n                        console.log(\"Continue button not found, proceeding anyway\");\n                    }\n                    await page.waitForTimeout(2000);\n                    try {\n                        await page.click(\"div#cookiescript_accept\", {\n                            timeout: 5000\n                        });\n                    } catch (error) {\n                        console.log(\"Cookie accept button not found, proceeding anyway\");\n                    }\n                } catch (schoolError) {\n                    console.log(\"School selection error:\", schoolError.message);\n                }\n                await page.waitForTimeout(3000);\n                // Click the Microsoft SSO login button\n                const microsoftButton = page.locator('div.sso-login-button[onclick*=\"oauth2/login\"]');\n                if (await microsoftButton.count() > 0) {\n                    console.log(\"Found Microsoft login button, clicking...\");\n                    await microsoftButton.click();\n                } else {\n                    console.log(\"Microsoft login button not found, trying alternative selectors...\");\n                    const altSelectors = [\n                        'a[href*=\"microsoftonline.com\"]',\n                        'button:has-text(\"Microsoft\")',\n                        '[data-provider=\"microsoft\"]',\n                        \".microsoft-login\"\n                    ];\n                    let found = false;\n                    for (const selector of altSelectors){\n                        const element = page.locator(selector);\n                        if (await element.count() > 0) {\n                            console.log(`Found Microsoft login with selector: ${selector}`);\n                            await element.click();\n                            found = true;\n                            break;\n                        }\n                    }\n                    if (!found) {\n                        throw new Error(\"Microsoft login button not found\");\n                    }\n                }\n                await page.waitForTimeout(3000);\n                // Input email address\n                console.log(\"Inputting email address...\");\n                // Use credentials from request\n                if (!credentials || !credentials.email || !credentials.password) {\n                    throw new Error(\"Login credentials are required\");\n                }\n                await page.type('input[type=\"email\"], input[name=\"loginfmt\"], input[placeholder*=\"email\"]', credentials.email);\n                // Wait and click Next button\n                await page.waitForTimeout(1000);\n                console.log(\"Clicking Next button...\");\n                await page.click('button:has-text(\"Next\"), input[type=\"submit\"][value=\"Next\"], button[id*=\"next\"]');\n                // Wait and input password\n                await page.waitForTimeout(4000);\n                console.log(\"Inputting password...\");\n                await page.type('input[type=\"password\"], input[name=\"passwd\"], input[placeholder*=\"password\"]', credentials.password);\n                // Wait and click Sign in button\n                await page.waitForTimeout(1000);\n                console.log(\"Clicking Sign in button...\");\n                await page.click('button:has-text(\"Sign in\"), input[type=\"submit\"][value=\"Sign in\"], button[id*=\"signin\"]');\n                // Wait and click No button\n                await page.waitForTimeout(2000);\n                console.log(\"Clicking No button...\");\n                await page.click('button:has-text(\"No\"), input[type=\"button\"][value=\"No\"], button[id*=\"no\"]');\n                // Wait for login completion and navigate to library\n                console.log(\"Waiting for login completion...\");\n                await page.waitForTimeout(5000);\n                console.log(\"Navigating to Sparx Reader library page...\");\n                await page.goto(\"https://reader.sparx-learning.com/library\", {\n                    waitUntil: \"domcontentloaded\",\n                    timeout: 30000\n                });\n                await page.waitForTimeout(5000);\n                // Extract user's total SRP using enhanced function from start route\n                console.log(\"Extracting user total SRP...\");\n                let userTotalSrp = null;\n                // Try to get user total SRP with multiple attempts\n                for(let attempt = 0; attempt < 5; attempt++){\n                    try {\n                        userTotalSrp = await page.evaluate(()=>{\n                            // Get the user's total SRP\n                            const userTotalSrpElement = document.querySelector(\".sr_92b39de6\");\n                            return userTotalSrpElement ? userTotalSrpElement.textContent.replace(/[^\\d,]/g, \"\").replace(\",\", \"\") : null;\n                        });\n                        if (userTotalSrp) {\n                            console.log(`User Total SRP extracted on attempt ${attempt + 1}: ${userTotalSrp}`);\n                            break;\n                        }\n                        // If we didn't get the info, wait a short time and try again\n                        if (attempt < 4) {\n                            await page.waitForTimeout(200);\n                        }\n                    } catch (error) {\n                        console.log(`SRP extraction attempt ${attempt + 1} failed:`, error.message);\n                        if (attempt < 4) {\n                            await page.waitForTimeout(200);\n                        }\n                    }\n                }\n                // Store initial SRP info everywhere im pretty sure and in browser localStorage\n                global.sessionSrpInfo = {\n                    initialUserTotalSrp: userTotalSrp,\n                    targetSrpNeeded: targetSrp || null\n                };\n                // Store target SRP and initial SRP in browser localStorage so extension can do its thang\n                if (targetSrp) {\n                    await page.evaluate(({ target, initial })=>{\n                        localStorage.setItem(\"targetSrp\", target.toString());\n                        localStorage.setItem(\"initialSrp\", initial || \"0\");\n                    }, {\n                        target: targetSrp,\n                        initial: userTotalSrp\n                    });\n                    console.log(`Target SRP stored in localStorage: ${targetSrp}`);\n                    console.log(`Initial SRP stored in localStorage: ${userTotalSrp}`);\n                }\n                console.log(\"Microsoft login setup complete.\");\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    success: true,\n                    currentSrp: userTotalSrp || \"0\",\n                    bookTitle: \"Microsoft Login - Ready\",\n                    message: \"Microsoft login initiated successfully\"\n                });\n            } catch (playwrightError) {\n                if (playwrightError.message.includes(\"Executable doesn't exist\")) {\n                    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                        success: false,\n                        error: \"Playwright browsers not installed. Please run 'npx playwright install chromium' in your terminal.\",\n                        needsPlaywright: true\n                    }, {\n                        status: 500\n                    });\n                } else {\n                    throw playwrightError;\n                }\n            }\n        } catch (error) {\n            console.error(\"Error opening Sparx Reader with extension:\", error);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: error.message\n            }, {\n                status: 500\n            });\n        }\n    });\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/sparxreader/microsoft-start/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/auth.js":
/*!*********************!*\
  !*** ./lib/auth.js ***!
  \*********************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst jwt = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst { getDatabase } = __webpack_require__(/*! ./database */ \"(rsc)/./lib/database.js\");\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key-change-this-in-production\";\nconst TOKEN_EXPIRY = \"7d\"; // 7 days\nclass AuthManager {\n    constructor(){\n        this.db = getDatabase();\n    }\n    // Generate JWT token\n    generateToken(user) {\n        const payload = {\n            userId: user.id,\n            username: user.username,\n            role: user.role,\n            iat: Math.floor(Date.now() / 1000)\n        };\n        return jwt.sign(payload, JWT_SECRET, {\n            expiresIn: TOKEN_EXPIRY\n        });\n    }\n    // Verify JWT token\n    verifyToken(token) {\n        try {\n            return jwt.verify(token, JWT_SECRET);\n        } catch (error) {\n            throw new Error(\"Invalid token\");\n        }\n    }\n    // Hash token for database storage\n    hashToken(token) {\n        return crypto.createHash(\"sha256\").update(token).digest(\"hex\");\n    }\n    // Create session with token\n    createSession(user, ipAddress = null, userAgent = null) {\n        const token = this.generateToken(user);\n        const tokenHash = this.hashToken(token);\n        // Calculate expiry date\n        const expiresAt = new Date();\n        expiresAt.setDate(expiresAt.getDate() + 7); // 7 days\n        // Store session in database\n        this.db.createSession(user.id, tokenHash, expiresAt.toISOString(), ipAddress, userAgent);\n        // Log activity\n        this.db.logActivity(user.id, \"LOGIN\", `User logged in from ${ipAddress}`, ipAddress);\n        return {\n            token,\n            user: {\n                id: user.id,\n                username: user.username,\n                role: user.role,\n                lastLogin: user.last_login\n            },\n            expiresAt: expiresAt.toISOString()\n        };\n    }\n    // Validate session\n    validateSession(token) {\n        try {\n            // First verify JWT\n            const decoded = this.verifyToken(token);\n            // Then check database session\n            const tokenHash = this.hashToken(token);\n            const session = this.db.validateSession(tokenHash);\n            if (!session || !session.user_active) {\n                throw new Error(\"Session invalid or user inactive\");\n            }\n            return {\n                userId: session.user_id,\n                username: session.username,\n                role: session.role,\n                sessionId: session.id\n            };\n        } catch (error) {\n            throw new Error(\"Invalid session\");\n        }\n    }\n    // Logout user\n    logout(token, userId = null) {\n        const tokenHash = this.hashToken(token);\n        this.db.invalidateSession(tokenHash);\n        if (userId) {\n            this.db.logActivity(userId, \"LOGOUT\", \"User logged out\");\n        }\n    }\n    // Logout all sessions for user\n    logoutAllSessions(userId) {\n        this.db.invalidateAllUserSessions(userId);\n        this.db.logActivity(userId, \"LOGOUT_ALL\", \"All sessions invalidated\");\n    }\n    // Middleware for protecting routes\n    requireAuth(requiredRole = null) {\n        return async (req, res, next)=>{\n            try {\n                const authHeader = req.headers.authorization;\n                if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n                    return res.status(401).json({\n                        error: \"No token provided\"\n                    });\n                }\n                const token = authHeader.substring(7);\n                const session = await this.validateSession(token);\n                // Check role if required\n                if (requiredRole && session.role !== requiredRole) {\n                    return res.status(403).json({\n                        error: \"Insufficient permissions\"\n                    });\n                }\n                // Add user info to request\n                req.user = session;\n                req.token = token;\n                next();\n            } catch (error) {\n                return res.status(401).json({\n                    error: error.message\n                });\n            }\n        };\n    }\n    // Admin only middleware\n    requireAdmin() {\n        return this.requireAuth(\"admin\");\n    }\n    // Extract IP address from request (Next.js compatible)\n    getClientIP(req) {\n        // For Next.js App Router requests\n        if (req.headers && typeof req.headers.get === \"function\") {\n            return req.headers.get(\"x-forwarded-for\") || req.headers.get(\"x-real-ip\") || req.ip || \"127.0.0.1\";\n        }\n        // For traditional Node.js requests\n        return req.headers[\"x-forwarded-for\"] || req.connection?.remoteAddress || req.socket?.remoteAddress || (req.connection?.socket ? req.connection.socket.remoteAddress : null) || \"127.0.0.1\";\n    }\n    // Extract user agent (Next.js compatible)\n    getUserAgent(req) {\n        // For Next.js App Router requests\n        if (req.headers && typeof req.headers.get === \"function\") {\n            return req.headers.get(\"user-agent\") || \"Unknown\";\n        }\n        // For traditional Node.js requests\n        return req.headers[\"user-agent\"] || \"Unknown\";\n    }\n    // Rate limiting helper\n    checkRateLimit(identifier, maxAttempts = 5, windowMinutes = 15) {\n        // This is a simple in-memory rate limiter\n        // In production, you might want to use Redis or database\n        if (!this.rateLimitStore) {\n            this.rateLimitStore = new Map();\n        }\n        const now = Date.now();\n        const windowMs = windowMinutes * 60 * 1000;\n        const key = `rate_limit_${identifier}`;\n        if (!this.rateLimitStore.has(key)) {\n            this.rateLimitStore.set(key, {\n                count: 1,\n                resetTime: now + windowMs\n            });\n            return {\n                allowed: true,\n                remaining: maxAttempts - 1\n            };\n        }\n        const record = this.rateLimitStore.get(key);\n        if (now > record.resetTime) {\n            // Reset the window\n            this.rateLimitStore.set(key, {\n                count: 1,\n                resetTime: now + windowMs\n            });\n            return {\n                allowed: true,\n                remaining: maxAttempts - 1\n            };\n        }\n        if (record.count >= maxAttempts) {\n            return {\n                allowed: false,\n                remaining: 0,\n                resetTime: record.resetTime\n            };\n        }\n        record.count++;\n        return {\n            allowed: true,\n            remaining: maxAttempts - record.count\n        };\n    }\n    // Clean up expired rate limit entries\n    cleanupRateLimit() {\n        if (!this.rateLimitStore) return;\n        const now = Date.now();\n        for (const [key, record] of this.rateLimitStore.entries()){\n            if (now > record.resetTime) {\n                this.rateLimitStore.delete(key);\n            }\n        }\n    }\n    // Password strength validation\n    validatePasswordStrength(password) {\n        const minLength = 8;\n        const hasUpperCase = /[A-Z]/.test(password);\n        const hasLowerCase = /[a-z]/.test(password);\n        const hasNumbers = /\\d/.test(password);\n        const hasSpecialChar = /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n        const errors = [];\n        if (password.length < minLength) {\n            errors.push(`Password must be at least ${minLength} characters long`);\n        }\n        if (!hasUpperCase) {\n            errors.push(\"Password must contain at least one uppercase letter\");\n        }\n        if (!hasLowerCase) {\n            errors.push(\"Password must contain at least one lowercase letter\");\n        }\n        if (!hasNumbers) {\n            errors.push(\"Password must contain at least one number\");\n        }\n        if (!hasSpecialChar) {\n            errors.push(\"Password must contain at least one special character\");\n        }\n        return {\n            isValid: errors.length === 0,\n            errors,\n            strength: this.calculatePasswordStrength(password)\n        };\n    }\n    calculatePasswordStrength(password) {\n        let score = 0;\n        // Length bonus\n        score += Math.min(password.length * 2, 20);\n        // Character variety bonus\n        if (/[a-z]/.test(password)) score += 5;\n        if (/[A-Z]/.test(password)) score += 5;\n        if (/[0-9]/.test(password)) score += 5;\n        if (/[^A-Za-z0-9]/.test(password)) score += 10;\n        // Penalty for common patterns\n        if (/(.)\\1{2,}/.test(password)) score -= 10; // Repeated characters\n        if (/123|abc|qwe/i.test(password)) score -= 10; // Sequential patterns\n        if (score < 30) return \"weak\";\n        if (score < 60) return \"medium\";\n        return \"strong\";\n    }\n}\n// Export singleton instance\nlet authInstance = null;\nfunction getAuthManager() {\n    if (!authInstance) {\n        authInstance = new AuthManager();\n    }\n    return authInstance;\n}\nmodule.exports = {\n    getAuthManager,\n    AuthManager\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.js\n");

/***/ }),

/***/ "(rsc)/./lib/database.js":
/*!*************************!*\
  !*** ./lib/database.js ***!
  \*************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst Database = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\nconst bcrypt = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/umd/index.js\");\nconst { v4: uuidv4 } = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/cjs/index.js\");\nconst path = __webpack_require__(/*! path */ \"path\");\nclass DatabaseManager {\n    constructor(){\n        const dbPath = path.join(process.cwd(), \"data\", \"app.db\");\n        this.db = new Database(dbPath);\n        this.initializeTables();\n        this.createDefaultAdmin();\n    }\n    initializeTables() {\n        // Users table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS users (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        username TEXT UNIQUE NOT NULL,\n        password_hash TEXT NOT NULL,\n        role TEXT DEFAULT 'user' CHECK(role IN ('user', 'admin')),\n        license_key_id INTEGER,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        is_active BOOLEAN DEFAULT 1,\n        last_login DATETIME,\n        FOREIGN KEY (license_key_id) REFERENCES license_keys (id)\n      )\n    `);\n        // License keys table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS license_keys (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        key_code TEXT UNIQUE NOT NULL,\n        duration_days INTEGER NOT NULL,\n        max_uses INTEGER NOT NULL DEFAULT 1,\n        current_uses INTEGER DEFAULT 0,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        expires_at DATETIME NOT NULL,\n        is_active BOOLEAN DEFAULT 1,\n        created_by INTEGER,\n        features TEXT DEFAULT '[]',\n        FOREIGN KEY (created_by) REFERENCES users (id)\n      )\n    `);\n        // User sessions table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS user_sessions (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        token_hash TEXT NOT NULL,\n        expires_at DATETIME NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        ip_address TEXT,\n        user_agent TEXT,\n        is_active BOOLEAN DEFAULT 1,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Activity logs table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS activity_logs (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER,\n        action TEXT NOT NULL,\n        details TEXT,\n        ip_address TEXT,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // System settings table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS system_settings (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        setting_key TEXT UNIQUE NOT NULL,\n        setting_value TEXT,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_by INTEGER,\n        FOREIGN KEY (updated_by) REFERENCES users (id)\n      )\n    `);\n        // Encrypted login credentials table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS encrypted_credentials (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        login_key TEXT UNIQUE NOT NULL,\n        login_method TEXT NOT NULL CHECK(login_method IN ('normal', 'microsoft', 'google')),\n        encrypted_school TEXT NOT NULL,\n        encrypted_email TEXT NOT NULL,\n        encrypted_password TEXT NOT NULL,\n        encryption_iv TEXT NOT NULL,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        is_active BOOLEAN DEFAULT 1,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // License feature settings table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS license_feature_settings (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        license_key_id INTEGER NOT NULL,\n        max_accounts_per_batch INTEGER DEFAULT 0,\n        priority_level INTEGER DEFAULT 0 CHECK(priority_level >= 0 AND priority_level <= 10),\n        scheduling_access BOOLEAN DEFAULT 0,\n        multi_user_access BOOLEAN DEFAULT 0,\n        max_batches_per_day INTEGER DEFAULT 1,\n        max_schedules_per_week INTEGER DEFAULT 1,\n        max_main_jobs_per_day INTEGER DEFAULT 5,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (license_key_id) REFERENCES license_keys (id),\n        UNIQUE(license_key_id)\n      )\n    `);\n        // System configuration table for admin settings\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS system_config (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        config_key TEXT UNIQUE NOT NULL,\n        config_value TEXT NOT NULL,\n        config_type TEXT DEFAULT 'string' CHECK(config_type IN ('string', 'number', 'boolean', 'json')),\n        description TEXT,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Dead letter queue table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS dead_letter_queue (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        original_job_id INTEGER,\n        job_type TEXT NOT NULL,\n        job_data TEXT NOT NULL,\n        user_id INTEGER NOT NULL,\n        failure_reason TEXT,\n        failure_count INTEGER DEFAULT 1,\n        last_failure_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        status TEXT DEFAULT 'failed' CHECK(status IN ('failed', 'investigating', 'resolved', 'discarded')),\n        FOREIGN KEY (user_id) REFERENCES users (id),\n        FOREIGN KEY (original_job_id) REFERENCES queue_jobs (id)\n      )\n    `);\n        // Queue batches table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_batches (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        batch_name TEXT,\n        login_type TEXT DEFAULT 'normal' CHECK(login_type IN ('normal', 'google', 'microsoft')),\n        total_accounts INTEGER NOT NULL,\n        processed_accounts INTEGER DEFAULT 0,\n        failed_accounts INTEGER DEFAULT 0,\n        status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),\n        priority_level INTEGER DEFAULT 0,\n        srp_target INTEGER DEFAULT 100,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        started_at DATETIME,\n        completed_at DATETIME,\n        scheduled_time DATETIME,\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Add srp_target column to queue_batches if it doesn't exist\n        try {\n            this.db.exec(`ALTER TABLE queue_batches ADD COLUMN srp_target INTEGER DEFAULT 100`);\n        } catch (error) {\n        // Column already exists, ignore error\n        }\n        // Queue jobs table\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_jobs (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        batch_id INTEGER,\n        user_id INTEGER NOT NULL,\n        job_type TEXT NOT NULL DEFAULT 'sparx_reader',\n        job_data TEXT NOT NULL,\n        status TEXT DEFAULT 'queued' CHECK(status IN ('queued', 'processing', 'completed', 'failed', 'cancelled')),\n        priority_level INTEGER DEFAULT 0,\n        effective_priority INTEGER DEFAULT 0,\n        srp_target INTEGER DEFAULT 100,\n        scheduled_time DATETIME,\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        started_at DATETIME,\n        completed_at DATETIME,\n        error_message TEXT,\n        retry_count INTEGER DEFAULT 0,\n        max_retries INTEGER DEFAULT 3,\n        FOREIGN KEY (batch_id) REFERENCES queue_batches (id),\n        FOREIGN KEY (user_id) REFERENCES users (id)\n      )\n    `);\n        // Add srp_target column to queue_jobs if it doesn't exist\n        try {\n            this.db.exec(`ALTER TABLE queue_jobs ADD COLUMN srp_target INTEGER DEFAULT 100`);\n        } catch (error) {\n        // Column already exists, ignore error\n        }\n        // Queue schedules table for conflict detection\n        this.db.exec(`\n      CREATE TABLE IF NOT EXISTS queue_schedules (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        user_id INTEGER NOT NULL,\n        scheduled_time DATETIME NOT NULL,\n        duration_minutes INTEGER DEFAULT 30,\n        srp_target INTEGER DEFAULT 100,\n        job_id INTEGER,\n        batch_id INTEGER,\n        status TEXT DEFAULT 'scheduled' CHECK(status IN ('scheduled', 'active', 'completed', 'cancelled')),\n        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users (id),\n        FOREIGN KEY (job_id) REFERENCES queue_jobs (id),\n        FOREIGN KEY (batch_id) REFERENCES queue_batches (id)\n      )\n    `);\n        // Add srp_target column if it doesn't exist (for existing databases)\n        try {\n            this.db.exec(`ALTER TABLE queue_schedules ADD COLUMN srp_target INTEGER DEFAULT 100`);\n        } catch (error) {\n        // Column already exists, ignore error\n        }\n        // Create indexes for better performance\n        this.db.exec(`\n      CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);\n      CREATE INDEX IF NOT EXISTS idx_users_license_key ON users(license_key_id);\n      CREATE INDEX IF NOT EXISTS idx_license_keys_code ON license_keys(key_code);\n      CREATE INDEX IF NOT EXISTS idx_license_keys_expires ON license_keys(expires_at);\n      CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(token_hash);\n      CREATE INDEX IF NOT EXISTS idx_sessions_user ON user_sessions(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_user ON activity_logs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_activity_created ON activity_logs(created_at);\n      CREATE INDEX IF NOT EXISTS idx_encrypted_credentials_user ON encrypted_credentials(user_id);\n      CREATE INDEX IF NOT EXISTS idx_encrypted_credentials_key ON encrypted_credentials(login_key);\n      CREATE INDEX IF NOT EXISTS idx_license_features ON license_feature_settings(license_key_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_user ON queue_batches(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_status ON queue_batches(status);\n      CREATE INDEX IF NOT EXISTS idx_queue_batches_scheduled ON queue_batches(scheduled_time);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_batch ON queue_jobs(batch_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_user ON queue_jobs(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_status ON queue_jobs(status);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_priority ON queue_jobs(effective_priority);\n      CREATE INDEX IF NOT EXISTS idx_queue_jobs_scheduled ON queue_jobs(scheduled_time);\n      CREATE INDEX IF NOT EXISTS idx_queue_schedules_user ON queue_schedules(user_id);\n      CREATE INDEX IF NOT EXISTS idx_queue_schedules_time ON queue_schedules(scheduled_time);\n    `);\n        // Migration: Add max_batches_per_day column if it doesn't exist\n        try {\n            const columns = this.db.prepare(\"PRAGMA table_info(license_feature_settings)\").all();\n            const hasMaxBatchesPerDay = columns.some((col)=>col.name === \"max_batches_per_day\");\n            if (!hasMaxBatchesPerDay) {\n                console.log(\"Adding max_batches_per_day column to license_feature_settings...\");\n                this.db.exec(`ALTER TABLE license_feature_settings ADD COLUMN max_batches_per_day INTEGER DEFAULT 1`);\n            }\n        } catch (error) {\n            console.error(\"Migration error for max_batches_per_day:\", error);\n        }\n        // Migration: Add login_type column to queue_batches if it doesn't exist\n        try {\n            const batchColumns = this.db.prepare(\"PRAGMA table_info(queue_batches)\").all();\n            const hasLoginType = batchColumns.some((col)=>col.name === \"login_type\");\n            if (!hasLoginType) {\n                console.log(\"Adding login_type column to queue_batches...\");\n                this.db.exec(`ALTER TABLE queue_batches ADD COLUMN login_type TEXT DEFAULT 'normal' CHECK(login_type IN ('normal', 'google', 'microsoft'))`);\n            }\n        } catch (error) {\n            console.error(\"Migration error for login_type:\", error);\n        }\n    }\n    createDefaultAdmin() {\n        const adminExists = this.db.prepare(\"SELECT id FROM users WHERE role = ? LIMIT 1\").get(\"admin\");\n        if (!adminExists) {\n            const hashedPassword = bcrypt.hashSync(process.env.DEFAULT_ADMIN_PASSWORD, 12);\n            const stmt = this.db.prepare(`\n        INSERT INTO users (username, password_hash, role, is_active)\n        VALUES (?, ?, ?, ?)\n      `);\n            stmt.run(process.env.DEFAULT_ADMIN_USERNAME, hashedPassword, \"admin\", 1);\n            console.log(`Default admin user created: ${process.env.DEFAULT_ADMIN_USERNAME}/[hidden]`);\n        }\n        // Initialize default system configuration\n        this.initializeDefaultConfig();\n    }\n    initializeDefaultConfig() {\n        const defaultConfigs = [\n            {\n                key: \"default_max_main_jobs_per_day\",\n                value: process.env.DEFAULT_MAX_MAIN_JOBS_PER_DAY || \"5\",\n                type: \"number\",\n                description: \"Default daily limit for main automation jobs\"\n            },\n            {\n                key: \"default_max_batches_per_day\",\n                value: process.env.DEFAULT_MAX_BATCHES_PER_DAY || \"1\",\n                type: \"number\",\n                description: \"Default daily limit for batch jobs\"\n            },\n            {\n                key: \"default_max_schedules_per_week\",\n                value: process.env.DEFAULT_MAX_SCHEDULES_PER_WEEK || \"1\",\n                type: \"number\",\n                description: \"Default weekly limit for scheduled jobs\"\n            },\n            {\n                key: \"dead_letter_queue_enabled\",\n                value: \"true\",\n                type: \"boolean\",\n                description: \"Enable dead letter queue for failed jobs\"\n            },\n            {\n                key: \"max_retry_attempts\",\n                value: \"3\",\n                type: \"number\",\n                description: \"Maximum retry attempts before moving to dead letter queue\"\n            },\n            {\n                key: \"circuit_breaker_enabled\",\n                value: \"true\",\n                type: \"boolean\",\n                description: \"Enable circuit breaker for external API calls\"\n            }\n        ];\n        for (const config of defaultConfigs){\n            const existingStmt = this.db.prepare(\"SELECT id FROM system_config WHERE config_key = ?\");\n            const existing = existingStmt.get(config.key);\n            if (!existing) {\n                const insertStmt = this.db.prepare(`\n          INSERT INTO system_config (config_key, config_value, config_type, description)\n          VALUES (?, ?, ?, ?)\n        `);\n                insertStmt.run(config.key, config.value, config.type, config.description);\n            }\n        }\n    }\n    // User management methods\n    createUser(username, password, licenseKey) {\n        const transaction = this.db.transaction(()=>{\n            // Validate license key\n            const licenseStmt = this.db.prepare(`\n        SELECT id, max_uses, current_uses, expires_at, is_active \n        FROM license_keys \n        WHERE key_code = ? AND is_active = 1\n      `);\n            const license = licenseStmt.get(licenseKey);\n            if (!license) {\n                throw new Error(\"Invalid license key\");\n            }\n            if (new Date(license.expires_at) < new Date()) {\n                throw new Error(\"License key has expired\");\n            }\n            if (license.current_uses >= license.max_uses) {\n                throw new Error(\"License key has reached maximum uses\");\n            }\n            // Check if username already exists\n            const userExists = this.db.prepare(\"SELECT id FROM users WHERE username = ?\").get(username);\n            if (userExists) {\n                throw new Error(\"Username already exists\");\n            }\n            // Create user\n            const hashedPassword = bcrypt.hashSync(password, 12);\n            const userStmt = this.db.prepare(`\n        INSERT INTO users (username, password_hash, license_key_id, role)\n        VALUES (?, ?, ?, ?)\n      `);\n            const result = userStmt.run(username, hashedPassword, license.id, \"user\");\n            // Update license key usage\n            const updateLicenseStmt = this.db.prepare(`\n        UPDATE license_keys \n        SET current_uses = current_uses + 1 \n        WHERE id = ?\n      `);\n            updateLicenseStmt.run(license.id);\n            return result.lastInsertRowid;\n        });\n        return transaction();\n    }\n    authenticateUser(username, password) {\n        const stmt = this.db.prepare(`\n      SELECT u.*, lk.expires_at as license_expires, lk.is_active as license_active\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      WHERE u.username = ? AND u.is_active = 1\n    `);\n        const user = stmt.get(username);\n        if (!user) {\n            throw new Error(\"Invalid credentials\");\n        }\n        const isValidPassword = bcrypt.compareSync(password, user.password_hash);\n        if (!isValidPassword) {\n            throw new Error(\"Invalid credentials\");\n        }\n        // Check license validity for non-admin users\n        if (user.role !== \"admin\") {\n            if (!user.license_active || new Date(user.license_expires) < new Date()) {\n                throw new Error(\"License has expired\");\n            }\n        }\n        // Update last login\n        const updateStmt = this.db.prepare(\"UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?\");\n        updateStmt.run(user.id);\n        // Remove sensitive data\n        delete user.password_hash;\n        return user;\n    }\n    // License key management methods\n    createLicenseKey(durationDays, maxUses = 1, features = [], createdBy = null) {\n        const keyCode = this.generateLicenseKey();\n        const expiresAt = new Date();\n        expiresAt.setDate(expiresAt.getDate() + durationDays);\n        const stmt = this.db.prepare(`\n      INSERT INTO license_keys (key_code, duration_days, max_uses, expires_at, created_by, features)\n      VALUES (?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(keyCode, durationDays, maxUses, expiresAt.toISOString(), createdBy, JSON.stringify(features));\n        return {\n            id: result.lastInsertRowid,\n            keyCode,\n            durationDays,\n            maxUses,\n            expiresAt: expiresAt.toISOString(),\n            features\n        };\n    }\n    generateLicenseKey() {\n        const segments = [];\n        for(let i = 0; i < 4; i++){\n            segments.push(uuidv4().replace(/-/g, \"\").substring(0, 8).toUpperCase());\n        }\n        return `SRX-${segments.join(\"-\")}`;\n    }\n    getLicenseKeys(limit = 50, offset = 0) {\n        const stmt = this.db.prepare(`\n      SELECT \n        lk.*,\n        u.username as created_by_username,\n        COUNT(users.id) as users_count\n      FROM license_keys lk\n      LEFT JOIN users u ON lk.created_by = u.id\n      LEFT JOIN users ON users.license_key_id = lk.id\n      GROUP BY lk.id\n      ORDER BY lk.created_at DESC\n      LIMIT ? OFFSET ?\n    `);\n        return stmt.all(limit, offset);\n    }\n    deactivateLicenseKey(keyId) {\n        const stmt = this.db.prepare(\"UPDATE license_keys SET is_active = 0 WHERE id = ?\");\n        return stmt.run(keyId);\n    }\n    // Get detailed license information for a user\n    getUserLicenseStatus(userId) {\n        const stmt = this.db.prepare(`\n      SELECT \n        u.id as user_id,\n        u.username,\n        lk.id as license_id,\n        lk.key_code,\n        lk.max_uses,\n        lk.current_uses,\n        lk.expires_at,\n        lk.is_active,\n        CASE \n          WHEN lk.expires_at <= datetime('now') THEN 'expired'\n          WHEN lk.current_uses >= lk.max_uses THEN 'maxed_out'\n          WHEN lk.is_active = 0 THEN 'inactive'\n          ELSE 'valid'\n        END as license_status\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      WHERE u.id = ?\n    `);\n        return stmt.get(userId);\n    }\n    // Validate a license key for renewal (check if it's valid and has available uses)\n    validateLicenseForRenewal(licenseKey) {\n        const stmt = this.db.prepare(`\n      SELECT \n        id,\n        key_code,\n        max_uses,\n        current_uses,\n        expires_at,\n        is_active,\n        CASE \n          WHEN expires_at <= datetime('now') THEN 'expired'\n          WHEN current_uses >= max_uses THEN 'maxed_out'\n          WHEN is_active = 0 THEN 'inactive'\n          ELSE 'valid'\n        END as status\n      FROM license_keys \n      WHERE key_code = ?\n    `);\n        const license = stmt.get(licenseKey);\n        if (!license) {\n            return {\n                valid: false,\n                error: \"License key not found\"\n            };\n        }\n        if (license.status !== \"valid\") {\n            let errorMessage = \"License key is not valid\";\n            switch(license.status){\n                case \"expired\":\n                    errorMessage = \"License key has expired\";\n                    break;\n                case \"maxed_out\":\n                    errorMessage = \"License key has reached maximum uses\";\n                    break;\n                case \"inactive\":\n                    errorMessage = \"License key is inactive\";\n                    break;\n            }\n            return {\n                valid: false,\n                error: errorMessage\n            };\n        }\n        return {\n            valid: true,\n            license\n        };\n    }\n    // Renew user's license with a new license key\n    renewUserLicense(userId, newLicenseKey) {\n        const transaction = this.db.transaction(()=>{\n            // First validate the new license key\n            const validation = this.validateLicenseForRenewal(newLicenseKey);\n            if (!validation.valid) {\n                throw new Error(validation.error);\n            }\n            const newLicense = validation.license;\n            // Update user's license_key_id to the new license\n            const updateUserStmt = this.db.prepare(`\n        UPDATE users \n        SET license_key_id = ?, updated_at = CURRENT_TIMESTAMP \n        WHERE id = ?\n      `);\n            updateUserStmt.run(newLicense.id, userId);\n            // Increment the new license's current_uses\n            const updateLicenseStmt = this.db.prepare(`\n        UPDATE license_keys \n        SET current_uses = current_uses + 1 \n        WHERE id = ?\n      `);\n            updateLicenseStmt.run(newLicense.id);\n            // Log the renewal activity\n            this.logActivity(userId, \"LICENSE_RENEWED\", `License renewed with key: ${newLicenseKey}`);\n            return {\n                success: true,\n                newLicenseId: newLicense.id,\n                newLicenseKey: newLicenseKey,\n                expiresAt: newLicense.expires_at,\n                maxUses: newLicense.max_uses,\n                currentUses: newLicense.current_uses + 1\n            };\n        });\n        return transaction();\n    }\n    // Session management\n    createSession(userId, tokenHash, expiresAt, ipAddress = null, userAgent = null) {\n        const stmt = this.db.prepare(`\n      INSERT INTO user_sessions (user_id, token_hash, expires_at, ip_address, user_agent)\n      VALUES (?, ?, ?, ?, ?)\n    `);\n        return stmt.run(userId, tokenHash, expiresAt, ipAddress, userAgent);\n    }\n    validateSession(tokenHash) {\n        const stmt = this.db.prepare(`\n      SELECT s.*, u.username, u.role, u.is_active as user_active\n      FROM user_sessions s\n      JOIN users u ON s.user_id = u.id\n      WHERE s.token_hash = ? AND s.is_active = 1 AND s.expires_at > datetime('now')\n    `);\n        return stmt.get(tokenHash);\n    }\n    invalidateSession(tokenHash) {\n        const stmt = this.db.prepare(\"UPDATE user_sessions SET is_active = 0 WHERE token_hash = ?\");\n        return stmt.run(tokenHash);\n    }\n    invalidateAllUserSessions(userId) {\n        const stmt = this.db.prepare(\"UPDATE user_sessions SET is_active = 0 WHERE user_id = ?\");\n        return stmt.run(userId);\n    }\n    // Activity logging\n    logActivity(userId, action, details = null, ipAddress = null) {\n        const stmt = this.db.prepare(`\n      INSERT INTO activity_logs (user_id, action, details, ip_address)\n      VALUES (?, ?, ?, ?)\n    `);\n        return stmt.run(userId, action, details, ipAddress);\n    }\n    // Cleanup expired keys older than 30 days\n    cleanupExpiredKeys() {\n        try {\n            const stmt = this.db.prepare(`\n        DELETE FROM license_keys\n        WHERE status = 'expired'\n        AND datetime(expires_at) < datetime('now', '-30 days')\n      `);\n            const result = stmt.run();\n            if (result.changes > 0) {\n                console.log(`🧹 Cleaned up ${result.changes} expired license keys older than 30 days`);\n                this.logActivity(null, \"SYSTEM_CLEANUP\", `Removed ${result.changes} expired keys older than 30 days`);\n            }\n            return result.changes;\n        } catch (error) {\n            console.error(\"Error cleaning up expired keys:\", error);\n            return 0;\n        }\n    }\n    getActivityLogs(userId = null, limit = 100, offset = 0) {\n        let query = `\n      SELECT \n        al.*,\n        u.username\n      FROM activity_logs al\n      LEFT JOIN users u ON al.user_id = u.id\n    `;\n        const params = [];\n        if (userId) {\n            query += \" WHERE al.user_id = ?\";\n            params.push(userId);\n        }\n        query += \" ORDER BY al.created_at DESC LIMIT ? OFFSET ?\";\n        params.push(limit, offset);\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    // Analytics methods\n    getSystemStats() {\n        const stats = {};\n        // Total users\n        stats.totalUsers = this.db.prepare(\"SELECT COUNT(*) as count FROM users WHERE role = 'user'\").get().count;\n        // Active users (logged in within last 30 days)\n        stats.activeUsers = this.db.prepare(`\n      SELECT COUNT(*) as count FROM users \n      WHERE role = 'user' AND last_login > datetime('now', '-30 days')\n    `).get().count;\n        // Total license keys\n        stats.totalLicenseKeys = this.db.prepare(\"SELECT COUNT(*) as count FROM license_keys\").get().count;\n        // Active license keys\n        stats.activeLicenseKeys = this.db.prepare(`\n      SELECT COUNT(*) as count FROM license_keys \n      WHERE is_active = 1 AND expires_at > datetime('now')\n    `).get().count;\n        // Expired license keys\n        stats.expiredLicenseKeys = this.db.prepare(`\n      SELECT COUNT(*) as count FROM license_keys \n      WHERE expires_at <= datetime('now')\n    `).get().count;\n        // Recent activity (last 24 hours)\n        stats.recentActivity = this.db.prepare(`\n      SELECT COUNT(*) as count FROM activity_logs \n      WHERE created_at > datetime('now', '-1 day')\n    `).get().count;\n        return stats;\n    }\n    // User management for admin\n    getUsers(limit = 50, offset = 0) {\n        const stmt = this.db.prepare(`\n      SELECT \n        u.id,\n        u.username,\n        u.role,\n        u.created_at,\n        u.last_login,\n        u.is_active,\n        lk.key_code,\n        lk.expires_at as license_expires\n      FROM users u\n      LEFT JOIN license_keys lk ON u.license_key_id = lk.id\n      ORDER BY u.created_at DESC\n      LIMIT ? OFFSET ?\n    `);\n        return stmt.all(limit, offset);\n    }\n    toggleUserStatus(userId) {\n        const stmt = this.db.prepare(\"UPDATE users SET is_active = NOT is_active WHERE id = ?\");\n        return stmt.run(userId);\n    }\n    // Cleanup methods\n    cleanupExpiredSessions() {\n        const stmt = this.db.prepare('DELETE FROM user_sessions WHERE expires_at <= datetime(\"now\")');\n        return stmt.run();\n    }\n    cleanupOldLogs(daysToKeep = 90) {\n        const stmt = this.db.prepare(`\n      DELETE FROM activity_logs \n      WHERE created_at <= datetime('now', '-${daysToKeep} days')\n    `);\n        return stmt.run();\n    }\n    // Encrypted credentials methods\n    saveEncryptedCredentials(userId, loginMethod, school, email, password) {\n        const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n        // Check for required environment variable\n        const secret = process.env.CREDENTIAL_ENCRYPTION_SECRET;\n        if (!secret) {\n            throw new Error(\"CREDENTIAL_ENCRYPTION_SECRET environment variable is required\");\n        }\n        // Generate a unique login key\n        const loginKey = \"SLK-\" + crypto.randomBytes(8).toString(\"hex\").toUpperCase();\n        // Create encryption IV\n        const iv = crypto.randomBytes(16);\n        // Derive key using stable secret + userId as salt for user isolation\n        const key = crypto.scryptSync(secret, String(userId), 32);\n        // Encrypt school, email and password\n        const cipher1 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedSchool = cipher1.update(school, \"utf8\", \"hex\") + cipher1.final(\"hex\");\n        const cipher2 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedEmail = cipher2.update(email, \"utf8\", \"hex\") + cipher2.final(\"hex\");\n        const cipher3 = crypto.createCipheriv(\"aes-256-cbc\", key, iv);\n        const encryptedPassword = cipher3.update(password, \"utf8\", \"hex\") + cipher3.final(\"hex\");\n        const stmt = this.db.prepare(`\n      INSERT INTO encrypted_credentials (user_id, login_key, login_method, encrypted_school, encrypted_email, encrypted_password, encryption_iv)\n      VALUES (?, ?, ?, ?, ?, ?, ?)\n    `);\n        stmt.run(userId, loginKey, loginMethod, encryptedSchool, encryptedEmail, encryptedPassword, iv.toString(\"hex\"));\n        return loginKey;\n    }\n    getEncryptedCredentials(loginKey) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM encrypted_credentials \n      WHERE login_key = ? AND is_active = 1\n    `);\n        const result = stmt.get(loginKey);\n        if (!result) return null;\n        const crypto = __webpack_require__(/*! crypto */ \"crypto\");\n        try {\n            // Check for required environment variable\n            const secret = process.env.CREDENTIAL_ENCRYPTION_SECRET;\n            if (!secret) {\n                throw new Error(\"CREDENTIAL_ENCRYPTION_SECRET environment variable is required\");\n            }\n            // Derive key using stable secret + userId as salt (same as during encryption)\n            const key = crypto.scryptSync(secret, String(result.user_id), 32);\n            const iv = Buffer.from(result.encryption_iv, \"hex\");\n            // Decrypt school, email and password\n            const decipher1 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const school = result.encrypted_school ? decipher1.update(result.encrypted_school, \"hex\", \"utf8\") + decipher1.final(\"utf8\") : null;\n            const decipher2 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const email = result.encrypted_email ? decipher2.update(result.encrypted_email, \"hex\", \"utf8\") + decipher2.final(\"utf8\") : null;\n            const decipher3 = crypto.createDecipheriv(\"aes-256-cbc\", key, iv);\n            const password = result.encrypted_password ? decipher3.update(result.encrypted_password, \"hex\", \"utf8\") + decipher3.final(\"utf8\") : null;\n            return {\n                loginMethod: result.login_method,\n                school,\n                email,\n                password,\n                userId: result.user_id\n            };\n        } catch (error) {\n            console.error(\"Failed to decrypt credentials:\", error);\n            return null;\n        }\n    }\n    getUserCredentials(userId) {\n        const stmt = this.db.prepare(`\n      SELECT login_key, login_method, created_at FROM encrypted_credentials \n      WHERE user_id = ? AND is_active = 1\n      ORDER BY created_at DESC\n    `);\n        return stmt.all(userId);\n    }\n    deactivateCredentials(loginKey) {\n        const stmt = this.db.prepare(\"UPDATE encrypted_credentials SET is_active = 0 WHERE login_key = ?\");\n        return stmt.run(loginKey);\n    }\n    // License Feature Settings Methods\n    setLicenseFeatures(licenseKeyId, features) {\n        const stmt = this.db.prepare(`\n      INSERT OR REPLACE INTO license_feature_settings\n      (license_key_id, max_accounts_per_batch, priority_level, scheduling_access, max_batches_per_day, updated_at)\n      VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)\n    `);\n        return stmt.run(licenseKeyId, features.max_accounts_per_batch || 0, features.priority_level || 0, features.scheduling_access ? 1 : 0, features.max_batches_per_day || 1);\n    }\n    getLicenseFeatures(licenseKeyId) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM license_feature_settings WHERE license_key_id = ?\n    `);\n        const result = stmt.get(licenseKeyId);\n        if (!result) {\n            // Return default features if none set\n            return {\n                max_accounts_per_batch: 0,\n                priority_level: 0,\n                scheduling_access: false,\n                max_batches_per_day: 1\n            };\n        }\n        return {\n            max_accounts_per_batch: result.max_accounts_per_batch,\n            priority_level: result.priority_level,\n            scheduling_access: Boolean(result.scheduling_access),\n            max_batches_per_day: result.max_batches_per_day || 1\n        };\n    }\n    getUserLicenseFeatures(userId) {\n        const stmt = this.db.prepare(`\n      SELECT lfs.* FROM license_feature_settings lfs\n      JOIN users u ON u.license_key_id = lfs.license_key_id\n      WHERE u.id = ?\n    `);\n        const result = stmt.get(userId);\n        if (!result) {\n            return {\n                max_accounts_per_batch: 0,\n                priority_level: 0,\n                scheduling_access: false,\n                max_batches_per_day: 1\n            };\n        }\n        return {\n            max_accounts_per_batch: result.max_accounts_per_batch,\n            priority_level: result.priority_level,\n            scheduling_access: Boolean(result.scheduling_access),\n            max_batches_per_day: result.max_batches_per_day || 1\n        };\n    }\n    // Daily batch count check\n    getUserDailyBatchCount(userId, date = null) {\n        const targetDate = date || new Date().toISOString().split(\"T\")[0]; // YYYY-MM-DD format\n        const stmt = this.db.prepare(`\n      SELECT COUNT(*) as count\n      FROM queue_batches\n      WHERE user_id = ?\n      AND DATE(created_at) = ?\n    `);\n        const result = stmt.get(userId, targetDate);\n        return result.count;\n    }\n    // Weekly schedule count check\n    getUserWeeklyScheduleCount(userId) {\n        // Get the start of the current week (Monday)\n        const now = new Date();\n        const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, etc.\n        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // If Sunday, go back 6 days to Monday\n        const startOfWeek = new Date(now);\n        startOfWeek.setDate(now.getDate() - daysToMonday);\n        startOfWeek.setHours(0, 0, 0, 0);\n        const stmt = this.db.prepare(`\n      SELECT COUNT(*) as count\n      FROM queue_schedules\n      WHERE user_id = ?\n      AND created_at >= ?\n      AND status != 'cancelled'\n    `);\n        const result = stmt.get(userId, startOfWeek.toISOString());\n        return result.count;\n    }\n    // Queue Batch Methods\n    createQueueBatch(userId, batchName, accounts, scheduledTime = null, loginType = \"normal\", srpTarget = 100) {\n        const transaction = this.db.transaction(()=>{\n            // Get user's license features\n            const features = this.getUserLicenseFeatures(userId);\n            // Check daily batch limit\n            const dailyBatchCount = this.getUserDailyBatchCount(userId);\n            if (dailyBatchCount >= features.max_batches_per_day) {\n                throw new Error(`Daily batch limit reached (${features.max_batches_per_day} batches per day). Please try again tomorrow.`);\n            }\n            // Validate batch size against license limits\n            if (features.max_accounts_per_batch > 0 && accounts.length > features.max_accounts_per_batch) {\n                throw new Error(`Batch size (${accounts.length}) exceeds license limit (${features.max_accounts_per_batch})`);\n            }\n            // Validate scheduling access\n            if (scheduledTime && !features.scheduling_access) {\n                throw new Error(\"Scheduling access not available for this license\");\n            }\n            // Validate login type\n            if (![\n                \"normal\",\n                \"google\",\n                \"microsoft\"\n            ].includes(loginType)) {\n                throw new Error(\"Invalid login type specified\");\n            }\n            // Validate SRP target\n            if (srpTarget < 1 || srpTarget > 400) {\n                throw new Error(\"SRP target must be between 1 and 400\");\n            }\n            // Create batch\n            const batchStmt = this.db.prepare(`\n        INSERT INTO queue_batches (user_id, batch_name, login_type, total_accounts, priority_level, srp_target, scheduled_time)\n        VALUES (?, ?, ?, ?, ?, ?, ?)\n      `);\n            const batchResult = batchStmt.run(userId, batchName, loginType, accounts.length, features.priority_level, srpTarget, scheduledTime);\n            const batchId = batchResult.lastInsertRowid;\n            // Create individual jobs for each account\n            const jobStmt = this.db.prepare(`\n        INSERT INTO queue_jobs (batch_id, user_id, job_data, priority_level, effective_priority, srp_target, scheduled_time)\n        VALUES (?, ?, ?, ?, ?, ?, ?)\n      `);\n            accounts.forEach((account)=>{\n                const effectivePriority = this.calculateEffectivePriority(features.priority_level, scheduledTime);\n                jobStmt.run(batchId, userId, JSON.stringify(account), features.priority_level, effectivePriority, srpTarget, scheduledTime);\n            });\n            // Create schedule entry if scheduled\n            if (scheduledTime) {\n                this.createScheduleEntry(userId, scheduledTime, null, batchId, 30, srpTarget);\n            }\n            // Log activity\n            this.logActivity(userId, \"BATCH_CREATED\", `Created batch: ${batchName} with ${accounts.length} accounts`);\n            return batchId;\n        });\n        return transaction();\n    }\n    calculateEffectivePriority(basePriority, scheduledTime) {\n        let effectivePriority = basePriority;\n        // Boost priority for scheduled jobs approaching their time\n        if (scheduledTime) {\n            const now = new Date();\n            const scheduled = new Date(scheduledTime);\n            const timeDiff = scheduled.getTime() - now.getTime();\n            const hoursUntil = timeDiff / (1000 * 60 * 60);\n            if (hoursUntil <= 1) {\n                effectivePriority += 5; // High boost for jobs due within an hour\n            } else if (hoursUntil <= 6) {\n                effectivePriority += 2; // Medium boost for jobs due within 6 hours\n            }\n        }\n        // Apply starvation prevention (boost priority for old jobs)\n        // This would be implemented in a background process\n        return Math.min(effectivePriority, 10); // Cap at maximum priority\n    }\n    getQueueBatches(userId = null, status = null, limit = 50, offset = 0) {\n        let query = `\n      SELECT qb.*, u.username\n      FROM queue_batches qb\n      JOIN users u ON qb.user_id = u.id\n      WHERE 1=1\n    `;\n        const params = [];\n        if (userId) {\n            query += \" AND qb.user_id = ?\";\n            params.push(userId);\n        }\n        if (status) {\n            query += \" AND qb.status = ?\";\n            params.push(status);\n        }\n        query += \" ORDER BY qb.created_at DESC LIMIT ? OFFSET ?\";\n        params.push(limit, offset);\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    getBatchJobs(batchId) {\n        const stmt = this.db.prepare(`\n      SELECT * FROM queue_jobs \n      WHERE batch_id = ? \n      ORDER BY effective_priority DESC, created_at ASC\n    `);\n        return stmt.all(batchId);\n    }\n    updateBatchStatus(batchId, status, completedAt = null) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_batches\n      SET status = ?, completed_at = ?\n      WHERE id = ?\n    `);\n        return stmt.run(status, completedAt, batchId);\n    }\n    // Queue Job Methods\n    createQueueJob({ username, job_type = \"sparx_reader\", job_data, srp_target = 100, priority = 0, status = \"queued\" }) {\n        // Get user ID\n        const userStmt = this.db.prepare(\"SELECT id FROM users WHERE username = ?\");\n        const user = userStmt.get(username);\n        if (!user) {\n            throw new Error(\"User not found\");\n        }\n        // Create the job\n        const stmt = this.db.prepare(`\n      INSERT INTO queue_jobs (user_id, job_type, job_data, priority_level, effective_priority, srp_target, status)\n      VALUES (?, ?, ?, ?, ?, ?, ?)\n    `);\n        const result = stmt.run(user.id, job_type, job_data, priority, priority, srp_target, status);\n        return result.lastInsertRowid;\n    }\n    getQueueJobs(userId = null) {\n        let query = `\n      SELECT qj.*, qb.batch_name, u.username\n      FROM queue_jobs qj\n      LEFT JOIN queue_batches qb ON qj.batch_id = qb.id\n      JOIN users u ON qj.user_id = u.id\n    `;\n        const params = [];\n        if (userId) {\n            query += \" WHERE qj.user_id = ?\";\n            params.push(userId);\n        }\n        query += \" ORDER BY qj.created_at DESC\";\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    getNextQueueJob() {\n        const stmt = this.db.prepare(`\n      SELECT qj.*, qb.batch_name, u.username\n      FROM queue_jobs qj\n      LEFT JOIN queue_batches qb ON qj.batch_id = qb.id\n      JOIN users u ON qj.user_id = u.id\n      WHERE qj.status = 'queued'\n      AND (qj.scheduled_time IS NULL OR qj.scheduled_time <= datetime('now'))\n      ORDER BY qj.effective_priority DESC, qj.created_at ASC\n      LIMIT 1\n    `);\n        return stmt.get();\n    }\n    updateJobStatus(jobId, status, errorMessage = null, startedAt = null, completedAt = null) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET status = ?, error_message = ?, started_at = ?, completed_at = ?\n      WHERE id = ?\n    `);\n        return stmt.run(status, errorMessage, startedAt, completedAt, jobId);\n    }\n    incrementJobRetry(jobId) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET retry_count = retry_count + 1, status = 'queued'\n      WHERE id = ? AND retry_count < max_retries\n    `);\n        return stmt.run(jobId);\n    }\n    // Scheduling Methods\n    createScheduleEntry(userId, scheduledTime, jobId = null, batchId = null, durationMinutes = 30, srpTarget = 100) {\n        // Check for conflicts\n        const conflicts = this.checkScheduleConflicts(userId, scheduledTime, durationMinutes);\n        if (conflicts.length > 0) {\n            throw new Error(`Schedule conflict detected at ${scheduledTime}`);\n        }\n        const stmt = this.db.prepare(`\n      INSERT INTO queue_schedules (user_id, scheduled_time, duration_minutes, srp_target, job_id, batch_id)\n      VALUES (?, ?, ?, ?, ?, ?)\n    `);\n        return stmt.run(userId, scheduledTime, durationMinutes, srpTarget, jobId, batchId);\n    }\n    checkScheduleConflicts(userId, scheduledTime, durationMinutes) {\n        const startTime = new Date(scheduledTime);\n        const endTime = new Date(startTime.getTime() + durationMinutes * 60 * 1000);\n        const stmt = this.db.prepare(`\n      SELECT * FROM queue_schedules\n      WHERE user_id = ? \n      AND status IN ('scheduled', 'active')\n      AND (\n        (scheduled_time <= ? AND datetime(scheduled_time, '+' || duration_minutes || ' minutes') > ?) OR\n        (scheduled_time < ? AND datetime(scheduled_time, '+' || duration_minutes || ' minutes') >= ?)\n      )\n    `);\n        return stmt.all(userId, startTime.toISOString(), startTime.toISOString(), endTime.toISOString(), endTime.toISOString());\n    }\n    getUserSchedules(userId, startDate = null, endDate = null) {\n        let query = `\n      SELECT qs.*, qj.job_type, qb.batch_name\n      FROM queue_schedules qs\n      LEFT JOIN queue_jobs qj ON qs.job_id = qj.id\n      LEFT JOIN queue_batches qb ON qs.batch_id = qb.id\n      WHERE qs.user_id = ?\n    `;\n        const params = [\n            userId\n        ];\n        if (startDate) {\n            query += \" AND qs.scheduled_time >= ?\";\n            params.push(startDate);\n        }\n        if (endDate) {\n            query += \" AND qs.scheduled_time <= ?\";\n            params.push(endDate);\n        }\n        query += \" ORDER BY qs.scheduled_time ASC\";\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    // Priority Management Methods\n    updateJobPriority(jobId, newPriority, adminOverride = false) {\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET effective_priority = ?, priority_level = ?\n      WHERE id = ?\n    `);\n        const result = stmt.run(newPriority, adminOverride ? newPriority : null, jobId);\n        if (adminOverride) {\n            this.logActivity(null, \"ADMIN_PRIORITY_OVERRIDE\", `Job ${jobId} priority set to ${newPriority}`);\n        }\n        return result;\n    }\n    applyStarvationPrevention() {\n        // Boost priority for jobs that have been waiting too long\n        const stmt = this.db.prepare(`\n      UPDATE queue_jobs \n      SET effective_priority = CASE \n        WHEN datetime('now') > datetime(created_at, '+2 hours') THEN MIN(effective_priority + 2, 10)\n        WHEN datetime('now') > datetime(created_at, '+1 hour') THEN MIN(effective_priority + 1, 10)\n        ELSE effective_priority\n      END\n      WHERE status = 'queued'\n    `);\n        return stmt.run();\n    }\n    getQueueStats() {\n        const stats = {};\n        // Total jobs by status\n        const statusStmt = this.db.prepare(`\n      SELECT status, COUNT(*) as count \n      FROM queue_jobs \n      GROUP BY status\n    `);\n        stats.jobsByStatus = statusStmt.all();\n        // Jobs by priority level\n        const priorityStmt = this.db.prepare(`\n      SELECT effective_priority, COUNT(*) as count \n      FROM queue_jobs \n      WHERE status = 'queued'\n      GROUP BY effective_priority\n      ORDER BY effective_priority DESC\n    `);\n        stats.jobsByPriority = priorityStmt.all();\n        // Average wait time\n        const waitTimeStmt = this.db.prepare(`\n      SELECT AVG(julianday(started_at) - julianday(created_at)) * 24 * 60 as avg_wait_minutes\n      FROM queue_jobs \n      WHERE started_at IS NOT NULL\n    `);\n        stats.averageWaitTime = waitTimeStmt.get()?.avg_wait_minutes || 0;\n        return stats;\n    }\n    close() {\n        this.db.close();\n    }\n    // System Configuration Methods\n    getSystemConfig(key) {\n        const stmt = this.db.prepare(\"SELECT * FROM system_config WHERE config_key = ?\");\n        const config = stmt.get(key);\n        if (!config) return null;\n        // Parse value based on type\n        switch(config.config_type){\n            case \"number\":\n                return {\n                    ...config,\n                    config_value: parseInt(config.config_value)\n                };\n            case \"boolean\":\n                return {\n                    ...config,\n                    config_value: config.config_value === \"true\"\n                };\n            case \"json\":\n                return {\n                    ...config,\n                    config_value: JSON.parse(config.config_value)\n                };\n            default:\n                return config;\n        }\n    }\n    setSystemConfig(key, value, type = \"string\", description = null) {\n        const stringValue = type === \"json\" ? JSON.stringify(value) : value.toString();\n        const stmt = this.db.prepare(`\n      INSERT OR REPLACE INTO system_config (config_key, config_value, config_type, description, updated_at)\n      VALUES (?, ?, ?, COALESCE(?, (SELECT description FROM system_config WHERE config_key = ?)), CURRENT_TIMESTAMP)\n    `);\n        return stmt.run(key, stringValue, type, description, key);\n    }\n    getAllSystemConfig() {\n        const stmt = this.db.prepare(\"SELECT * FROM system_config ORDER BY config_key\");\n        const configs = stmt.all();\n        return configs.map((config)=>{\n            switch(config.config_type){\n                case \"number\":\n                    return {\n                        ...config,\n                        config_value: parseInt(config.config_value)\n                    };\n                case \"boolean\":\n                    return {\n                        ...config,\n                        config_value: config.config_value === \"true\"\n                    };\n                case \"json\":\n                    return {\n                        ...config,\n                        config_value: JSON.parse(config.config_value)\n                    };\n                default:\n                    return config;\n            }\n        });\n    }\n    // Dead Letter Queue Methods\n    addToDeadLetterQueue(originalJobId, jobType, jobData, userId, failureReason) {\n        const stmt = this.db.prepare(`\n      INSERT INTO dead_letter_queue (original_job_id, job_type, job_data, user_id, failure_reason)\n      VALUES (?, ?, ?, ?, ?)\n    `);\n        return stmt.run(originalJobId, jobType, JSON.stringify(jobData), userId, failureReason);\n    }\n    getDeadLetterQueueJobs(status = null, limit = 50, offset = 0) {\n        let query = `\n      SELECT dlq.*, u.username\n      FROM dead_letter_queue dlq\n      JOIN users u ON dlq.user_id = u.id\n    `;\n        const params = [];\n        if (status) {\n            query += \" WHERE dlq.status = ?\";\n            params.push(status);\n        }\n        query += \" ORDER BY dlq.created_at DESC LIMIT ? OFFSET ?\";\n        params.push(limit, offset);\n        const stmt = this.db.prepare(query);\n        return stmt.all(...params);\n    }\n    updateDeadLetterQueueStatus(id, status) {\n        const stmt = this.db.prepare(`\n      UPDATE dead_letter_queue\n      SET status = ?, updated_at = CURRENT_TIMESTAMP\n      WHERE id = ?\n    `);\n        return stmt.run(status, id);\n    }\n    retryDeadLetterQueueJob(id) {\n        const getStmt = this.db.prepare(\"SELECT * FROM dead_letter_queue WHERE id = ?\");\n        const job = getStmt.get(id);\n        if (!job) {\n            throw new Error(\"Dead letter queue job not found\");\n        }\n        // Create new job in queue_jobs using the original job data\n        const insertStmt = this.db.prepare(`\n      INSERT INTO queue_jobs (user_id, job_type, job_data, priority, effective_priority, srp_target, status)\n      VALUES (?, ?, ?, 0, 0, 100, 'queued')\n    `);\n        const result = insertStmt.run(job.user_id, job.job_type, job.job_data, 0, 0, 100);\n        // Update dead letter queue status\n        this.updateDeadLetterQueueStatus(id, \"resolved\");\n        return result.lastInsertRowid;\n    }\n}\n// Create data directory if it doesn't exist\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst dataDir = path.join(process.cwd(), \"data\");\nif (!fs.existsSync(dataDir)) {\n    fs.mkdirSync(dataDir, {\n        recursive: true\n    });\n}\n// Export singleton instance\nlet dbInstance = null;\nfunction getDatabase() {\n    if (!dbInstance) {\n        dbInstance = new DatabaseManager();\n    }\n    return dbInstance;\n}\nmodule.exports = {\n    getDatabase,\n    DatabaseManager\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/database.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/uuid","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute&page=%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsparxreader%2Fmicrosoft-start%2Froute.js&appDir=D%3A%5Creader-auto-main%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Creader-auto-main&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();