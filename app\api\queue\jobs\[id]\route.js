import { NextResponse } from 'next/server';
const { getDatabase } = require('../../../../../lib/database');
const { getAuthManager } = require('../../../../../lib/auth');

// Middleware wrapper for authentication
function withMiddleware(handler) {
  return async (request, context) => {
    try {
      const auth = getAuthManager();
      const authHeader = request.headers.get('authorization');

      if (!authHeader?.startsWith('Bearer ')) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const token = authHeader.substring(7);
      const session = auth.validateSession(token);

      const req = {
        ...request,
        body: await request.json().catch(() => ({})),
        user: {
          id: session.userId,
          username: session.username,
          role: session.role
        }
      };

      return handler(req, context);
    } catch (error) {
      console.error('Auth error:', error);
      return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
    }
  };
}

// GET - Get specific job details
export async function GET(request, { params }) {
  const handler = withMiddleware(async (req) => {
    try {
      const jobId = params.id;

      if (!jobId) {
        return NextResponse.json({
          error: 'Job ID is required'
        }, { status: 400 });
      }

      const db = getDatabase();
      
      // Get the specific job
      const jobStmt = db.db.prepare('SELECT * FROM queue_jobs WHERE id = ?');
      const job = jobStmt.get(parseInt(jobId));

      if (!job) {
        return NextResponse.json({
          error: 'Job not found'
        }, { status: 404 });
      }

      // Verify ownership (user can only check their own jobs unless admin)
      if (job.user_id !== req.user.id && req.user.role !== 'admin') {
        return NextResponse.json({
          error: 'Access denied'
        }, { status: 403 });
      }

      // Parse JSON fields
      const jobData = {
        ...job,
        job_data: job.job_data ? JSON.parse(job.job_data) : null,
        result: job.result ? JSON.parse(job.result) : null
      };

      return NextResponse.json({
        success: true,
        job: jobData
      });

    } catch (error) {
      console.error('Get job error:', error);
      return NextResponse.json({
        error: 'Failed to get job details',
        details: error.message
      }, { status: 500 });
    }
  });

  return handler(request);
}
