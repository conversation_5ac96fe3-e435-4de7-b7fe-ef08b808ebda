import { NextResponse } from 'next/server';
const { getDatabase } = require('../../../../lib/database');
const { getWebhookManager } = require('../../../../lib/webhook');
const { getAuthManager } = require('../../../../lib/auth');

// Middleware wrapper for Next.js API routes
function withMiddleware(handler) {
  return async (request, context) => {
    try {
      const auth = getAuthManager();
      
      // Get token from header
      const authHeader = request.headers.get('authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return NextResponse.json({ error: 'No token provided' }, { status: 401 });
      }

      const token = authHeader.substring(7);
      const session = auth.validateSession(token);
      
      if (!session) {
        return NextResponse.json({ error: 'Invalid session' }, { status: 401 });
      }

      const req = {
        ...request,
        body: await request.json().catch(() => ({})),
        user: session,
        url: request.url
      };

      return handler(req, context);
    } catch (error) {
      console.error('Admin auth error:', error);
      return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
    }
  };
}

// GET - Get dead letter queue jobs
export async function GET(request) {
  const handler = withMiddleware(async (req) => {
    try {
      // Check admin access
      if (req.user.role !== 'admin') {
        return NextResponse.json({
          error: 'Admin access required'
        }, { status: 403 });
      }

      const db = getDatabase();
      const url = new URL(request.url);
      const status = url.searchParams.get('status');
      const limit = parseInt(url.searchParams.get('limit')) || 50;
      const offset = parseInt(url.searchParams.get('offset')) || 0;

      const jobs = db.getDeadLetterQueueJobs(status, limit, offset);
      
      // Get total count for pagination
      let countQuery = 'SELECT COUNT(*) as total FROM dead_letter_queue';
      const countParams = [];
      
      if (status) {
        countQuery += ' WHERE status = ?';
        countParams.push(status);
      }
      
      const countStmt = db.db.prepare(countQuery);
      const { total } = countStmt.get(...countParams);

      return NextResponse.json({
        success: true,
        jobs: jobs.map(job => ({
          ...job,
          job_data: JSON.parse(job.job_data) // Parse job data for display
        })),
        pagination: {
          total,
          limit,
          offset,
          has_more: offset + limit < total
        }
      });

    } catch (error) {
      console.error('Error fetching dead letter queue:', error);
      return NextResponse.json({
        error: 'Failed to fetch dead letter queue jobs',
        details: error.message
      }, { status: 500 });
    }
  });

  return handler(request);
}

// POST - Manage dead letter queue jobs (retry, update status, etc.)
export async function POST(request) {
  const handler = withMiddleware(async (req) => {
    try {
      // Check admin access
      if (req.user.role !== 'admin') {
        return NextResponse.json({
          error: 'Admin access required'
        }, { status: 403 });
      }

      const db = getDatabase();
      const webhook = getWebhookManager();
      const { action, job_ids, new_status } = req.body;

      if (!action) {
        return NextResponse.json({
          error: 'action is required'
        }, { status: 400 });
      }

      if (!job_ids || !Array.isArray(job_ids) || job_ids.length === 0) {
        return NextResponse.json({
          error: 'job_ids array is required'
        }, { status: 400 });
      }

      let result = { success: true, affected_jobs: 0, results: [] };

      switch (action) {
        case 'retry':
          for (const jobId of job_ids) {
            try {
              const newJobId = db.retryDeadLetterQueueJob(jobId);
              result.results.push({
                dead_letter_job_id: jobId,
                new_job_id: newJobId,
                status: 'retried'
              });
              result.affected_jobs++;
            } catch (error) {
              result.results.push({
                dead_letter_job_id: jobId,
                status: 'error',
                error: error.message
              });
            }
          }
          break;

        case 'update_status':
          if (!new_status) {
            return NextResponse.json({
              error: 'new_status is required for update_status action'
            }, { status: 400 });
          }

          if (!['failed', 'investigating', 'resolved', 'discarded'].includes(new_status)) {
            return NextResponse.json({
              error: 'new_status must be one of: failed, investigating, resolved, discarded'
            }, { status: 400 });
          }

          for (const jobId of job_ids) {
            try {
              db.updateDeadLetterQueueStatus(jobId, new_status);
              result.results.push({
                dead_letter_job_id: jobId,
                status: 'updated',
                new_status: new_status
              });
              result.affected_jobs++;
            } catch (error) {
              result.results.push({
                dead_letter_job_id: jobId,
                status: 'error',
                error: error.message
              });
            }
          }
          break;

        case 'bulk_discard':
          for (const jobId of job_ids) {
            try {
              db.updateDeadLetterQueueStatus(jobId, 'discarded');
              result.results.push({
                dead_letter_job_id: jobId,
                status: 'discarded'
              });
              result.affected_jobs++;
            } catch (error) {
              result.results.push({
                dead_letter_job_id: jobId,
                status: 'error',
                error: error.message
              });
            }
          }
          break;

        default:
          return NextResponse.json({
            error: `Unknown action: ${action}`
          }, { status: 400 });
      }

      // Log admin action
      db.logActivity(
        req.user.id,
        'ADMIN_DEAD_LETTER_QUEUE',
        `Performed ${action} on ${result.affected_jobs} dead letter queue jobs`
      );

      // Send webhook notification for significant actions
      if (result.affected_jobs > 0) {
        await webhook.sendDiscordNotification(
          '🔄 Dead Letter Queue Action',
          `Admin **${req.user.username}** performed **${action}** on ${result.affected_jobs} failed jobs`,
          0xf39c12, // Orange color
          [
            { name: 'Action', value: action, inline: true },
            { name: 'Jobs Affected', value: result.affected_jobs.toString(), inline: true },
            { name: 'Admin', value: req.user.username, inline: true }
          ]
        );
      }

      return NextResponse.json(result);

    } catch (error) {
      console.error('Dead letter queue management error:', error);
      
      const webhook = getWebhookManager();
      await webhook.sendQueueAlert(
        'system_error',
        'Dead letter queue management failed',
        {
          admin_user: req.user?.username || 'unknown',
          action: req.body?.action || 'unknown',
          error: error.message
        }
      );

      return NextResponse.json({
        error: 'Dead letter queue management failed',
        details: error.message
      }, { status: 500 });
    }
  });

  return handler(request);
}
