// Global browser context storage using globalThis for persistence
if (!globalThis.sparxBrowserContext) {
  globalThis.sparxBrowserContext = {
    browser: null,
    page: null
  };
}

function setGlobalBrowser(browser, page) {
  globalThis.sparxBrowserContext.browser = browser;
  globalThis.sparxBrowserContext.page = page;
  console.log('Browser context set:', !!browser, !!page);
}

function getGlobalBrowser() {
  return globalThis.sparxBrowserContext.browser;
}

function getGlobalPage() {
  return globalThis.sparxBrowserContext.page;
}

function clearGlobalBrowser() {
  globalThis.sparxBrowserContext.browser = null;
  globalThis.sparxBrowserContext.page = null;
  console.log('Browser context cleared');
}

module.exports = {
  setGlobalBrowser,
  getGlobalBrowser,
  getGlobalPage,
  clearGlobalBrowser
};