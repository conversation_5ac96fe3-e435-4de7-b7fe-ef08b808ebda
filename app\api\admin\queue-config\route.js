import { NextResponse } from 'next/server';
const { getDatabase } = require('../../../../lib/database');
const { getQueueProcessor } = require('../../../../lib/queueProcessor');
const { getAuthManager } = require('../../../../lib/auth');

// Middleware wrapper for admin authentication
function withMiddleware(handler) {
  return async (request, context) => {
    try {
      const auth = getAuthManager();
      const authHeader = request.headers.get('authorization');

      if (!authHeader?.startsWith('Bearer ')) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      const token = authHeader.substring(7);
      const session = auth.validateSession(token);

      if (!session || session.role !== 'admin') {
        return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
      }

      const req = {
        ...request,
        body: await request.json().catch(() => ({})),
        user: {
          id: session.userId,
          username: session.username,
          role: session.role
        }
      };

      return handler(req, context);
    } catch (error) {
      console.error('Admin auth error:', error);
      return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
    }
  };
}

// GET - Get current queue configuration
export async function GET(request) {
  const handler = withMiddleware(async (req) => {
    try {
      // Check admin access
      if (req.user.role !== 'admin') {
        return NextResponse.json({
          error: 'Admin access required'
        }, { status: 403 });
      }

      const processor = getQueueProcessor();
      const status = processor.getStatus();

      return NextResponse.json({
        success: true,
        config: {
          maxConcurrentJobs: status.max_concurrent_jobs,
          browserTimeoutMinutes: 9, // Default timeout
          currentlyProcessing: status.currently_processing_count,
          isProcessing: status.is_processing
        }
      });

    } catch (error) {
      console.error('Get queue config error:', error);
      return NextResponse.json({
        error: 'Failed to get queue configuration',
        details: error.message
      }, { status: 500 });
    }
  });

  return handler(request);
}

// POST - Update queue configuration
export async function POST(request) {
  const handler = withMiddleware(async (req) => {
    try {
      // Check admin access
      if (req.user.role !== 'admin') {
        return NextResponse.json({
          error: 'Admin access required'
        }, { status: 403 });
      }

      const { maxConcurrentJobs, browserTimeoutMinutes } = req.body;

      // Validate inputs
      if (maxConcurrentJobs !== undefined) {
        if (maxConcurrentJobs < 1 || maxConcurrentJobs > 10) {
          return NextResponse.json({
            error: 'maxConcurrentJobs must be between 1 and 10'
          }, { status: 400 });
        }

        // Update queue processor
        const processor = getQueueProcessor();
        processor.setMaxConcurrentJobs(maxConcurrentJobs);
      }

      if (browserTimeoutMinutes !== undefined) {
        if (browserTimeoutMinutes < 1 || browserTimeoutMinutes > 30) {
          return NextResponse.json({
            error: 'browserTimeoutMinutes must be between 1 and 30'
          }, { status: 400 });
        }

        // Update browser timeout (we'll implement this in the queue processor)
        const processor = getQueueProcessor();
        processor.setBrowserTimeout(browserTimeoutMinutes);
      }

      // Log admin action
      const db = getDatabase();
      db.logActivity(
        req.user.id,
        'ADMIN_QUEUE_CONFIG',
        `Updated queue config: maxConcurrent=${maxConcurrentJobs}, timeout=${browserTimeoutMinutes}m`
      );

      return NextResponse.json({
        success: true,
        message: 'Queue configuration updated successfully',
        config: {
          maxConcurrentJobs,
          browserTimeoutMinutes
        }
      });

    } catch (error) {
      console.error('Update queue config error:', error);
      return NextResponse.json({
        error: 'Failed to update queue configuration',
        details: error.message
      }, { status: 500 });
    }
  });

  return handler(request);
}
